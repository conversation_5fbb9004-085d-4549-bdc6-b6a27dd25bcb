<?php
/**
 * Blog Writer Database Handler
 * 
 * Extends the base DatabaseHandler to include Blog Writer specific tables
 */

namespace BlogWriter;

use VisionFramework\Core\DatabaseHandler as BaseHandler;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BlogWriterDatabaseHandler Class
 */
class BlogWriterDatabaseHandler extends BaseHandler
{
    /**
     * Initialize table names with Blog Writer specific tables
     */
    protected function initTables()
    {
        // Call parent to initialize base tables
        parent::initTables();
        
        // Replace hyphens with underscores to ensure valid MySQL table names
        $prefix = str_replace('-', '_', $this->config['text_domain']);
        
        // Add Blog Writer specific tables
        $this->tables['pages'] = $this->wpdb->prefix . $prefix . '_pages';
        $this->tables['articles'] = $this->wpdb->prefix . $prefix . '_articles';
        $this->tables['blog_settings'] = $this->wpdb->prefix . $prefix . '_blog_settings';
        $this->tables['mou'] = $this->wpdb->prefix . $prefix . '_mou';
        $this->tables['page_balance'] = $this->wpdb->prefix . $prefix . '_page_balance';
        $this->tables['image_placeholders'] = $this->wpdb->prefix . $prefix . '_image_placeholders';
        $this->tables['ai_providers'] = $this->wpdb->prefix . $prefix . '_ai_providers';
        
        // Clear any cached table existence information
        $this->clearTableCache();
    }

    /**
     * Clear table cache
     */
    protected function clearTableCache()
    {
        $this->tableExistsCache = [];
    }

    /**
     * Get table name by key
     *
     * @param string $table_key Table key
     * @return string|null Table name or null if not found
     */
    public function getTableName($table_key)
    {
        return $this->tables[$table_key] ?? null;
    }

    /**
     * Create plugin tables including Blog Writer specific ones
     */
    public function createTables()
    {
        // Create base framework tables first
        parent::createTables();
        
        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        
        $charset_collate = $this->wpdb->get_charset_collate();
        
        // Create pages table for keyword storage
        $pages_sql = "CREATE TABLE {$this->tables['pages']} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            page_id bigint(20) unsigned NOT NULL,
            page_title varchar(255) NOT NULL,
            page_url varchar(500) NOT NULL,
            page_status varchar(20) NOT NULL DEFAULT 'publish',
            keywords longtext DEFAULT NULL,
            last_scan_date datetime DEFAULT NULL,
            scan_model varchar(50) DEFAULT NULL,
            scan_settings longtext DEFAULT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY page_id (page_id),
            KEY page_status (page_status),
            KEY last_scan_date (last_scan_date),
            KEY scan_model (scan_model)
        ) $charset_collate;";
        
        dbDelta($pages_sql);
        
        // Create articles table for generated content tracking
        $articles_sql = "CREATE TABLE {$this->tables['articles']} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            post_id bigint(20) unsigned DEFAULT NULL,
            user_id bigint(20) unsigned NOT NULL,
            title varchar(255) NOT NULL,
            slug varchar(200) DEFAULT NULL,
            prompt_data longtext DEFAULT NULL,
            generation_models json DEFAULT NULL,
            internal_links json DEFAULT NULL,
            image_data json DEFAULT NULL,
            word_count int unsigned DEFAULT 0,
            generation_date datetime DEFAULT NULL,
            status varchar(20) NOT NULL DEFAULT 'draft',
            ai_model_used varchar(50) DEFAULT NULL,
            generation_time_seconds int unsigned DEFAULT NULL,
            seo_data json DEFAULT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY post_id (post_id),
            KEY user_id (user_id),
            KEY status (status),
            KEY generation_date (generation_date),
            KEY ai_model_used (ai_model_used)
        ) $charset_collate;";
        
        dbDelta($articles_sql);
        
        // Create blog settings table for complex configuration
        $blog_settings_sql = "CREATE TABLE {$this->tables['blog_settings']} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            setting_key varchar(100) NOT NULL,
            setting_value longtext NOT NULL,
            setting_type varchar(20) NOT NULL DEFAULT 'string',
            setting_group varchar(50) NOT NULL DEFAULT 'general',
            is_encrypted tinyint(1) NOT NULL DEFAULT 0,
            autoload varchar(10) NOT NULL DEFAULT 'yes',
            description text DEFAULT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY setting_key (setting_key),
            KEY setting_group (setting_group),
            KEY autoload (autoload)
        ) $charset_collate;";
        
        dbDelta($blog_settings_sql);
        
        // Create MoU (Memorandum of Understanding) table
        $mou_sql = "CREATE TABLE {$this->tables['mou']} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            content text NOT NULL,
            version varchar(20) NOT NULL DEFAULT '1.0',
            generated_by varchar(50) DEFAULT NULL,
            generation_prompt longtext DEFAULT NULL,
            last_updated datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_active tinyint(1) NOT NULL DEFAULT 1,
            word_count int unsigned DEFAULT 0,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY is_active (is_active),
            KEY version (version)
        ) $charset_collate;";
        
        dbDelta($mou_sql);
        
        // Create page balance tracking table
        $page_balance_sql = "CREATE TABLE {$this->tables['page_balance']} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            page_id bigint(20) unsigned NOT NULL,
            page_title varchar(255) NOT NULL,
            page_type varchar(50) NOT NULL DEFAULT 'page',
            service_category varchar(100) DEFAULT NULL,
            article_count int unsigned NOT NULL DEFAULT 0,
            last_article_date datetime DEFAULT NULL,
            target_article_count int unsigned NOT NULL DEFAULT 5,
            priority_score decimal(5,2) NOT NULL DEFAULT 0.00,
            is_active tinyint(1) NOT NULL DEFAULT 1,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY page_id (page_id),
            KEY service_category (service_category),
            KEY priority_score (priority_score),
            KEY is_active (is_active)
        ) $charset_collate;";
        
        dbDelta($page_balance_sql);
        
        // Create image placeholders table
        $image_placeholders_sql = "CREATE TABLE {$this->tables['image_placeholders']} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            article_id bigint(20) unsigned NOT NULL,
            placeholder_text varchar(500) NOT NULL,
            ai_prompt longtext NOT NULL,
            generated_images json DEFAULT NULL,
            selected_image_url varchar(500) DEFAULT NULL,
            selected_provider varchar(50) DEFAULT NULL,
            generation_status varchar(20) NOT NULL DEFAULT 'pending',
            wp_attachment_id bigint(20) unsigned DEFAULT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY article_id (article_id),
            KEY generation_status (generation_status),
            KEY selected_provider (selected_provider)
        ) $charset_collate;";
        
        dbDelta($image_placeholders_sql);
        
        // Create AI providers configuration table
        $ai_providers_sql = "CREATE TABLE {$this->tables['ai_providers']} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            provider_key varchar(50) NOT NULL,
            provider_name varchar(100) NOT NULL,
            provider_type varchar(50) NOT NULL DEFAULT 'text',
            api_key varchar(500) DEFAULT NULL,
            is_enabled tinyint(1) NOT NULL DEFAULT 0,
            is_default tinyint(1) NOT NULL DEFAULT 0,
            rate_limit_per_minute int unsigned DEFAULT 60,
            rate_limit_per_hour int unsigned DEFAULT 3600,
            configuration json DEFAULT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY provider_key (provider_key),
            KEY provider_type (provider_type),
            KEY is_enabled (is_enabled),
            KEY is_default (is_default)
        ) $charset_collate;";
        
        dbDelta($ai_providers_sql);
        
        // Insert default settings
        $this->insertDefaultSettings();
        
        // Insert default AI providers
        $this->insertDefaultProviders();
        
        // Insert default MoU if none exists
        $this->insertDefaultMoU();
        
        // Update database version
        update_option($this->config['text_domain'] . '_db_version', $this->config['db_version']);
    }
    
    /**
     * Insert default Blog Writer settings
     */
    protected function insertDefaultSettings()
    {
        $default_settings = [
            [
                'setting_key' => 'openrouter_api_key',
                'setting_value' => '',
                'setting_type' => 'string',
                'setting_group' => 'api',
                'is_encrypted' => 1,
                'description' => 'OpenRouter API key for AI model access'
            ],
            [
                'setting_key' => 'default_ai_model',
                'setting_value' => 'gpt-4',
                'setting_type' => 'string',
                'setting_group' => 'ai',
                'description' => 'Default AI model for article generation'
            ],
            [
                'setting_key' => 'default_word_count',
                'setting_value' => '1000',
                'setting_type' => 'integer',
                'setting_group' => 'content',
                'description' => 'Default word count for generated articles'
            ],
            [
                'setting_key' => 'assigned_user_id',
                'setting_value' => '0',
                'setting_type' => 'integer',
                'setting_group' => 'general',
                'description' => 'User ID assigned to own generated articles'
            ],
            [
                'setting_key' => 'main_content_prompt',
                'setting_value' => 'Write a comprehensive, SEO-optimized blog post about [TOPIC]. Include relevant headings, engaging content, and ensure it provides value to readers. Target word count: [WORD_COUNT] words.',
                'setting_type' => 'text',
                'setting_group' => 'prompts',
                'description' => 'Main prompt template for article generation'
            ],
            [
                'setting_key' => 'image_generation_prompt',
                'setting_value' => 'Create a professional, relevant image for a blog post about [TOPIC]. Style should be modern and engaging.',
                'setting_type' => 'text',
                'setting_group' => 'prompts',
                'description' => 'Default prompt for AI image generation'
            ],
            [
                'setting_key' => 'seo_optimization_prompt',
                'setting_value' => 'Focus on relevant keywords, proper heading structure, and content that provides genuine value to readers searching for information about [TOPIC].',
                'setting_type' => 'text',
                'setting_group' => 'prompts',
                'description' => 'SEO optimization guidelines for content generation'
            ],
            [
                'setting_key' => 'auto_extract_keywords',
                'setting_value' => '1',
                'setting_type' => 'boolean',
                'setting_group' => 'seo',
                'description' => 'Automatically extract keywords from new pages'
            ],
            [
                'setting_key' => 'max_internal_links',
                'setting_value' => '5',
                'setting_type' => 'integer',
                'setting_group' => 'seo',
                'description' => 'Maximum number of internal links per article'
            ],
            [
                'setting_key' => 'keyword_extraction_model',
                'setting_value' => 'gpt-3.5-turbo',
                'setting_type' => 'string',
                'setting_group' => 'ai',
                'description' => 'AI model used for keyword extraction'
            ]
        ];
        
        foreach ($default_settings as $setting) {
            $existing = $this->wpdb->get_var(
                $this->wpdb->prepare(
                    "SELECT id FROM {$this->tables['blog_settings']} WHERE setting_key = %s",
                    $setting['setting_key']
                )
            );
            
            if (!$existing) {
                $this->wpdb->insert(
                    $this->tables['blog_settings'],
                    $setting,
                    ['%s', '%s', '%s', '%s', '%d', '%s']
                );
            }
        }
    }
    
    /**
     * Get Blog Writer setting value
     * 
     * @param string $key Setting key
     * @param mixed $default Default value if setting not found
     * @return mixed
     */
    public function getBlogSetting($key, $default = null)
    {
        if (!$this->tableExists('blog_settings')) {
            return $default;
        }
        
        $setting = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT setting_value, setting_type, is_encrypted FROM {$this->tables['blog_settings']} WHERE setting_key = %s",
                $key
            )
        );
        
        if (!$setting) {
            return $default;
        }
        
        $value = $setting->setting_value;
        
        // Decrypt if encrypted
        if ($setting->is_encrypted) {
            $value = $this->decryptValue($value);
        }
        
        // Cast to appropriate type
        switch ($setting->setting_type) {
            case 'integer':
                return (int) $value;
            case 'boolean':
                return (bool) $value;
            case 'array':
            case 'object':
                return json_decode($value, true);
            default:
                return $value;
        }
    }
    
    /**
     * Set Blog Writer setting value
     * 
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @param string $type Value type
     * @param string $group Setting group
     * @param bool $encrypt Whether to encrypt the value
     * @return bool
     */
    public function setBlogSetting($key, $value, $type = 'string', $group = 'general', $encrypt = false)
    {
        if (!$this->tableExists('blog_settings')) {
            $this->createTables();
        }
        
        // Prepare value based on type
        switch ($type) {
            case 'array':
            case 'object':
                $value = wp_json_encode($value);
                break;
            case 'boolean':
                $value = $value ? '1' : '0';
                break;
            default:
                $value = (string) $value;
        }
        
        // Encrypt if requested
        if ($encrypt) {
            $value = $this->encryptValue($value);
        }
        
        $data = [
            'setting_value' => $value,
            'setting_type' => $type,
            'setting_group' => $group,
            'is_encrypted' => $encrypt ? 1 : 0,
            'updated_at' => current_time('mysql')
        ];
        
        $existing = $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SELECT id FROM {$this->tables['blog_settings']} WHERE setting_key = %s",
                $key
            )
        );
        
        if ($existing) {
            return $this->wpdb->update(
                $this->tables['blog_settings'],
                $data,
                ['setting_key' => $key],
                ['%s', '%s', '%s', '%d', '%s'],
                ['%s']
            );
        } else {
            $data['setting_key'] = $key;
            return $this->wpdb->insert(
                $this->tables['blog_settings'],
                $data,
                ['%s', '%s', '%s', '%s', '%d', '%s']
            );
        }
    }
    
    /**
     * Simple encryption for sensitive data
     * 
     * @param string $value Value to encrypt
     * @return string
     */
    protected function encryptValue($value)
    {
        if (!defined('AUTH_KEY') || empty(AUTH_KEY)) {
            return base64_encode($value); // Fallback to basic encoding
        }
        
        $key = substr(hash('sha256', AUTH_KEY), 0, 32);
        $iv = openssl_random_pseudo_bytes(16);
        $encrypted = openssl_encrypt($value, 'AES-256-CBC', $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * Simple decryption for sensitive data
     * 
     * @param string $encrypted Encrypted value
     * @return string
     */
    protected function decryptValue($encrypted)
    {
        if (!defined('AUTH_KEY') || empty(AUTH_KEY)) {
            return base64_decode($encrypted); // Fallback for basic encoding
        }
        
        $data = base64_decode($encrypted);
        if (strlen($data) < 16) {
            return $encrypted; // Not encrypted
        }
        
        $key = substr(hash('sha256', AUTH_KEY), 0, 32);
        $iv = substr($data, 0, 16);
        $encrypted_data = substr($data, 16);
        
        return openssl_decrypt($encrypted_data, 'AES-256-CBC', $key, 0, $iv);
    }
    
    /**
     * Get page data with keywords
     * 
     * @param int $page_id WordPress page ID
     * @return array|null
     */
    public function getPageData($page_id)
    {
        if (!$this->tableExists('pages')) {
            return null;
        }
        
        return $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->tables['pages']} WHERE page_id = %d",
                $page_id
            ),
            ARRAY_A
        );
    }
    
    /**
     * Save page data with keywords
     * 
     * @param int $page_id WordPress page ID
     * @param array $data Page data
     * @return bool|int
     */
    public function savePageData($page_id, $data)
    {
        if (!$this->tableExists('pages')) {
            $this->createTables();
        }
        
        $existing = $this->getPageData($page_id);
        
        $page_data = [
            'page_id' => $page_id,
            'page_title' => sanitize_text_field($data['title'] ?? ''),
            'page_url' => esc_url_raw($data['url'] ?? ''),
            'page_status' => sanitize_key($data['status'] ?? 'publish'),
            'keywords' => sanitize_textarea_field($data['keywords'] ?? ''),
            'last_scan_date' => current_time('mysql'),
            'scan_model' => sanitize_text_field($data['scan_model'] ?? ''),
            'scan_settings' => !empty($data['scan_settings']) ? wp_json_encode($data['scan_settings']) : null,
            'updated_at' => current_time('mysql')
        ];
        
        if ($existing) {
            return $this->wpdb->update(
                $this->tables['pages'],
                $page_data,
                ['page_id' => $page_id],
                ['%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s'],
                ['%d']
            );
        } else {
            $page_data['created_at'] = current_time('mysql');
            return $this->wpdb->insert(
                $this->tables['pages'],
                $page_data,
                ['%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s']
            );
        }
    }
    
    /**
     * Get all pages with keyword data
     * 
     * @param array $args Query arguments
     * @return array
     */
    public function getAllPages($args = [])
    {
        if (!$this->tableExists('pages')) {
            return [];
        }
        
        $defaults = [
            'status' => '',
            'limit' => 0,
            'offset' => 0,
            'order' => 'ASC',
            'orderby' => 'page_title'
        ];
        
        $args = wp_parse_args($args, $defaults);
        
        $sql = "SELECT * FROM {$this->tables['pages']} WHERE 1=1";
        $params = [];
        
        if (!empty($args['status'])) {
            $sql .= " AND page_status = %s";
            $params[] = $args['status'];
        }
        
        $orderby = in_array($args['orderby'], ['page_title', 'last_scan_date', 'created_at']) ? $args['orderby'] : 'page_title';
        $order = in_array(strtoupper($args['order']), ['ASC', 'DESC']) ? strtoupper($args['order']) : 'ASC';
        $sql .= " ORDER BY {$orderby} {$order}";
        
        if ($args['limit'] > 0) {
            $sql .= " LIMIT %d";
            $params[] = $args['limit'];
            
            if ($args['offset'] > 0) {
                $sql .= " OFFSET %d";
                $params[] = $args['offset'];
            }
        }
        
        if (!empty($params)) {
            $sql = $this->wpdb->prepare($sql, $params);
        }
        
        return $this->wpdb->get_results($sql, ARRAY_A);
    }
    
    /**
     * Save generated article data
     * 
     * @param array $data Article data
     * @return bool|int
     */
    public function saveArticleData($data)
    {
        if (!$this->tableExists('articles')) {
            $this->createTables();
        }
        
        $article_data = [
            'post_id' => !empty($data['post_id']) ? absint($data['post_id']) : null,
            'user_id' => absint($data['user_id']),
            'title' => sanitize_text_field($data['title']),
            'slug' => sanitize_title($data['slug'] ?? ''),
            'prompt_data' => !empty($data['prompt_data']) ? wp_json_encode($data['prompt_data']) : null,
            'generation_models' => !empty($data['generation_models']) ? wp_json_encode($data['generation_models']) : null,
            'internal_links' => !empty($data['internal_links']) ? wp_json_encode($data['internal_links']) : null,
            'image_data' => !empty($data['image_data']) ? wp_json_encode($data['image_data']) : null,
            'word_count' => absint($data['word_count'] ?? 0),
            'generation_date' => current_time('mysql'),
            'status' => sanitize_key($data['status'] ?? 'draft'),
            'ai_model_used' => sanitize_text_field($data['ai_model_used'] ?? ''),
            'generation_time_seconds' => absint($data['generation_time_seconds'] ?? 0),
            'seo_data' => !empty($data['seo_data']) ? wp_json_encode($data['seo_data']) : null
        ];
        
        return $this->wpdb->insert(
            $this->tables['articles'],
            $article_data,
            ['%d', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%d', '%s']
        );
    }
    
    /**
     * Get generated articles
     * 
     * @param array $args Query arguments
     * @return array
     */
    public function getArticles($args = [])
    {
        if (!$this->tableExists('articles')) {
            return [];
        }
        
        $defaults = [
            'status' => '',
            'user_id' => 0,
            'limit' => 50,
            'offset' => 0,
            'order' => 'DESC',
            'orderby' => 'generation_date'
        ];
        
        $args = wp_parse_args($args, $defaults);
        
        $sql = "SELECT * FROM {$this->tables['articles']} WHERE 1=1";
        $params = [];
        
        if (!empty($args['status'])) {
            $sql .= " AND status = %s";
            $params[] = $args['status'];
        }
        
        if (!empty($args['user_id'])) {
            $sql .= " AND user_id = %d";
            $params[] = $args['user_id'];
        }
        
        $orderby = in_array($args['orderby'], ['title', 'generation_date', 'word_count']) ? $args['orderby'] : 'generation_date';
        $order = in_array(strtoupper($args['order']), ['ASC', 'DESC']) ? strtoupper($args['order']) : 'DESC';
        $sql .= " ORDER BY {$orderby} {$order}";
        
        if ($args['limit'] > 0) {
            $sql .= " LIMIT %d";
            $params[] = $args['limit'];
            
            if ($args['offset'] > 0) {
                $sql .= " OFFSET %d";
                $params[] = $args['offset'];
            }
        }
        
        if (!empty($params)) {
            $sql = $this->wpdb->prepare($sql, $params);
        }
        
        return $this->wpdb->get_results($sql, ARRAY_A);
    }
    
    /**
     * Clean up old tables with Blog Writer specific cleanup
     */
    protected function cleanupOldTables()
    {
        parent::cleanupOldTables();
        
        // Also clean up any potential old Blog Writer tables
        $old_tables = [
            $this->wpdb->prefix . 'blog-writer_pages',
            $this->wpdb->prefix . 'blog-writer_articles',
            $this->wpdb->prefix . 'blog-writer_blog_settings'
        ];
        
        foreach ($old_tables as $table) {
            $this->wpdb->query("DROP TABLE IF EXISTS `{$table}`");
        }
    }
    
    /**
     * Insert default AI providers
     */
    protected function insertDefaultProviders()
    {
        $default_providers = [
            [
                'provider_key' => 'openrouter',
                'provider_name' => 'OpenRouter',
                'provider_type' => 'text',
                'is_enabled' => 1,
                'is_default' => 1,
                'rate_limit_per_minute' => 60,
                'rate_limit_per_hour' => 3600,
                'configuration' => wp_json_encode([
                    'base_url' => 'https://openrouter.ai/api/v1',
                    'models' => ['gpt-4', 'gpt-3.5-turbo', 'claude-3-opus', 'claude-3-sonnet']
                ])
            ],
            [
                'provider_key' => 'openai',
                'provider_name' => 'OpenAI',
                'provider_type' => 'text',
                'is_enabled' => 0,
                'is_default' => 0,
                'rate_limit_per_minute' => 50,
                'rate_limit_per_hour' => 3000,
                'configuration' => wp_json_encode([
                    'base_url' => 'https://api.openai.com/v1',
                    'models' => ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo']
                ])
            ],
            [
                'provider_key' => 'anthropic',
                'provider_name' => 'Anthropic',
                'provider_type' => 'text',
                'is_enabled' => 0,
                'is_default' => 0,
                'rate_limit_per_minute' => 40,
                'rate_limit_per_hour' => 2400,
                'configuration' => wp_json_encode([
                    'base_url' => 'https://api.anthropic.com/v1',
                    'models' => ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku']
                ])
            ],
            [
                'provider_key' => 'grok',
                'provider_name' => 'Grok (X.AI)',
                'provider_type' => 'text',
                'is_enabled' => 0,
                'is_default' => 0,
                'rate_limit_per_minute' => 30,
                'rate_limit_per_hour' => 1800,
                'configuration' => wp_json_encode([
                    'base_url' => 'https://api.x.ai/v1',
                    'models' => ['grok-beta', 'grok-vision-beta']
                ])
            ],
            [
                'provider_key' => 'google',
                'provider_name' => 'Google Gemini',
                'provider_type' => 'text',
                'is_enabled' => 0,
                'is_default' => 0,
                'rate_limit_per_minute' => 60,
                'rate_limit_per_hour' => 3600,
                'configuration' => wp_json_encode([
                    'base_url' => 'https://generativelanguage.googleapis.com/v1beta',
                    'models' => ['gemini-pro', 'gemini-pro-vision']
                ])
            ],
            [
                'provider_key' => 'groq',
                'provider_name' => 'Groq',
                'provider_type' => 'text',
                'is_enabled' => 0,
                'is_default' => 0,
                'rate_limit_per_minute' => 30,
                'rate_limit_per_hour' => 1800,
                'configuration' => wp_json_encode([
                    'base_url' => 'https://api.groq.com/openai/v1',
                    'models' => ['llama2-70b-4096', 'mixtral-8x7b-32768', 'gemma-7b-it']
                ])
            ],
            [
                'provider_key' => 'dalle',
                'provider_name' => 'DALL-E (OpenAI)',
                'provider_type' => 'image',
                'is_enabled' => 0,
                'is_default' => 1,
                'rate_limit_per_minute' => 20,
                'rate_limit_per_hour' => 1200,
                'configuration' => wp_json_encode([
                    'base_url' => 'https://api.openai.com/v1',
                    'models' => ['dall-e-3', 'dall-e-2']
                ])
            ],
            [
                'provider_key' => 'stable_diffusion',
                'provider_name' => 'Stable Diffusion',
                'provider_type' => 'image',
                'is_enabled' => 0,
                'is_default' => 0,
                'rate_limit_per_minute' => 15,
                'rate_limit_per_hour' => 900,
                'configuration' => wp_json_encode([
                    'base_url' => 'https://api.stability.ai/v1',
                    'models' => ['stable-diffusion-xl-base-1.0', 'stable-diffusion-v1-6']
                ])
            ]
        ];
        
        foreach ($default_providers as $provider) {
            $existing = $this->wpdb->get_var(
                $this->wpdb->prepare(
                    "SELECT id FROM {$this->tables['ai_providers']} WHERE provider_key = %s",
                    $provider['provider_key']
                )
            );
            
            if (!$existing) {
                $this->wpdb->insert(
                    $this->tables['ai_providers'],
                    $provider,
                    ['%s', '%s', '%s', '%s', '%d', '%d', '%d', '%d', '%s']
                );
            }
        }
    }
    
    /**
     * Insert default MoU if none exists
     */
    protected function insertDefaultMoU()
    {
        $existing = $this->wpdb->get_var(
            "SELECT id FROM {$this->tables['mou']} WHERE is_active = 1 LIMIT 1"
        );
        
        if (!$existing) {
            $default_mou = [
                'content' => 'Your website provides professional services to customers. Please customize this Memorandum of Understanding to describe your business, services, target market, and location. This information will be used to generate relevant, SEO-optimized content that aligns with your business goals.',
                'version' => '1.0',
                'generated_by' => 'default',
                'word_count' => 35,
                'is_active' => 1
            ];
            
            $this->wpdb->insert(
                $this->tables['mou'],
                $default_mou,
                ['%s', '%s', '%s', '%d', '%d']
            );
        }
    }
    
    /**
     * Get active MoU content
     * 
     * @return string|null
     */
    public function getActiveMoU()
    {
        if (!$this->tableExists('mou')) {
            return null;
        }
        
        return $this->wpdb->get_var(
            "SELECT content FROM {$this->tables['mou']} WHERE is_active = 1 ORDER BY last_updated DESC LIMIT 1"
        );
    }
    
    /**
     * Save MoU content
     * 
     * @param string $content MoU content
     * @param string $generated_by Who/what generated this MoU
     * @param string $prompt Optional generation prompt
     * @return bool|int
     */
    public function saveMoU($content, $generated_by = 'user', $prompt = null)
    {
        if (!$this->tableExists('mou')) {
            $this->createTables();
        }
        
        // Deactivate existing MoUs
        $this->wpdb->update(
            $this->tables['mou'],
            ['is_active' => 0],
            ['is_active' => 1],
            ['%d'],
            ['%d']
        );
        
        // Insert new MoU
        $data = [
            'content' => sanitize_textarea_field($content),
            'version' => '1.0',
            'generated_by' => sanitize_text_field($generated_by),
            'generation_prompt' => $prompt ? sanitize_textarea_field($prompt) : null,
            'word_count' => str_word_count(strip_tags($content)),
            'is_active' => 1
        ];
        
        return $this->wpdb->insert(
            $this->tables['mou'],
            $data,
            ['%s', '%s', '%s', '%s', '%d', '%d']
        );
    }
    
    /**
     * Get AI provider configuration
     * 
     * @param string $provider_key Provider key
     * @return array|null
     */
    public function getAiProvider($provider_key)
    {
        if (!$this->tableExists('ai_providers')) {
            return null;
        }
        
        return $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->tables['ai_providers']} WHERE provider_key = %s",
                $provider_key
            ),
            ARRAY_A
        );
    }
    
    /**
     * Get all enabled AI providers by type
     * 
     * @param string $type Provider type ('text' or 'image')
     * @return array
     */
    public function getEnabledProviders($type = 'text')
    {
        if (!$this->tableExists('ai_providers')) {
            return [];
        }
        
        return $this->wpdb->get_results(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->tables['ai_providers']} WHERE provider_type = %s AND is_enabled = 1 ORDER BY is_default DESC, provider_name ASC",
                $type
            ),
            ARRAY_A
        );
    }

    /**
     * Update page balance data
     *
     * @param int $page_id WordPress page ID
     * @param array $data Balance data
     * @return bool
     */
    public function updatePageBalance($page_id, $data = [])
    {
        if (!$this->tableExists('page_balance')) {
            $this->createTables();
        }

        $page = get_post($page_id);
        if (!$page || $page->post_type !== 'page') {
            return false;
        }

        // Get current article count for this page
        $article_count = $this->getPageArticleCount($page_id);

        $balance_data = [
            'page_title' => $page->post_title,
            'page_type' => 'page',
            'service_category' => $data['service_category'] ?? $this->detectServiceCategory($page->post_title),
            'article_count' => $article_count,
            'last_article_date' => $this->getLastArticleDate($page_id),
            'target_article_count' => $data['target_article_count'] ?? 5,
            'priority_score' => $this->calculatePriorityScore($page_id, $article_count),
            'is_active' => $data['is_active'] ?? 1,
            'updated_at' => current_time('mysql')
        ];

        $existing = $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SELECT id FROM {$this->tables['page_balance']} WHERE page_id = %d",
                $page_id
            )
        );

        if ($existing) {
            return $this->wpdb->update(
                $this->tables['page_balance'],
                $balance_data,
                ['page_id' => $page_id],
                ['%s', '%s', '%s', '%d', '%s', '%d', '%f', '%d', '%s'],
                ['%d']
            );
        } else {
            $balance_data['page_id'] = $page_id;
            return $this->wpdb->insert(
                $this->tables['page_balance'],
                $balance_data,
                ['%d', '%s', '%s', '%s', '%d', '%s', '%d', '%f', '%d', '%s']
            );
        }
    }

    /**
     * Get page article count
     *
     * @param int $page_id WordPress page ID
     * @return int
     */
    protected function getPageArticleCount($page_id)
    {
        if (!$this->tableExists('articles')) {
            return 0;
        }

        // Count articles that link to this page
        // Note: JSON_CONTAINS can be slow on large datasets. Consider adding a separate
        // article_page_links table for better performance if this becomes a bottleneck.
        $count = $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->tables['articles']}
                 WHERE JSON_CONTAINS(internal_links, JSON_OBJECT('page_id', %d))",
                $page_id
            )
        );

        return (int) $count;
    }

    /**
     * Get last article date for a page
     *
     * @param int $page_id WordPress page ID
     * @return string|null
     */
    protected function getLastArticleDate($page_id)
    {
        if (!$this->tableExists('articles')) {
            return null;
        }

        $last_date = $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SELECT MAX(generation_date) FROM {$this->tables['articles']}
                 WHERE JSON_CONTAINS(internal_links, JSON_OBJECT('page_id', %d))",
                $page_id
            )
        );

        return $last_date;
    }

    /**
     * Detect service category from page title
     *
     * @param string $title Page title
     * @return string
     */
    protected function detectServiceCategory($title)
    {
        $title_lower = strtolower($title);

        // Commercial keywords
        if (preg_match('/\b(commercial|business|office|industrial|corporate)\b/', $title_lower)) {
            return 'commercial';
        }

        // Emergency keywords
        if (preg_match('/\b(emergency|urgent|24\/7|24 hour|immediate)\b/', $title_lower)) {
            return 'emergency';
        }

        // Maintenance keywords
        if (preg_match('/\b(maintenance|repair|service|cleaning|inspection)\b/', $title_lower)) {
            return 'maintenance';
        }

        // Installation keywords
        if (preg_match('/\b(installation|install|new|replacement)\b/', $title_lower)) {
            return 'installation';
        }

        return 'general';
    }

    /**
     * Calculate priority score for a page
     *
     * @param int $page_id WordPress page ID
     * @param int $article_count Current article count
     * @return float
     */
    protected function calculatePriorityScore($page_id, $article_count)
    {
        $score = 0;

        // Base score inversely related to article count
        $score += max(0, 100 - ($article_count * 10));

        // Boost for pages with no articles
        if ($article_count === 0) {
            $score += 50;
        }

        // Boost for pages with keywords
        $page_data = $this->getPageData($page_id);
        if ($page_data && !empty($page_data['keywords'])) {
            $score += 25;
        }

        // Boost for service pages (detected by category)
        $page = get_post($page_id);
        if ($page) {
            $category = $this->detectServiceCategory($page->post_title);
            if ($category !== 'general') {
                $score += 15;
            }
        }

        return min($score, 100.0);
    }

    /**
     * Get page balance data for all pages
     *
     * @param array $options Query options
     * @return array
     */
    public function getPageBalanceData($options = [])
    {
        if (!$this->tableExists('page_balance')) {
            return [];
        }

        $where_clauses = ['is_active = 1'];
        $order_by = $options['order_by'] ?? 'priority_score DESC';
        $limit = $options['limit'] ?? 0;

        if (!empty($options['service_category'])) {
            $where_clauses[] = $this->wpdb->prepare('service_category = %s', $options['service_category']);
        }

        if (!empty($options['needs_attention'])) {
            $where_clauses[] = 'article_count < target_article_count';
        }

        $where_sql = implode(' AND ', $where_clauses);
        $limit_sql = $limit > 0 ? "LIMIT {$limit}" : '';

        $results = $this->wpdb->get_results(
            "SELECT * FROM {$this->tables['page_balance']}
             WHERE {$where_sql}
             ORDER BY {$order_by}
             {$limit_sql}",
            ARRAY_A
        );

        return $results;
    }

    /**
     * Get page balance summary statistics (optimized)
     *
     * @return array
     */
    public function getPageBalanceSummary()
    {
        if (!$this->tableExists('page_balance')) {
            return [
                'total_pages' => 0,
                'pages_covered' => 0,
                'needs_attention' => 0,
                'content_balance' => 0,
                'categories' => []
            ];
        }

        // Single optimized query for main statistics
        $summary_stats = $this->wpdb->get_row(
            "SELECT
                COUNT(*) as total_pages,
                COUNT(CASE WHEN article_count > 0 THEN 1 END) as pages_covered,
                COUNT(CASE WHEN article_count < target_article_count THEN 1 END) as needs_attention,
                SUM(article_count) as total_articles,
                SUM(target_article_count) as target_articles
             FROM {$this->tables['page_balance']}
             WHERE is_active = 1",
            ARRAY_A
        );

        $content_balance = $summary_stats['target_articles'] > 0 ?
            round(($summary_stats['total_articles'] / $summary_stats['target_articles']) * 100) : 0;

        // Get category breakdown
        $categories = $this->wpdb->get_results(
            "SELECT service_category, COUNT(*) as page_count, SUM(article_count) as total_articles, AVG(article_count) as avg_articles
             FROM {$this->tables['page_balance']}
             WHERE is_active = 1
             GROUP BY service_category
             ORDER BY total_articles DESC",
            ARRAY_A
        );

        return [
            'total_pages' => (int) $summary_stats['total_pages'],
            'pages_covered' => (int) $summary_stats['pages_covered'],
            'needs_attention' => (int) $summary_stats['needs_attention'],
            'content_balance' => (int) $content_balance,
            'total_articles' => (int) $summary_stats['total_articles'],
            'target_articles' => (int) $summary_stats['target_articles'],
            'categories' => $categories
        ];
    }

    /**
     * Get pages that need attention (underrepresented)
     *
     * @param int $limit Number of pages to return
     * @return array
     */
    public function getPagesNeedingAttention($limit = 10)
    {
        return $this->getPageBalanceData([
            'needs_attention' => true,
            'order_by' => 'priority_score DESC, article_count ASC',
            'limit' => $limit
        ]);
    }

    /**
     * Refresh page balance for all published pages
     *
     * @return int Number of pages updated
     */
    public function refreshAllPageBalance()
    {
        $pages = get_pages(['post_status' => 'publish']);
        $updated_count = 0;

        foreach ($pages as $page) {
            if ($this->updatePageBalance($page->ID)) {
                $updated_count++;
            }
        }

        return $updated_count;
    }

    /**
     * Get page balance insights and recommendations
     *
     * @return array
     */
    public function getPageBalanceInsights()
    {
        $summary = $this->getPageBalanceSummary();
        $insights = [];

        // Category-based insights
        foreach ($summary['categories'] as $category) {
            $category_name = ucfirst($category['service_category']);
            $article_count = (int) $category['total_articles'];
            $page_count = (int) $category['page_count'];
            $avg_articles = round($category['avg_articles'], 1);

            if ($article_count === 0) {
                $insights[] = [
                    'type' => 'warning',
                    'message' => sprintf('%s services have no articles (%d pages)', $category_name, $page_count),
                    'action' => 'generate_for_category',
                    'category' => $category['service_category']
                ];
            } elseif ($avg_articles < 2) {
                $insights[] = [
                    'type' => 'warning',
                    'message' => sprintf('%s services need more content (%d articles, %s avg)', $category_name, $article_count, $avg_articles),
                    'action' => 'generate_for_category',
                    'category' => $category['service_category']
                ];
            } else {
                $insights[] = [
                    'type' => 'success',
                    'message' => sprintf('%s services are well-covered (%d articles)', $category_name, $article_count),
                    'action' => null,
                    'category' => $category['service_category']
                ];
            }
        }

        // Overall balance insights
        if ($summary['content_balance'] < 50) {
            $insights[] = [
                'type' => 'error',
                'message' => 'Overall content balance is low. Focus on generating more articles.',
                'action' => 'generate_bulk',
                'category' => null
            ];
        } elseif ($summary['content_balance'] < 80) {
            $insights[] = [
                'type' => 'warning',
                'message' => 'Content balance is moderate. Consider targeting underrepresented pages.',
                'action' => 'generate_targeted',
                'category' => null
            ];
        }

        return $insights;
    }
}