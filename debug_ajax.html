<!DOCTYPE html>
<html>
<head>
    <title>AJAX Debug Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Blog Writer AJAX Debug Test</h1>
    
    <div id="results"></div>
    
    <script>
    // Test direct AJAX call to WordPress
    function testAjax() {
        console.log('🧪 Testing direct AJAX call...');
        
        // You'll need to update these values from your WordPress admin
        const testData = {
            action: 'blog-writer_ajax',
            endpoint: 'test_api_provider',
            provider: 'openrouter',
            api_key: 'test-key',
            nonce: 'YOUR_NONCE_HERE' // Get this from WordPress admin
        };
        
        $.ajax({
            url: '/wp-admin/admin-ajax.php', // Update this to your WordPress site URL
            type: 'POST',
            data: testData,
            dataType: 'json'
        })
        .done(function(response) {
            console.log('✅ AJAX Success:', response);
            $('#results').html('<div style="color: green;">SUCCESS: ' + JSON.stringify(response, null, 2) + '</div>');
        })
        .fail(function(xhr, status, error) {
            console.error('❌ AJAX Failed:', {xhr, status, error});
            $('#results').html('<div style="color: red;">ERROR: ' + status + ' - ' + error + '<br>Response: ' + xhr.responseText + '</div>');
        });
    }
    
    // Auto-run test
    $(document).ready(function() {
        console.log('🚀 Debug page loaded');
        // testAjax(); // Uncomment to auto-run
    });
    </script>
    
    <button onclick="testAjax()">Test AJAX</button>
    
    <h2>Instructions:</h2>
    <ol>
        <li>Update the WordPress site URL in the script</li>
        <li>Get the correct nonce from WordPress admin console</li>
        <li>Click "Test AJAX" to verify the connection</li>
    </ol>
</body>
</html>