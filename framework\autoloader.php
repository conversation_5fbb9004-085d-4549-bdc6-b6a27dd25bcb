<?php
/**
 * Autoloader for Vision Framework
 * 
 * Handles automatic loading of framework and plugin classes
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Framework Autoloader Class
 */
class VisionFrameworkAutoloader
{
    /**
     * Registered namespaces and their paths
     * 
     * @var array
     */
    private static $namespaces = [];
    
    /**
     * Initialize the autoloader
     */
    public static function init()
    {
        // Get the plugin directory - works for any plugin using this framework
        $pluginDir = defined('BLOG_WRITER_PLUGIN_DIR') ? BLOG_WRITER_PLUGIN_DIR : 
                    (defined('SAMPLE_PLUGIN_PLUGIN_DIR') ? SAMPLE_PLUGIN_PLUGIN_DIR : 
                    plugin_dir_path(__FILE__) . '../');
        
        // Register framework namespace
        self::addNamespace('VisionFramework', $pluginDir . 'framework/src');
        
        // Register plugin namespaces based on which constants are defined
        if (defined('BLOG_WRITER_PLUGIN_DIR')) {
            self::addNamespace('BlogWriter', <PERSON><PERSON><PERSON><PERSON>_WRITER_PLUGIN_DIR . 'src');
        }
        
        // Register the autoloader
        spl_autoload_register([__CLASS__, 'autoload']);
    }
    
    /**
     * Add a namespace to the autoloader
     * 
     * @param string $namespace The namespace
     * @param string $path The base path for the namespace
     */
    public static function addNamespace($namespace, $path)
    {
        self::$namespaces[$namespace] = rtrim($path, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR;
    }
    
    /**
     * Autoload a class
     * 
     * @param string $className The fully qualified class name
     */
    public static function autoload($className)
    {
        // Split the class name into namespace and class
        $parts = explode('\\', $className);
        
        if (count($parts) < 2) {
            return;
        }
        
        $namespace = $parts[0];
        
        // Check if we handle this namespace
        if (!isset(self::$namespaces[$namespace])) {
            return;
        }
        
        // Build the file path
        $relativePath = implode(DIRECTORY_SEPARATOR, array_slice($parts, 1)) . '.php';
        $fullPath = self::$namespaces[$namespace] . $relativePath;
        
        // Load the file if it exists
        if (file_exists($fullPath)) {
            require_once $fullPath;
        }
    }
}

// Initialize the autoloader
VisionFrameworkAutoloader::init();