<?php
/**
 * Plugin Installer
 * 
 * Handles plugin activation, deactivation, and uninstall processes
 */

namespace BlogWriter;

use VisionFramework\Core\DatabaseHandler;
use BlogWriter\BlogWriterDatabaseHandler;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Installer Class
 */
class Installer
{
    /**
     * Plugin activation
     */
    public static function activate()
    {
        // Check WordPress version compatibility
        if (!self::checkWordPressVersion()) {
            deactivate_plugins(BLOG_WRITER_PLUGIN_BASENAME);
            wp_die(
                __('This plugin requires WordPress 5.0 or higher.', BLOG_WRITER_TEXT_DOMAIN),
                __('Plugin Activation Error', BLOG_WRITER_TEXT_DOMAIN),
                ['back_link' => true]
            );
        }
        
        // Check PHP version compatibility
        if (!self::checkPHPVersion()) {
            deactivate_plugins(BLOG_WRITER_PLUGIN_BASENAME);
            wp_die(
                __('This plugin requires PHP 7.4 or higher.', BLOG_WRITER_TEXT_DOMAIN),
                __('Plugin Activation Error', BLOG_WRITER_TEXT_DOMAIN),
                ['back_link' => true]
            );
        }
        
        // Clean up any old tables and create new ones with proper names
        self::clearDatabaseCache();
        $config = [
            'text_domain' => BLOG_WRITER_TEXT_DOMAIN,
            'db_version' => '1.0.0'
        ];
        
        $db = new DatabaseHandler($config);
        $db->forceRecreate();
        
        // Set default options
        self::setDefaultOptions();
        
        // Create necessary directories
        self::createDirectories();
        
        // Set activation flag
        update_option(BLOG_WRITER_TEXT_DOMAIN . '_activated', time());
        
        // Clear any existing caches
        self::clearCaches();
        
        // Log activation
        if (class_exists('BlogWriter\\BlogWriter')) {
            $plugin = BlogWriter::instance();
            $db = $plugin->getDatabaseHandler();
            if ($db) {
                $db->log('info', 'Plugin activated', [
                    'version' => BLOG_WRITER_VERSION,
                    'user_id' => get_current_user_id(),
                    'timestamp' => current_time('mysql')
                ]);
            }
        }
        
        // Schedule activation notice
        set_transient(BLOG_WRITER_TEXT_DOMAIN . '_activation_notice', true, 60);
    }
    
    /**
     * Plugin deactivation
     */
    public static function deactivate()
    {
        // Clear scheduled events
        wp_clear_scheduled_hook(BLOG_WRITER_TEXT_DOMAIN . '_daily_cleanup');
        wp_clear_scheduled_hook(BLOG_WRITER_TEXT_DOMAIN . '_weekly_maintenance');
        
        // Clear caches
        self::clearCaches();
        
        // Log deactivation
        if (class_exists('BlogWriter\\BlogWriter')) {
            $plugin = BlogWriter::instance();
            $db = $plugin->getDatabaseHandler();
            if ($db) {
                $db->log('info', 'Plugin deactivated', [
                    'version' => BLOG_WRITER_VERSION,
                    'user_id' => get_current_user_id(),
                    'timestamp' => current_time('mysql')
                ]);
            }
        }
        
        // Remove activation flag
        delete_option(BLOG_WRITER_TEXT_DOMAIN . '_activated');
        
        // Schedule deactivation notice
        set_transient(BLOG_WRITER_TEXT_DOMAIN . '_deactivation_notice', true, 60);
    }
    
    /**
     * Plugin uninstall
     */
    public static function uninstall()
    {
        // Check if user has permission to delete plugins
        if (!current_user_can('delete_plugins')) {
            return;
        }
        
        // Double-check uninstall request
        if (!defined('WP_UNINSTALL_PLUGIN') && !defined('WP_CLI')) {
            return;
        }
        
        // Drop database tables
        self::dropTables();
        
        // Remove all options
        self::removeOptions();
        
        // Remove user meta
        self::removeUserMeta();
        
        // Remove uploaded files and directories
        self::removeDirectories();
        
        // Clear all caches
        self::clearCaches();
        
        // Remove any scheduled events
        wp_clear_scheduled_hook(BLOG_WRITER_TEXT_DOMAIN . '_daily_cleanup');
        wp_clear_scheduled_hook(BLOG_WRITER_TEXT_DOMAIN . '_weekly_maintenance');
    }
    
    /**
     * Check WordPress version compatibility
     * 
     * @return bool
     */
    protected static function checkWordPressVersion()
    {
        global $wp_version;
        return version_compare($wp_version, '5.0', '>=');
    }
    
    /**
     * Check PHP version compatibility
     * 
     * @return bool
     */
    protected static function checkPHPVersion()
    {
        return version_compare(PHP_VERSION, '7.4', '>=');
    }
    
    /**
     * Create database tables
     */
    protected static function createTables()
    {
        $config = [
            'text_domain' => BLOG_WRITER_TEXT_DOMAIN,
            'db_version' => '1.0.0'
        ];
        
        $db = new BlogWriterDatabaseHandler($config);
        $db->createTables();
    }
    
    /**
     * Drop database tables
     */
    protected static function dropTables()
    {
        $config = [
            'text_domain' => BLOG_WRITER_TEXT_DOMAIN,
            'db_version' => '1.0.0'
        ];
        
        $db = new BlogWriterDatabaseHandler($config);
        $db->dropTables();
        
        // Also clean up any old tables with hyphens
        self::cleanupOldTables();
    }
    
    /**
     * Clear database cache to ensure clean state
     */
    protected static function clearDatabaseCache()
    {
        // Clear WordPress object cache
        wp_cache_flush();
        
        // Clear table existence cache specifically
        wp_cache_flush_group('table_exists');
        
        // Clear any database-related transients
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_%table%' OR option_name LIKE '_transient_timeout_%table%'");
        
        // Force WordPress to refresh its internal table cache
        $wpdb->tables = null;
    }
    
    /**
     * Clean up old tables with invalid hyphen names
     */
    protected static function cleanupOldTables()
    {
        global $wpdb;
        
        // List of old table names with hyphens that need to be removed
        $old_tables = [
            $wpdb->prefix . 'sample-plugin_logs',
            $wpdb->prefix . 'sample-plugin_settings', 
            $wpdb->prefix . 'sample-plugin_data'
        ];
        
        foreach ($old_tables as $table) {
            // Use backticks to handle hyphenated table names in DROP statement
            $wpdb->query("DROP TABLE IF EXISTS `{$table}`");
        }
        
        // Also try to drop any other variations that might exist
        $variations = [
            $wpdb->prefix . 'sample_plugin_logs',
            $wpdb->prefix . 'sample_plugin_settings',
            $wpdb->prefix . 'sample_plugin_data'
        ];
        
        foreach ($variations as $table) {
            $wpdb->query("DROP TABLE IF EXISTS `{$table}`");
        }
        
        // Clear any cached table information
        wp_cache_flush();
    }
    
    /**
     * Set default plugin options
     */
    protected static function setDefaultOptions()
    {
        $defaults = [
            BLOG_WRITER_TEXT_DOMAIN . '_version' => BLOG_WRITER_VERSION,
            BLOG_WRITER_TEXT_DOMAIN . '_db_version' => '1.0.0',
            BLOG_WRITER_TEXT_DOMAIN . '_settings' => [
                'openrouter_api_key' => '',
                'default_ai_model' => 'gpt-4',
                'default_word_count' => 1000,
                'assigned_user_id' => 0,
                'auto_extract_keywords' => true,
                'max_internal_links' => 5,
                'theme_mode' => 'auto'
            ],
            BLOG_WRITER_TEXT_DOMAIN . '_first_install' => current_time('mysql'),
            BLOG_WRITER_TEXT_DOMAIN . '_last_update' => current_time('mysql')
        ];
        
        foreach ($defaults as $option => $value) {
            if (!get_option($option)) {
                add_option($option, $value);
            }
        }
    }
    
    /**
     * Remove all plugin options
     */
    protected static function removeOptions()
    {
        global $wpdb;
        
        // Remove options with our plugin prefix
        $prefix = BLOG_WRITER_TEXT_DOMAIN . '_';
        
        $wpdb->query($wpdb->prepare("
            DELETE FROM {$wpdb->options} 
            WHERE option_name LIKE %s
        ", $prefix . '%'));
        
        // Remove transients
        $wpdb->query($wpdb->prepare("
            DELETE FROM {$wpdb->options} 
            WHERE option_name LIKE %s 
            OR option_name LIKE %s
        ", 
            '_transient_' . $prefix . '%',
            '_transient_timeout_' . $prefix . '%'
        ));
    }
    
    /**
     * Remove user meta data
     */
    protected static function removeUserMeta()
    {
        global $wpdb;
        
        $prefix = BLOG_WRITER_TEXT_DOMAIN . '_';
        
        $wpdb->query($wpdb->prepare("
            DELETE FROM {$wpdb->usermeta} 
            WHERE meta_key LIKE %s
        ", $prefix . '%'));
    }
    
    /**
     * Create necessary directories
     */
    protected static function createDirectories()
    {
        $upload_dir = wp_upload_dir();
        $plugin_dir = $upload_dir['basedir'] . '/' . BLOG_WRITER_TEXT_DOMAIN;
        
        if (!file_exists($plugin_dir)) {
            wp_mkdir_p($plugin_dir);
            
            // Create index.php to prevent directory browsing
            $index_content = "<?php\n// Silence is golden.";
            file_put_contents($plugin_dir . '/index.php', $index_content);
        }
        
        // Create subdirectories
        $subdirs = ['logs', 'cache', 'temp', 'exports'];
        
        foreach ($subdirs as $subdir) {
            $dir_path = $plugin_dir . '/' . $subdir;
            if (!file_exists($dir_path)) {
                wp_mkdir_p($dir_path);
                file_put_contents($dir_path . '/index.php', $index_content);
            }
        }
        
        // Create .htaccess for security
        $htaccess_content = "# Deny direct access\n<Files *.log>\n    Order allow,deny\n    Deny from all\n</Files>";
        file_put_contents($plugin_dir . '/.htaccess', $htaccess_content);
    }
    
    /**
     * Remove plugin directories and files
     */
    protected static function removeDirectories()
    {
        $upload_dir = wp_upload_dir();
        $plugin_dir = $upload_dir['basedir'] . '/' . BLOG_WRITER_TEXT_DOMAIN;
        
        if (file_exists($plugin_dir)) {
            self::removeDirectory($plugin_dir);
        }
    }
    
    /**
     * Recursively remove directory
     * 
     * @param string $dir Directory path
     */
    protected static function removeDirectory($dir)
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            
            if (is_dir($path)) {
                self::removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        
        rmdir($dir);
    }
    
    /**
     * Clear all caches
     */
    protected static function clearCaches()
    {
        // Clear WordPress object cache
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // Clear popular caching plugins
        if (function_exists('w3tc_flush_all')) {
            w3tc_flush_all();
        }
        
        if (function_exists('wp_cache_clear_cache')) {
            wp_cache_clear_cache();
        }
        
        if (function_exists('rocket_clean_domain')) {
            rocket_clean_domain();
        }
        
        if (class_exists('LiteSpeed_Cache_API')) {
            LiteSpeed_Cache_API::purge_all();
        }
    }
    
    /**
     * Schedule initial tasks
     */
    protected static function scheduleEvents()
    {
        // Schedule daily cleanup
        if (!wp_next_scheduled(BLOG_WRITER_TEXT_DOMAIN . '_daily_cleanup')) {
            wp_schedule_event(time(), 'daily', BLOG_WRITER_TEXT_DOMAIN . '_daily_cleanup');
        }
        
        // Schedule weekly maintenance
        if (!wp_next_scheduled(BLOG_WRITER_TEXT_DOMAIN . '_weekly_maintenance')) {
            wp_schedule_event(time(), 'weekly', BLOG_WRITER_TEXT_DOMAIN . '_weekly_maintenance');
        }
    }
    
    /**
     * Check if plugin needs update
     * 
     * @return bool
     */
    public static function needsUpdate()
    {
        $current_version = get_option(BLOG_WRITER_TEXT_DOMAIN . '_version', '0.0.0');
        return version_compare($current_version, BLOG_WRITER_VERSION, '<');
    }
    
    /**
     * Update plugin
     */
    public static function update()
    {
        $current_version = get_option(BLOG_WRITER_TEXT_DOMAIN . '_version', '0.0.0');
        
        if (version_compare($current_version, BLOG_WRITER_VERSION, '<')) {
            // Update database if needed
            $current_db_version = get_option(BLOG_WRITER_TEXT_DOMAIN . '_db_version', '0.0.0');
            if (version_compare($current_db_version, '1.0.0', '<')) {
                self::createTables();
            }
            
            // Update version numbers
            update_option(BLOG_WRITER_TEXT_DOMAIN . '_version', BLOG_WRITER_VERSION);
            update_option(BLOG_WRITER_TEXT_DOMAIN . '_last_update', current_time('mysql'));
            
            // Clear caches after update
            self::clearCaches();
            
            // Log update
            if (class_exists('BlogWriter\\BlogWriter')) {
                $plugin = BlogWriter::instance();
                $db = $plugin->getDatabaseHandler();
                if ($db) {
                    $db->log('info', 'Plugin updated', [
                        'from_version' => $current_version,
                        'to_version' => BLOG_WRITER_VERSION,
                        'user_id' => get_current_user_id(),
                        'timestamp' => current_time('mysql')
                    ]);
                }
            }
        }
    }
}