<?php
/**
 * API Handler for AJAX Requests
 * 
 * Handles all AJAX requests with security and validation
 */

namespace VisionFramework\Core;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ApiHandler Class
 */
class ApiHandler
{
    /**
     * Plugin configuration
     * 
     * @var array
     */
    protected $config;
    
    /**
     * Registered endpoints
     * 
     * @var array
     */
    protected $endpoints = [];
    
    /**
     * Constructor
     * 
     * @param array $config Plugin configuration
     */
    public function __construct($config)
    {
        $this->config = $config;
        $this->registerDefaultEndpoints();
    }
    
    /**
     * Register default endpoints
     */
    protected function registerDefaultEndpoints()
    {
        $this->registerEndpoint('refresh_data', [$this, 'handleRefreshData']);
        $this->registerEndpoint('clear_cache', [$this, 'handleClearCache']);
        $this->registerEndpoint('clear_logs', [$this, 'handleClearLogs']);
        $this->registerEndpoint('start_scan', [$this, 'handleStartScan']);
        $this->registerEndpoint('save_settings', [$this, 'handleSaveSettings']);
        $this->registerEndpoint('switch_tab', [$this, 'handleSwitchTab']);
    }
    
    /**
     * Register an API endpoint
     * 
     * @param string $action Endpoint action
     * @param callable $callback Callback function
     */
    public function registerEndpoint($action, $callback)
    {
        $this->endpoints[$action] = $callback;
    }
    
    /**
     * Handle AJAX request
     */
    public function handleRequest()
    {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', $this->config['text_domain'] . '_nonce')) {
            wp_die(__('Security check failed.', $this->config['text_domain']));
        }
        
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', $this->config['text_domain']));
        }
        
        // Get the action
        $action = sanitize_text_field($_POST['endpoint'] ?? '');
        
        if (empty($action) || !isset($this->endpoints[$action])) {
            wp_send_json_error(__('Invalid action.', $this->config['text_domain']));
        }
        
        try {
            // Call the endpoint handler
            $result = call_user_func($this->endpoints[$action], $_POST);
            
            if ($result === false) {
                wp_send_json_error(__('Operation failed.', $this->config['text_domain']));
            }
            
            wp_send_json_success($result);
            
        } catch (Exception $e) {
            error_log('Vision Framework API Error: ' . $e->getMessage());
            wp_send_json_error(__('An error occurred. Please try again.', $this->config['text_domain']));
        }
    }
    
    /**
     * Handle refresh data request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleRefreshData($data)
    {
        // Simulate data refresh
        return [
            'message' => __('Data refreshed successfully.', $this->config['text_domain']),
            'timestamp' => current_time('mysql'),
            'stats' => [
                'total_items' => rand(10, 100),
                'active_items' => rand(5, 50),
                'last_update' => current_time('mysql')
            ]
        ];
    }
    
    /**
     * Handle clear cache request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleClearCache($data)
    {
        // Clear WordPress object cache if available
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // Clear plugin-specific transients
        $this->clearPluginTransients();
        
        return [
            'message' => __('Cache cleared successfully.', $this->config['text_domain']),
            'timestamp' => current_time('mysql')
        ];
    }
    
    /**
     * Handle clear logs request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleClearLogs($data)
    {
        global $wpdb;
        
        // Clear plugin log entries (if using custom table)
        $table_name = $wpdb->prefix . $this->config['text_domain'] . '_logs';
        
        if ($wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table_name)) === $table_name) {
            $wpdb->query("TRUNCATE TABLE {$table_name}");
        }
        
        return [
            'message' => __('Logs cleared successfully.', $this->config['text_domain']),
            'timestamp' => current_time('mysql')
        ];
    }
    
    /**
     * Handle start scan request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleStartScan($data)
    {
        // This would typically start a background process
        // For demo purposes, we'll simulate a scan result
        
        $scan_results = [
            'status' => 'completed',
            'started_at' => current_time('mysql'),
            'completed_at' => current_time('mysql'),
            'total_checked' => rand(50, 200),
            'issues_found' => rand(0, 5),
            'details' => [
                'files_scanned' => rand(20, 100),
                'security_checks' => rand(10, 50),
                'performance_issues' => rand(0, 3)
            ]
        ];
        
        // Store scan results
        update_option($this->config['text_domain'] . '_last_scan', $scan_results);
        
        return [
            'message' => __('Scan completed successfully.', $this->config['text_domain']),
            'results' => $scan_results
        ];
    }
    
    /**
     * Handle save settings request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleSaveSettings($data)
    {
        // Extract and sanitize settings
        $settings = [];
        
        if (isset($data['enable_feature'])) {
            $settings['enable_feature'] = (bool) $data['enable_feature'];
        }
        
        if (isset($data['api_key'])) {
            $settings['api_key'] = sanitize_text_field($data['api_key']);
        }
        
        if (isset($data['theme_mode'])) {
            $settings['theme_mode'] = sanitize_text_field($data['theme_mode']);
        }
        
        // Validate settings
        $validated_settings = $this->validateSettings($settings);
        
        if ($validated_settings === false) {
            return false;
        }
        
        // Save settings
        update_option($this->config['text_domain'] . '_settings', $validated_settings);
        
        return [
            'message' => __('Settings saved successfully.', $this->config['text_domain']),
            'settings' => $validated_settings
        ];
    }
    
    /**
     * Handle switch tab request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleSwitchTab($data)
    {
        $tab = sanitize_key($data['tab'] ?? '');
        
        if (empty($tab)) {
            return false;
        }
        
        // You could load tab-specific data here
        $tab_data = $this->getTabData($tab);
        
        return [
            'tab' => $tab,
            'data' => $tab_data,
            'message' => sprintf(__('Switched to %s tab.', $this->config['text_domain']), $tab)
        ];
    }
    
    /**
     * Validate settings
     * 
     * @param array $settings Settings to validate
     * @return array|false
     */
    protected function validateSettings($settings)
    {
        $validated = [];
        
        // Validate enable_feature
        if (isset($settings['enable_feature'])) {
            $validated['enable_feature'] = (bool) $settings['enable_feature'];
        }
        
        // Validate API key
        if (isset($settings['api_key'])) {
            $api_key = trim($settings['api_key']);
            if (empty($api_key) || strlen($api_key) < 10) {
                // API key validation failed - could be optional
                $validated['api_key'] = '';
            } else {
                $validated['api_key'] = $api_key;
            }
        }
        
        // Validate theme mode
        if (isset($settings['theme_mode'])) {
            $valid_themes = ['auto', 'light', 'dark'];
            if (in_array($settings['theme_mode'], $valid_themes)) {
                $validated['theme_mode'] = $settings['theme_mode'];
            } else {
                $validated['theme_mode'] = 'auto';
            }
        }
        
        return $validated;
    }
    
    /**
     * Get tab-specific data
     * 
     * @param string $tab Tab name
     * @return array
     */
    protected function getTabData($tab)
    {
        switch ($tab) {
            case 'dashboard':
                return [
                    'stats' => [
                        'active_features' => 3,
                        'total_scans' => get_option($this->config['text_domain'] . '_total_scans', 0),
                        'last_activity' => current_time('mysql')
                    ]
                ];
                
            case 'logs':
                return [
                    'recent_logs' => $this->getRecentLogs(),
                    'log_count' => $this->getLogCount()
                ];
                
            case 'scan':
                return [
                    'last_scan' => get_option($this->config['text_domain'] . '_last_scan', null),
                    'scan_history' => $this->getScanHistory()
                ];
                
            case 'settings':
                return [
                    'current_settings' => get_option($this->config['text_domain'] . '_settings', [])
                ];
                
            default:
                return [];
        }
    }
    
    /**
     * Clear plugin-specific transients
     */
    protected function clearPluginTransients()
    {
        global $wpdb;
        
        // Delete transients with our plugin prefix
        $prefix = $this->config['text_domain'] . '_';
        
        $wpdb->query($wpdb->prepare("
            DELETE FROM {$wpdb->options} 
            WHERE option_name LIKE %s 
            OR option_name LIKE %s
        ", 
            '_transient_' . $prefix . '%',
            '_transient_timeout_' . $prefix . '%'
        ));
    }
    
    /**
     * Get recent logs
     * 
     * @param int $limit Number of logs to retrieve
     * @return array
     */
    protected function getRecentLogs($limit = 10)
    {
        // This would typically fetch from a custom table or file
        // For demo purposes, return sample logs
        return [
            [
                'id' => 1,
                'level' => 'info',
                'message' => __('Plugin initialized', $this->config['text_domain']),
                'timestamp' => current_time('mysql')
            ],
            [
                'id' => 2,
                'level' => 'info',
                'message' => __('Settings updated', $this->config['text_domain']),
                'timestamp' => date('Y-m-d H:i:s', strtotime('-5 minutes'))
            ]
        ];
    }
    
    /**
     * Get total log count
     * 
     * @return int
     */
    protected function getLogCount()
    {
        return 2; // Demo value
    }
    
    /**
     * Get scan history
     * 
     * @param int $limit Number of scans to retrieve
     * @return array
     */
    protected function getScanHistory($limit = 5)
    {
        return []; // Demo - no previous scans
    }
}