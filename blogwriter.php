<?php
/**
 * Plugin Name: Blog Writer
 * Plugin URI: https://lvl3marketing.com/blog-writer
 * Description: AI-powered blog post generation with intelligent internal linking and SEO optimization. Create high-quality content automatically with multiple AI models, keyword extraction, and smart internal linking to existing pages.
 * Version: 1.1.0
 * Author: LVL3 Marketing
 * Author URI: https://lvl3marketing.com
 * Text Domain: blog-writer
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.3
 * Requires PHP: 7.4
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('BLOG_WRITER_VERSION', '1.0.9');
define('BLOG_WRITER_PLUGIN_FILE', __FILE__);
define('BLOG_WRITER_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('BLOG_WRITER_PLUGIN_URL', plugin_dir_url(__FILE__));
define('BLOG_WRITER_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('BLOG_WRITER_TEXT_DOMAIN', 'blog-writer');

// Check if framework is already loaded
if (!class_exists('VisionFramework\\Core\\Plugin')) {
    // Load the framework autoloader
    require_once BLOG_WRITER_PLUGIN_DIR . 'framework/autoloader.php';
}

// Manually load required classes for activation hooks
// This ensures they're available before WordPress tries to call the activation hook
if (!class_exists('VisionFramework\\Core\\DatabaseHandler')) {
    require_once BLOG_WRITER_PLUGIN_DIR . 'framework/src/Core/DatabaseHandler.php';
}
if (!class_exists('BlogWriter\\BlogWriterDatabaseHandler')) {
    require_once BLOG_WRITER_PLUGIN_DIR . 'src/BlogWriterDatabaseHandler.php';
}
if (!class_exists('BlogWriter\\Installer')) {
    require_once BLOG_WRITER_PLUGIN_DIR . 'src/Installer.php';
}

// Initialize the plugin
add_action('plugins_loaded', function() {
    // Load plugin textdomain
    load_plugin_textdomain(
        BLOG_WRITER_TEXT_DOMAIN,
        false,
        dirname(BLOG_WRITER_PLUGIN_BASENAME) . '/languages'
    );
    
    // Initialize the Blog Writer plugin
    BlogWriter\BlogWriter::instance();
});

// Activation hook
register_activation_hook(__FILE__, function() {
    BlogWriter\Installer::activate();
});

// Deactivation hook
register_deactivation_hook(__FILE__, function() {
    BlogWriter\Installer::deactivate();
});

// Uninstall hook
register_uninstall_hook(__FILE__, ['BlogWriter\\Installer', 'uninstall']);