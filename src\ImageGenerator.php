<?php
/**
 * Image Generator
 * 
 * Handles AI image generation and stock image integration
 */

namespace BlogWriter;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ImageGenerator Class
 */
class ImageGenerator
{
    /**
     * Database handler instance
     * 
     * @var BlogWriterDatabaseHandler
     */
    protected $databaseHandler;
    
    /**
     * Plugin configuration
     * 
     * @var array
     */
    protected $config;
    
    /**
     * Available image generation services
     * 
     * @var array
     */
    protected $imageServices = [
        'dalle' => [
            'name' => 'DALL-E 3',
            'provider' => 'openai',
            'cost_per_image' => 0.04,
            'max_resolution' => '1024x1024',
            'styles' => ['natural', 'vivid']
        ],
        'midjourney' => [
            'name' => 'Midjourney',
            'provider' => 'midjourney',
            'cost_per_image' => 0.02,
            'max_resolution' => '1024x1024',
            'styles' => ['artistic', 'photographic', 'digital']
        ],
        'stable_diffusion' => [
            'name' => 'Stable Diffusion',
            'provider' => 'stability',
            'cost_per_image' => 0.01,
            'max_resolution' => '1024x1024',
            'styles' => ['realistic', 'artistic', 'anime']
        ]
    ];
    
    /**
     * Constructor
     * 
     * @param BlogWriterDatabaseHandler $databaseHandler
     * @param array $config
     */
    public function __construct($databaseHandler, $config)
    {
        $this->databaseHandler = $databaseHandler;
        $this->config = $config;
    }
    
    /**
     * Generate AI image for article
     * 
     * @param string $topic Article topic
     * @param array $options Generation options
     * @return array|WP_Error
     */
    public function generateArticleImage($topic, $options = [])
    {
        $service = $options['service'] ?? 'dalle';
        $style = $options['style'] ?? 'natural';
        $size = $options['size'] ?? '1024x1024';
        $custom_prompt = $options['custom_prompt'] ?? '';
        
        if (!isset($this->imageServices[$service])) {
            return new \WP_Error('invalid_service', __('Invalid image generation service specified.', $this->config['text_domain']));
        }
        
        $api_key = $this->databaseHandler->getBlogSetting('openrouter_api_key', '');
        
        if (empty($api_key)) {
            return new \WP_Error('no_api_key', __('API key not configured for image generation.', $this->config['text_domain']));
        }
        
        try {
            // Build image prompt
            $prompt = $this->buildImagePrompt($topic, $style, $custom_prompt);
            
            // Generate image
            $image_data = $this->performImageGeneration($service, $prompt, $size, $api_key);
            
            if (is_wp_error($image_data)) {
                return $image_data;
            }
            
            // Download and save image to WordPress media library
            $media_result = $this->saveToMediaLibrary($image_data, $topic);
            
            if (is_wp_error($media_result)) {
                return $media_result;
            }
            
            // Log the generation
            $this->databaseHandler->log('info', 'AI image generated', [
                'topic' => $topic,
                'service' => $service,
                'style' => $style,
                'media_id' => $media_result['attachment_id'],
                'prompt' => $prompt
            ]);
            
            return [
                'attachment_id' => $media_result['attachment_id'],
                'url' => $media_result['url'],
                'thumbnail_url' => $media_result['thumbnail_url'],
                'alt_text' => $media_result['alt_text'],
                'caption' => $media_result['caption'],
                'prompt_used' => $prompt,
                'service_used' => $service,
                'style_used' => $style,
                'generation_cost' => $this->imageServices[$service]['cost_per_image'],
                'metadata' => [
                    'topic' => $topic,
                    'generated_at' => current_time('mysql'),
                    'service' => $service,
                    'style' => $style,
                    'size' => $size
                ]
            ];
            
        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Image generation failed', [
                'topic' => $topic,
                'service' => $service,
                'error' => $e->getMessage()
            ]);
            
            return new \WP_Error('generation_failed', 
                sprintf(__('Image generation failed: %s', $this->config['text_domain']), $e->getMessage())
            );
        }
    }
    
    /**
     * Generate multiple image options
     * 
     * @param string $topic
     * @param array $options
     * @return array|WP_Error
     */
    public function generateImageOptions($topic, $options = [])
    {
        $count = $options['count'] ?? 3;
        $services = $options['services'] ?? ['dalle'];
        $styles = $options['styles'] ?? ['natural'];
        
        $generated_images = [];
        $errors = [];
        
        $combinations = [];
        foreach ($services as $service) {
            foreach ($styles as $style) {
                $combinations[] = ['service' => $service, 'style' => $style];
            }
        }
        
        // Limit to requested count
        $combinations = array_slice($combinations, 0, $count);
        
        foreach ($combinations as $combination) {
            $generation_options = array_merge($options, $combination);
            $result = $this->generateArticleImage($topic, $generation_options);
            
            if (is_wp_error($result)) {
                $errors[] = [
                    'service' => $combination['service'],
                    'style' => $combination['style'],
                    'error' => $result->get_error_message()
                ];
            } else {
                $generated_images[] = $result;
            }
            
            // Add delay between generations
            sleep(2);
        }
        
        return [
            'images' => $generated_images,
            'errors' => $errors,
            'total_requested' => $count,
            'successful_generations' => count($generated_images),
            'failed_generations' => count($errors)
        ];
    }
    
    /**
     * Build image generation prompt
     * 
     * @param string $topic
     * @param string $style
     * @param string $custom_prompt
     * @return string
     */
    protected function buildImagePrompt($topic, $style, $custom_prompt = '')
    {
        if (!empty($custom_prompt)) {
            // Replace placeholders in custom prompt
            return str_replace(['[TOPIC]', '[STYLE]'], [$topic, $style], $custom_prompt);
        }
        
        // Get default prompt template
        $default_prompt = $this->databaseHandler->getBlogSetting('image_generation_prompt',
            'Create a professional, relevant image for a blog post about [TOPIC]. Style should be modern and engaging.'
        );
        
        $prompt = str_replace(['[TOPIC]', '[STYLE]'], [$topic, $style], $default_prompt);
        
        // Add style-specific enhancements
        switch ($style) {
            case 'photographic':
            case 'realistic':
                $prompt .= " High-quality, photorealistic style with good lighting and composition.";
                break;
            case 'artistic':
                $prompt .= " Creative, artistic interpretation with vibrant colors and unique perspective.";
                break;
            case 'professional':
                $prompt .= " Clean, professional business style suitable for corporate content.";
                break;
            case 'minimal':
                $prompt .= " Minimalist design with clean lines and simple composition.";
                break;
            default:
                $prompt .= " Natural, engaging style that complements the content.";
        }
        
        $prompt .= " No text or watermarks in the image.";
        
        return $prompt;
    }
    
    /**
     * Perform image generation API call
     * 
     * @param string $service
     * @param string $prompt
     * @param string $size
     * @param string $api_key
     * @return array|WP_Error
     */
    protected function performImageGeneration($service, $prompt, $size, $api_key)
    {
        switch ($service) {
            case 'dalle':
                return $this->generateWithDALLE($prompt, $size, $api_key);
            case 'stable_diffusion':
                return $this->generateWithStableDiffusion($prompt, $size, $api_key);
            default:
                return new \WP_Error('unsupported_service', __('Image generation service not yet implemented.', $this->config['text_domain']));
        }
    }
    
    /**
     * Generate image with DALL-E via OpenRouter
     * 
     * @param string $prompt
     * @param string $size
     * @param string $api_key
     * @return array|WP_Error
     */
    protected function generateWithDALLE($prompt, $size, $api_key)
    {
        $api_url = 'https://openrouter.ai/api/v1/images/generations';
        
        $headers = [
            'Authorization' => 'Bearer ' . $api_key,
            'Content-Type' => 'application/json',
            'HTTP-Referer' => home_url(),
            'X-Title' => get_bloginfo('name') . ' - Blog Writer Plugin'
        ];
        
        $body = [
            'model' => 'openai/dall-e-3',
            'prompt' => $prompt,
            'size' => $size,
            'quality' => 'standard',
            'n' => 1
        ];
        
        $response = wp_remote_post($api_url, [
            'headers' => $headers,
            'body' => wp_json_encode($body),
            'timeout' => 60
        ]);
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($response_code !== 200) {
            $error_data = json_decode($response_body, true);
            $error_message = $error_data['error']['message'] ?? 'Unknown API error';
            
            return new \WP_Error('api_error', 
                sprintf(__('Image generation API request failed (%d): %s', $this->config['text_domain']), $response_code, $error_message)
            );
        }
        
        $data = json_decode($response_body, true);
        
        if (!isset($data['data'][0]['url'])) {
            return new \WP_Error('invalid_response', __('Invalid image generation response format.', $this->config['text_domain']));
        }
        
        return [
            'url' => $data['data'][0]['url'],
            'revised_prompt' => $data['data'][0]['revised_prompt'] ?? $prompt
        ];
    }
    
    /**
     * Generate image with Stable Diffusion (placeholder)
     * 
     * @param string $prompt
     * @param string $size
     * @param string $api_key
     * @return array|WP_Error
     */
    protected function generateWithStableDiffusion($prompt, $size, $api_key)
    {
        // This would be implemented based on Stability AI's API
        return new \WP_Error('not_implemented', __('Stable Diffusion integration not yet implemented.', $this->config['text_domain']));
    }
    
    /**
     * Save generated image to WordPress media library
     * 
     * @param array $image_data
     * @param string $topic
     * @return array|WP_Error
     */
    protected function saveToMediaLibrary($image_data, $topic)
    {
        require_once ABSPATH . 'wp-admin/includes/media.php';
        require_once ABSPATH . 'wp-admin/includes/file.php';
        require_once ABSPATH . 'wp-admin/includes/image.php';
        
        // Download the image
        $temp_file = download_url($image_data['url']);
        
        if (is_wp_error($temp_file)) {
            return new \WP_Error('download_failed', __('Failed to download generated image.', $this->config['text_domain']));
        }
        
        // Prepare file array
        $file_array = [
            'name' => $this->generateImageFilename($topic),
            'tmp_name' => $temp_file
        ];
        
        // Handle the upload
        $attachment_id = media_handle_sideload($file_array, 0);
        
        // Clean up temp file
        if (file_exists($temp_file)) {
            unlink($temp_file);
        }
        
        if (is_wp_error($attachment_id)) {
            return $attachment_id;
        }
        
        // Set image metadata
        $alt_text = $this->generateAltText($topic);
        $caption = $this->generateImageCaption($topic);
        
        update_post_meta($attachment_id, '_wp_attachment_image_alt', $alt_text);
        
        wp_update_post([
            'ID' => $attachment_id,
            'post_excerpt' => $caption,
            'post_content' => $image_data['revised_prompt'] ?? ''
        ]);
        
        // Add custom metadata
        update_post_meta($attachment_id, '_blog_writer_generated', true);
        update_post_meta($attachment_id, '_blog_writer_topic', $topic);
        update_post_meta($attachment_id, '_blog_writer_prompt', $image_data['revised_prompt'] ?? '');
        update_post_meta($attachment_id, '_blog_writer_generation_date', current_time('mysql'));
        
        $attachment_url = wp_get_attachment_url($attachment_id);
        $thumbnail_url = wp_get_attachment_thumb_url($attachment_id);
        
        return [
            'attachment_id' => $attachment_id,
            'url' => $attachment_url,
            'thumbnail_url' => $thumbnail_url ?: $attachment_url,
            'alt_text' => $alt_text,
            'caption' => $caption
        ];
    }
    
    /**
     * Generate filename for image
     * 
     * @param string $topic
     * @return string
     */
    protected function generateImageFilename($topic)
    {
        $slug = sanitize_title($topic);
        $slug = substr($slug, 0, 50); // Limit length
        $timestamp = time();
        
        return "blog-writer-{$slug}-{$timestamp}.jpg";
    }
    
    /**
     * Generate alt text for image
     * 
     * @param string $topic
     * @return string
     */
    protected function generateAltText($topic)
    {
        return sprintf(__('AI-generated image related to %s', $this->config['text_domain']), $topic);
    }
    
    /**
     * Generate image caption
     * 
     * @param string $topic
     * @return string
     */
    protected function generateImageCaption($topic)
    {
        return sprintf(__('Professional image illustrating concepts related to %s', $this->config['text_domain']), $topic);
    }
    
    /**
     * Search stock images (placeholder for future integration)
     * 
     * @param string $query
     * @param array $options
     * @return array|WP_Error
     */
    public function searchStockImages($query, $options = [])
    {
        // This would integrate with stock photo APIs like Unsplash, Pexels, etc.
        return new \WP_Error('not_implemented', __('Stock image search not yet implemented.', $this->config['text_domain']));
    }
    
    /**
     * Get generated images for an article
     * 
     * @param int $post_id
     * @return array
     */
    public function getArticleImages($post_id)
    {
        $attachments = get_attached_media('image', $post_id);
        $generated_images = [];
        
        foreach ($attachments as $attachment) {
            $is_generated = get_post_meta($attachment->ID, '_blog_writer_generated', true);
            
            if ($is_generated) {
                $generated_images[] = [
                    'id' => $attachment->ID,
                    'url' => wp_get_attachment_url($attachment->ID),
                    'thumbnail' => wp_get_attachment_thumb_url($attachment->ID),
                    'alt_text' => get_post_meta($attachment->ID, '_wp_attachment_image_alt', true),
                    'caption' => $attachment->post_excerpt,
                    'topic' => get_post_meta($attachment->ID, '_blog_writer_topic', true),
                    'prompt' => get_post_meta($attachment->ID, '_blog_writer_prompt', true),
                    'generated_date' => get_post_meta($attachment->ID, '_blog_writer_generation_date', true)
                ];
            }
        }
        
        return $generated_images;
    }
    
    /**
     * Get image generation statistics
     * 
     * @return array
     */
    public function getImageStats()
    {
        global $wpdb;
        
        $generated_images = $wpdb->get_var("
            SELECT COUNT(*) 
            FROM {$wpdb->postmeta} 
            WHERE meta_key = '_blog_writer_generated' 
            AND meta_value = '1'
        ");
        
        $total_cost = $generated_images * 0.04; // Rough estimate
        
        return [
            'total_generated' => (int) $generated_images,
            'estimated_total_cost' => round($total_cost, 2),
            'average_cost_per_image' => 0.04,
            'last_generation' => $this->getLastGenerationDate()
        ];
    }
    
    /**
     * Get last image generation date
     * 
     * @return string|null
     */
    protected function getLastGenerationDate()
    {
        global $wpdb;
        
        $last_date = $wpdb->get_var("
            SELECT meta_value 
            FROM {$wpdb->postmeta} 
            WHERE meta_key = '_blog_writer_generation_date' 
            ORDER BY meta_value DESC 
            LIMIT 1
        ");
        
        return $last_date;
    }
    
    /**
     * Get available image services
     * 
     * @return array
     */
    public function getAvailableServices()
    {
        return $this->imageServices;
    }
    
    /**
     * Estimate image generation cost
     * 
     * @param string $service
     * @param int $count
     * @return float
     */
    public function estimateImageCost($service, $count = 1)
    {
        if (!isset($this->imageServices[$service])) {
            return 0;
        }
        
        return $this->imageServices[$service]['cost_per_image'] * $count;
    }
    
    /**
     * Get recommended image styles for topic
     *
     * @param string $topic
     * @return array
     */
    public function getRecommendedStyles($topic)
    {
        $topic_lower = strtolower($topic);

        // Simple keyword-based style recommendations
        if (strpos($topic_lower, 'business') !== false || strpos($topic_lower, 'corporate') !== false) {
            return ['professional', 'minimal'];
        }

        if (strpos($topic_lower, 'creative') !== false || strpos($topic_lower, 'design') !== false) {
            return ['artistic', 'creative'];
        }

        if (strpos($topic_lower, 'technology') !== false || strpos($topic_lower, 'tech') !== false) {
            return ['modern', 'minimal'];
        }

        // Default recommendations
        return ['natural', 'professional'];
    }

    /**
     * Save image placeholder to database
     *
     * @param int $article_id Article ID
     * @param string $placeholder_text Original placeholder text
     * @param string $ai_prompt Generated AI prompt
     * @return int|false Placeholder ID or false on failure
     */
    public function saveImagePlaceholder($article_id, $placeholder_text, $ai_prompt)
    {
        if (!$this->databaseHandler->tableExists('image_placeholders')) {
            $this->databaseHandler->createTables();
        }

        global $wpdb;
        $table_name = $this->databaseHandler->getTableName('image_placeholders');

        $result = $wpdb->insert(
            $table_name,
            [
                'article_id' => $article_id,
                'placeholder_text' => $placeholder_text,
                'ai_prompt' => $ai_prompt,
                'generation_status' => 'pending'
            ],
            ['%d', '%s', '%s', '%s']
        );

        if ($result === false) {
            return false;
        }

        return $wpdb->insert_id;
    }

    /**
     * Get image placeholders for an article
     *
     * @param int $article_id Article ID
     * @return array Array of placeholders
     */
    public function getArticlePlaceholders($article_id)
    {
        if (!$this->databaseHandler->tableExists('image_placeholders')) {
            return [];
        }

        global $wpdb;
        $table_name = $this->databaseHandler->getTableName('image_placeholders');

        $placeholders = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM {$table_name} WHERE article_id = %d ORDER BY id ASC",
                $article_id
            ),
            ARRAY_A
        );

        // Decode JSON fields
        foreach ($placeholders as &$placeholder) {
            if (!empty($placeholder['generated_images'])) {
                $placeholder['generated_images'] = json_decode($placeholder['generated_images'], true);
            }
        }

        return $placeholders;
    }

    /**
     * Generate image for placeholder
     *
     * @param int $placeholder_id Placeholder ID
     * @param array $options Generation options
     * @return array|WP_Error
     */
    public function generatePlaceholderImage($placeholder_id, $options = [])
    {
        global $wpdb;
        $table_name = $this->databaseHandler->getTableName('image_placeholders');

        // Get placeholder data
        $placeholder = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM {$table_name} WHERE id = %d",
                $placeholder_id
            ),
            ARRAY_A
        );

        if (!$placeholder) {
            return new \WP_Error('placeholder_not_found', __('Image placeholder not found.', $this->config['text_domain']));
        }

        // Update status to generating
        $wpdb->update(
            $table_name,
            ['generation_status' => 'generating'],
            ['id' => $placeholder_id],
            ['%s'],
            ['%d']
        );

        // Use the AI prompt from the placeholder
        $topic = $placeholder['placeholder_text'];
        $custom_prompt = $placeholder['ai_prompt'];

        $generation_options = array_merge($options, [
            'custom_prompt' => $custom_prompt
        ]);

        // Generate the image
        $result = $this->generateArticleImage($topic, $generation_options);

        if (is_wp_error($result)) {
            // Update status to failed
            $wpdb->update(
                $table_name,
                ['generation_status' => 'failed'],
                ['id' => $placeholder_id],
                ['%s'],
                ['%d']
            );

            return $result;
        }

        // Update placeholder with generated image data
        $generated_images = $placeholder['generated_images'] ? json_decode($placeholder['generated_images'], true) : [];
        $generated_images[] = $result;

        $update_data = [
            'generated_images' => wp_json_encode($generated_images),
            'generation_status' => 'completed',
            'wp_attachment_id' => $result['attachment_id']
        ];

        // If this is the first generated image, set it as selected
        if (empty($placeholder['selected_image_url'])) {
            $update_data['selected_image_url'] = $result['url'];
            $update_data['selected_provider'] = $result['service_used'];
        }

        $wpdb->update(
            $table_name,
            $update_data,
            ['id' => $placeholder_id],
            ['%s', '%s', '%d', '%s', '%s'],
            ['%d']
        );

        return $result;
    }

    /**
     * Update placeholder selection
     *
     * @param int $placeholder_id Placeholder ID
     * @param string $image_url Selected image URL
     * @param string $provider Provider used
     * @param int $attachment_id WordPress attachment ID
     * @return bool
     */
    public function updatePlaceholderSelection($placeholder_id, $image_url, $provider, $attachment_id)
    {
        global $wpdb;
        $table_name = $this->databaseHandler->getTableName('image_placeholders');

        $result = $wpdb->update(
            $table_name,
            [
                'selected_image_url' => $image_url,
                'selected_provider' => $provider,
                'wp_attachment_id' => $attachment_id
            ],
            ['id' => $placeholder_id],
            ['%s', '%s', '%d'],
            ['%d']
        );

        return $result !== false;
    }
}