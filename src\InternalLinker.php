<?php
/**
 * Internal Linker
 * 
 * Handles intelligent internal linking based on page keywords and content analysis
 */

namespace BlogWriter;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * InternalLinker Class
 */
class InternalLinker
{
    /**
     * Database handler instance
     * 
     * @var BlogWriterDatabaseHandler
     */
    protected $databaseHandler;
    
    /**
     * Plugin configuration
     * 
     * @var array
     */
    protected $config;
    
    /**
     * Page keywords cache
     * 
     * @var array
     */
    protected $pageKeywordsCache = [];
    
    /**
     * Constructor
     * 
     * @param BlogWriterDatabaseHandler $databaseHandler
     * @param array $config
     */
    public function __construct($databaseHandler, $config)
    {
        $this->databaseHandler = $databaseHandler;
        $this->config = $config;
        $this->loadPageKeywords();
    }
    
    /**
     * Process article content and add internal links
     * 
     * @param string $content Article content
     * @param array $options Linking options
     * @return array
     */
    public function processInternalLinks($content, $options = [])
    {
        $max_links = $options['max_links'] ?? $this->databaseHandler->getBlogSetting('max_internal_links', 5);
        $min_relevance_score = $options['min_relevance_score'] ?? 0.3;
        $link_strategy = $options['link_strategy'] ?? 'balanced'; // aggressive, conservative, balanced
        $avoid_consecutive = $options['avoid_consecutive'] ?? true;
        
        // Find potential link opportunities
        $link_opportunities = $this->findLinkOpportunities($content, $options);
        
        if (empty($link_opportunities)) {
            return [
                'content' => $content,
                'links_added' => [],
                'link_count' => 0,
                'opportunities_found' => 0
            ];
        }
        
        // Score and rank opportunities
        $scored_opportunities = $this->scoreLinkOpportunities($link_opportunities, $content, $link_strategy);
        
        // Filter by relevance score
        $qualified_opportunities = array_filter($scored_opportunities, function($opp) use ($min_relevance_score) {
            return $opp['relevance_score'] >= $min_relevance_score;
        });
        
        // Sort by score descending
        usort($qualified_opportunities, function($a, $b) {
            return $b['relevance_score'] <=> $a['relevance_score'];
        });
        
        // Select and apply links
        $selected_links = $this->selectOptimalLinks($qualified_opportunities, $max_links, $avoid_consecutive);
        $processed_content = $this->applyLinksToContent($content, $selected_links);
        
        // Generate link summary
        $link_summary = $this->generateLinkSummary($selected_links);
        
        // Log the linking process
        $this->databaseHandler->log('info', 'Internal links processed', [
            'opportunities_found' => count($link_opportunities),
            'qualified_opportunities' => count($qualified_opportunities),
            'links_applied' => count($selected_links),
            'max_links' => $max_links,
            'strategy' => $link_strategy
        ]);
        
        return [
            'content' => $processed_content,
            'links_added' => $link_summary,
            'link_count' => count($selected_links),
            'opportunities_found' => count($link_opportunities),
            'qualified_opportunities' => count($qualified_opportunities)
        ];
    }
    
    /**
     * Find potential link opportunities in content
     * 
     * @param string $content
     * @param array $options
     * @return array
     */
    protected function findLinkOpportunities($content, $options = [])
    {
        $opportunities = [];
        $content_text = strip_tags($content);
        $content_words = $this->extractContentWords($content_text);
        
        foreach ($this->pageKeywordsCache as $page_id => $page_data) {
            if (empty($page_data['keywords'])) {
                continue;
            }
            
            $page_keywords = explode(',', $page_data['keywords']);
            
            foreach ($page_keywords as $keyword) {
                $keyword = trim($keyword);
                
                if (strlen($keyword) < 3) {
                    continue;
                }
                
                // Find keyword matches in content
                $matches = $this->findKeywordMatches($content_text, $keyword);
                
                foreach ($matches as $match) {
                    $opportunities[] = [
                        'page_id' => $page_id,
                        'page_title' => $page_data['title'],
                        'page_url' => $page_data['url'],
                        'keyword' => $keyword,
                        'matched_text' => $match['text'],
                        'position' => $match['position'],
                        'context' => $this->extractContext($content_text, $match['position'], 100),
                        'keyword_length' => strlen($keyword),
                        'exact_match' => $match['exact_match']
                    ];
                }
            }
        }
        
        return $opportunities;
    }
    
    /**
     * Find keyword matches in content
     * 
     * @param string $content
     * @param string $keyword
     * @return array
     */
    protected function findKeywordMatches($content, $keyword)
    {
        $matches = [];
        $content_lower = strtolower($content);
        $keyword_lower = strtolower($keyword);
        
        // Find exact matches
        $offset = 0;
        while (($pos = strpos($content_lower, $keyword_lower, $offset)) !== false) {
            // Check if it's a whole word match
            if ($this->isWholeWordMatch($content_lower, $keyword_lower, $pos)) {
                $matches[] = [
                    'text' => substr($content, $pos, strlen($keyword)),
                    'position' => $pos,
                    'exact_match' => true
                ];
            }
            $offset = $pos + 1;
        }
        
        // Find partial matches for multi-word keywords
        if (str_word_count($keyword) > 1) {
            $keyword_words = explode(' ', $keyword_lower);
            $partial_matches = $this->findPartialMatches($content_lower, $keyword_words);
            
            foreach ($partial_matches as $match) {
                $matches[] = [
                    'text' => substr($content, $match['position'], $match['length']),
                    'position' => $match['position'],
                    'exact_match' => false
                ];
            }
        }
        
        return $matches;
    }
    
    /**
     * Check if keyword match is a whole word
     * 
     * @param string $content
     * @param string $keyword
     * @param int $position
     * @return bool
     */
    protected function isWholeWordMatch($content, $keyword, $position)
    {
        $keyword_length = strlen($keyword);
        
        // Check character before
        if ($position > 0) {
            $char_before = $content[$position - 1];
            if (ctype_alnum($char_before)) {
                return false;
            }
        }
        
        // Check character after
        $after_position = $position + $keyword_length;
        if ($after_position < strlen($content)) {
            $char_after = $content[$after_position];
            if (ctype_alnum($char_after)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Find partial matches for multi-word keywords
     * 
     * @param string $content
     * @param array $keyword_words
     * @return array
     */
    protected function findPartialMatches($content, $keyword_words)
    {
        $matches = [];
        
        // This is a simplified implementation
        // In a full implementation, you'd use more sophisticated matching
        
        return $matches;
    }
    
    /**
     * Score link opportunities based on relevance and strategy
     * 
     * @param array $opportunities
     * @param string $content
     * @param string $strategy
     * @return array
     */
    protected function scoreLinkOpportunities($opportunities, $content, $strategy)
    {
        $content_text = strip_tags($content);
        $content_length = strlen($content_text);
        
        foreach ($opportunities as &$opportunity) {
            $score = 0;
            
            // Base score from keyword match
            $score += $opportunity['exact_match'] ? 1.0 : 0.6;
            
            // Keyword length bonus (longer keywords are more specific)
            $score += min($opportunity['keyword_length'] / 20, 0.5);
            
            // Position scoring (middle content gets higher score)
            $position_factor = 1 - abs(($opportunity['position'] / $content_length) - 0.5) * 2;
            $score += $position_factor * 0.3;
            
            // Context relevance (simplified semantic analysis)
            $context_score = $this->analyzeContextRelevance($opportunity['context'], $opportunity['keyword']);
            $score += $context_score * 0.4;
            
            // Page authority (based on page status and date)
            $page_data = $this->pageKeywordsCache[$opportunity['page_id']];
            if ($page_data['status'] === 'publish') {
                $score += 0.2;
            }
            
            // Apply strategy adjustments
            switch ($strategy) {
                case 'aggressive':
                    $score *= 1.2;
                    break;
                case 'conservative':
                    $score *= 0.8;
                    break;
                default: // balanced
                    // No adjustment
                    break;
            }
            
            $opportunity['relevance_score'] = round($score, 3);
        }
        
        return $opportunities;
    }
    
    /**
     * Analyze context relevance using simple semantic analysis
     * 
     * @param string $context
     * @param string $keyword
     * @return float
     */
    protected function analyzeContextRelevance($context, $keyword)
    {
        $context_words = $this->extractContentWords($context);
        $keyword_words = $this->extractContentWords($keyword);
        
        $relevance_score = 0;
        $total_comparisons = 0;
        
        foreach ($keyword_words as $kw_word) {
            foreach ($context_words as $ctx_word) {
                $total_comparisons++;
                
                // Exact match
                if ($kw_word === $ctx_word) {
                    $relevance_score += 1.0;
                    continue;
                }
                
                // Partial similarity (simplified)
                $similarity = $this->calculateWordSimilarity($kw_word, $ctx_word);
                $relevance_score += $similarity;
            }
        }
        
        return $total_comparisons > 0 ? $relevance_score / $total_comparisons : 0;
    }
    
    /**
     * Calculate word similarity (simplified)
     * 
     * @param string $word1
     * @param string $word2
     * @return float
     */
    protected function calculateWordSimilarity($word1, $word2)
    {
        if (strlen($word1) < 3 || strlen($word2) < 3) {
            return 0;
        }
        
        // Simple substring matching
        if (strpos($word1, $word2) !== false || strpos($word2, $word1) !== false) {
            return 0.6;
        }
        
        // Character overlap
        $chars1 = array_count_values(str_split($word1));
        $chars2 = array_count_values(str_split($word2));
        
        $overlap = 0;
        foreach ($chars1 as $char => $count1) {
            if (isset($chars2[$char])) {
                $overlap += min($count1, $chars2[$char]);
            }
        }
        
        $max_length = max(strlen($word1), strlen($word2));
        return $overlap / $max_length;
    }
    
    /**
     * Select optimal links avoiding conflicts
     * 
     * @param array $opportunities
     * @param int $max_links
     * @param bool $avoid_consecutive
     * @return array
     */
    protected function selectOptimalLinks($opportunities, $max_links, $avoid_consecutive)
    {
        $selected = [];
        $used_positions = [];
        $used_pages = [];
        
        foreach ($opportunities as $opportunity) {
            if (count($selected) >= $max_links) {
                break;
            }
            
            // Avoid linking to the same page multiple times
            if (isset($used_pages[$opportunity['page_id']])) {
                continue;
            }
            
            // Avoid consecutive links if requested
            if ($avoid_consecutive && $this->hasConflictingPosition($opportunity['position'], $used_positions, 50)) {
                continue;
            }
            
            $selected[] = $opportunity;
            $used_positions[] = $opportunity['position'];
            $used_pages[$opportunity['page_id']] = true;
        }
        
        return $selected;
    }
    
    /**
     * Check if position conflicts with existing positions
     * 
     * @param int $position
     * @param array $used_positions
     * @param int $min_distance
     * @return bool
     */
    protected function hasConflictingPosition($position, $used_positions, $min_distance)
    {
        foreach ($used_positions as $used_position) {
            if (abs($position - $used_position) < $min_distance) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Apply selected links to content
     * 
     * @param string $content
     * @param array $selected_links
     * @return string
     */
    protected function applyLinksToContent($content, $selected_links)
    {
        // Sort links by position (descending) to avoid position shifting
        usort($selected_links, function($a, $b) {
            return $b['position'] <=> $a['position'];
        });
        
        foreach ($selected_links as $link) {
            $link_html = sprintf(
                '<a href="%s" title="%s" class="blog-writer-internal-link">%s</a>',
                esc_url($link['page_url']),
                esc_attr($link['page_title']),
                esc_html($link['matched_text'])
            );
            
            $content = substr_replace(
                $content,
                $link_html,
                $link['position'],
                strlen($link['matched_text'])
            );
        }
        
        return $content;
    }
    
    /**
     * Generate link summary for reporting
     * 
     * @param array $selected_links
     * @return array
     */
    protected function generateLinkSummary($selected_links)
    {
        $summary = [];
        
        foreach ($selected_links as $link) {
            $summary[] = [
                'page_id' => $link['page_id'],
                'page_title' => $link['page_title'],
                'page_url' => $link['page_url'],
                'anchor_text' => $link['matched_text'],
                'keyword' => $link['keyword'],
                'relevance_score' => $link['relevance_score'],
                'exact_match' => $link['exact_match'],
                'context' => substr($link['context'], 0, 100) . '...'
            ];
        }
        
        return $summary;
    }
    
    /**
     * Load page keywords into cache
     */
    protected function loadPageKeywords()
    {
        $pages = $this->databaseHandler->getAllPages(['status' => 'publish']);
        
        foreach ($pages as $page) {
            $this->pageKeywordsCache[$page['page_id']] = [
                'title' => $page['page_title'],
                'url' => $page['page_url'],
                'status' => $page['page_status'],
                'keywords' => $page['keywords']
            ];
        }
    }
    
    /**
     * Extract meaningful words from content
     * 
     * @param string $text
     * @return array
     */
    protected function extractContentWords($text)
    {
        // Convert to lowercase and remove special characters
        $text = strtolower(preg_replace('/[^a-zA-Z0-9\s]/', ' ', $text));
        
        // Split into words
        $words = preg_split('/\s+/', $text);
        
        // Remove stop words and short words
        $stop_words = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'they', 'them', 'their', 'there', 'then', 'than', 'when', 'where', 'who', 'what', 'how', 'why', 'which', 'while', 'during', 'before', 'after', 'above', 'below', 'up', 'down', 'out', 'off', 'over', 'under', 'again', 'further', 'once', 'here', 'now', 'very', 'too', 'only', 'just', 'also', 'still', 'even', 'more', 'most', 'other', 'some', 'any', 'each', 'few', 'many', 'much', 'several', 'all', 'both', 'either', 'neither', 'such', 'same', 'different', 'new', 'old', 'first', 'last', 'next', 'previous', 'good', 'better', 'best', 'bad', 'worse', 'worst'];
        
        $filtered_words = [];
        foreach ($words as $word) {
            $word = trim($word);
            if (strlen($word) >= 3 && !in_array($word, $stop_words) && !is_numeric($word)) {
                $filtered_words[] = $word;
            }
        }
        
        return array_unique($filtered_words);
    }
    
    /**
     * Extract context around a position
     * 
     * @param string $text
     * @param int $position
     * @param int $length
     * @return string
     */
    protected function extractContext($text, $position, $length)
    {
        $start = max(0, $position - $length);
        $end = min(strlen($text), $position + $length);
        
        return trim(substr($text, $start, $end - $start));
    }
    
    /**
     * Preview internal links without applying them
     * 
     * @param string $content
     * @param array $options
     * @return array
     */
    public function previewInternalLinks($content, $options = [])
    {
        $link_opportunities = $this->findLinkOpportunities($content, $options);
        $scored_opportunities = $this->scoreLinkOpportunities($link_opportunities, $content, $options['link_strategy'] ?? 'balanced');
        
        $min_relevance_score = $options['min_relevance_score'] ?? 0.3;
        $qualified_opportunities = array_filter($scored_opportunities, function($opp) use ($min_relevance_score) {
            return $opp['relevance_score'] >= $min_relevance_score;
        });
        
        usort($qualified_opportunities, function($a, $b) {
            return $b['relevance_score'] <=> $a['relevance_score'];
        });
        
        $max_links = $options['max_links'] ?? $this->databaseHandler->getBlogSetting('max_internal_links', 5);
        $preview_links = array_slice($qualified_opportunities, 0, $max_links);
        
        return [
            'total_opportunities' => count($link_opportunities),
            'qualified_opportunities' => count($qualified_opportunities),
            'preview_links' => $this->generateLinkSummary($preview_links),
            'settings' => [
                'max_links' => $max_links,
                'min_relevance_score' => $min_relevance_score,
                'strategy' => $options['link_strategy'] ?? 'balanced'
            ]
        ];
    }
    
    /**
     * Get internal linking statistics
     * 
     * @return array
     */
    public function getLinkingStats()
    {
        $total_pages = count($this->pageKeywordsCache);
        $pages_with_keywords = count(array_filter($this->pageKeywordsCache, function($page) {
            return !empty($page['keywords']);
        }));
        
        $articles = $this->databaseHandler->getArticles(['limit' => 0]);
        $articles_with_links = 0;
        $total_links = 0;
        
        foreach ($articles as $article) {
            if (!empty($article['internal_links'])) {
                $links = json_decode($article['internal_links'], true);
                if (is_array($links) && count($links) > 0) {
                    $articles_with_links++;
                    $total_links += count($links);
                }
            }
        }
        
        return [
            'total_pages' => $total_pages,
            'pages_with_keywords' => $pages_with_keywords,
            'keyword_coverage' => $total_pages > 0 ? round(($pages_with_keywords / $total_pages) * 100, 1) : 0,
            'total_articles' => count($articles),
            'articles_with_links' => $articles_with_links,
            'total_internal_links' => $total_links,
            'average_links_per_article' => count($articles) > 0 ? round($total_links / count($articles), 1) : 0
        ];
    }
    
    /**
     * Refresh page keywords cache
     */
    public function refreshCache()
    {
        $this->pageKeywordsCache = [];
        $this->loadPageKeywords();
    }
    
    /**
     * Get linking opportunities for a specific content
     * 
     * @param string $content
     * @param array $options
     * @return array
     */
    public function analyzeLinkingOpportunities($content, $options = [])
    {
        $opportunities = $this->findLinkOpportunities($content, $options);
        $scored_opportunities = $this->scoreLinkOpportunities($opportunities, $content, $options['link_strategy'] ?? 'balanced');
        
        // Group by page
        $grouped_opportunities = [];
        foreach ($scored_opportunities as $opportunity) {
            $page_id = $opportunity['page_id'];
            if (!isset($grouped_opportunities[$page_id])) {
                $grouped_opportunities[$page_id] = [
                    'page_id' => $page_id,
                    'page_title' => $opportunity['page_title'],
                    'page_url' => $opportunity['page_url'],
                    'opportunities' => []
                ];
            }
            $grouped_opportunities[$page_id]['opportunities'][] = $opportunity;
        }
        
        // Calculate page scores
        foreach ($grouped_opportunities as &$page_group) {
            $page_group['total_opportunities'] = count($page_group['opportunities']);
            $page_group['max_score'] = max(array_column($page_group['opportunities'], 'relevance_score'));
            $page_group['avg_score'] = array_sum(array_column($page_group['opportunities'], 'relevance_score')) / $page_group['total_opportunities'];
        }
        
        // Sort by max score
        uasort($grouped_opportunities, function($a, $b) {
            return $b['max_score'] <=> $a['max_score'];
        });
        
        return array_values($grouped_opportunities);
    }
}