<!DOCTYPE html>
<html>
<head>
    <title>API Fixes Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007cba; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        button { background: #007cba; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        input[type="text"] { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace; }
        .provider-test { margin: 10px 0; padding: 10px; background: #f9f9f9; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>🧪 Blog Writer API Fixes Test</h1>
    <p>This page tests the fixed API functionality. Check browser console for detailed logs.</p>

    <div class="test-section">
        <h2>1. Message Formatting Test</h2>
        <p>Tests the JavaScript message formatting with various data combinations:</p>
        <button onclick="testMessageFormatting()">Test Message Formatting</button>
        <div id="message-results"></div>
    </div>

    <div class="test-section">
        <h2>2. Data Validation Test</h2>
        <p>Tests the enhanced undefined/null checks:</p>
        <button onclick="testDataValidation()">Test Data Validation</button>
        <div id="validation-results"></div>
    </div>

    <div class="test-section">
        <h2>3. WordPress AJAX Test</h2>
        <p>Test actual WordPress AJAX endpoints (requires WordPress to be running):</p>
        
        <div class="provider-test">
            <h3>Test API Key Save</h3>
            <label>Provider: 
                <select id="test-provider">
                    <option value="openrouter">OpenRouter</option>
                    <option value="openai">OpenAI</option>
                    <option value="anthropic">Anthropic</option>
                    <option value="groq">Groq</option>
                    <option value="google">Google</option>
                </select>
            </label>
            <br><br>
            <label>API Key: <input type="text" id="test-api-key" placeholder="test-key-123" value="test-key-123"></label>
            <br><br>
            <button onclick="testApiKeySave()">Test Save API Key</button>
        </div>

        <div class="provider-test">
            <h3>Test API Connection</h3>
            <button onclick="testApiConnection()">Test API Connection (uses saved key)</button>
        </div>

        <div id="ajax-results"></div>
    </div>

    <script>
        // Copy the exact message formatting logic from admin.js
        function formatApiMessage(provider, data) {
            console.log('🔍 Formatting message for', provider, 'with data:', data);
            
            let message = data.message || `${provider.charAt(0).toUpperCase() + provider.slice(1)} API connected successfully!`;
            
            const modelCount = data.models_available;
            const responseTime = data.response_time;
            
            console.log('📊 Message data:', {
                modelCount: modelCount,
                responseTime: responseTime,
                details: data.details
            });
            
            if (modelCount !== undefined && modelCount !== null && !isNaN(modelCount) && 
                responseTime !== undefined && responseTime !== null && !isNaN(responseTime)) {
                message += ` (${modelCount} models, ${responseTime}s)`;
            } else if (modelCount !== undefined && modelCount !== null && !isNaN(modelCount)) {
                message += ` (${modelCount} models available)`;
            } else if (responseTime !== undefined && responseTime !== null && !isNaN(responseTime)) {
                message += ` (${responseTime}s response time)`;
            } else if (data.details && data.details.trim()) {
                message += ` - ${data.details}`;
            }
            
            return message;
        }

        function testMessageFormatting() {
            const testCases = [
                {
                    name: 'Complete data',
                    provider: 'openrouter',
                    data: { message: '✅ OpenRouter connection successful!', models_available: 150, response_time: 0.5 }
                },
                {
                    name: 'Only model count',
                    provider: 'openai',
                    data: { models_available: 25 }
                },
                {
                    name: 'Only response time',
                    provider: 'anthropic',
                    data: { response_time: 1.2 }
                },
                {
                    name: 'Only details',
                    provider: 'groq',
                    data: { details: 'Ultra-fast inference available' }
                },
                {
                    name: 'Undefined values (old bug)',
                    provider: 'google',
                    data: { models_available: undefined, response_time: undefined, details: undefined }
                },
                {
                    name: 'Null values',
                    provider: 'openai',
                    data: { models_available: null, response_time: null }
                },
                {
                    name: 'NaN values',
                    provider: 'anthropic',
                    data: { models_available: NaN, response_time: NaN }
                },
                {
                    name: 'Empty details',
                    provider: 'groq',
                    data: { details: '' }
                }
            ];

            let results = '<h3>Message Formatting Results:</h3>';
            
            testCases.forEach(test => {
                const result = formatApiMessage(test.provider, test.data);
                results += `<div><strong>${test.name}:</strong> <span class="info">${result}</span></div>`;
            });

            document.getElementById('message-results').innerHTML = results;
        }

        function testDataValidation() {
            const testValues = [undefined, null, NaN, 0, '', '   ', 42, 'valid string'];
            let results = '<h3>Data Validation Results:</h3><pre>';
            
            testValues.forEach(value => {
                const isValid = value !== undefined && value !== null && !isNaN(value);
                results += `Value: ${JSON.stringify(value)} → Valid: ${isValid}\n`;
            });
            
            results += '</pre>';
            document.getElementById('validation-results').innerHTML = results;
        }

        function testApiKeySave() {
            const provider = document.getElementById('test-provider').value;
            const apiKey = document.getElementById('test-api-key').value;
            
            console.log('🧪 Testing API key save for:', provider, 'Key length:', apiKey.length);
            
            $.ajax({
                url: '/wp-admin/admin-ajax.php',
                type: 'POST',
                data: {
                    action: 'blog-writer_ajax',
                    endpoint: 'save_api_key',
                    provider: provider,
                    api_key: apiKey,
                    nonce: '<?php echo wp_create_nonce("blog-writer_nonce"); ?>' // This won't work in static HTML
                },
                dataType: 'json'
            })
            .done(function(response) {
                console.log('✅ API Key Save Success:', response);
                $('#ajax-results').html('<div class="success">Save Success: ' + JSON.stringify(response, null, 2) + '</div>');
            })
            .fail(function(xhr, status, error) {
                console.error('❌ API Key Save Failed:', {xhr, status, error});
                $('#ajax-results').html('<div class="error">Save Error: ' + status + ' - ' + error + '<br>Response: ' + xhr.responseText + '</div>');
            });
        }

        function testApiConnection() {
            const provider = document.getElementById('test-provider').value;
            
            console.log('🧪 Testing API connection for:', provider);
            
            $.ajax({
                url: '/wp-admin/admin-ajax.php',
                type: 'POST',
                data: {
                    action: 'blog-writer_ajax',
                    endpoint: 'test_api_provider',
                    provider: provider,
                    api_key: 'test-key', // This would normally come from the saved value
                    nonce: '<?php echo wp_create_nonce("blog-writer_nonce"); ?>' // This won't work in static HTML
                },
                dataType: 'json'
            })
            .done(function(response) {
                console.log('✅ API Test Success:', response);
                const formatted = formatApiMessage(provider, response.data || {});
                $('#ajax-results').html('<div class="success">Test Success: ' + formatted + '<br><pre>' + JSON.stringify(response, null, 2) + '</pre></div>');
            })
            .fail(function(xhr, status, error) {
                console.error('❌ API Test Failed:', {xhr, status, error});
                $('#ajax-results').html('<div class="error">Test Error: ' + status + ' - ' + error + '<br>Response: ' + xhr.responseText + '</div>');
            });
        }

        // Auto-run message formatting test
        $(document).ready(function() {
            console.log('🚀 Test page loaded');
            testMessageFormatting();
        });
    </script>

    <h2>Instructions:</h2>
    <ol>
        <li><strong>Message Formatting Test:</strong> Runs automatically and tests the fixed undefined message bug</li>
        <li><strong>Data Validation Test:</strong> Click to see how the new validation logic works</li>
        <li><strong>WordPress AJAX Test:</strong> Only works if you place this file in your WordPress root and access it via your local server</li>
    </ol>

    <p><strong>Expected Results:</strong></p>
    <ul>
        <li>No more "undefined (undefined models, undefineds)" messages</li>
        <li>Clean, properly formatted success messages</li>
        <li>Detailed console logging for debugging API key saves</li>
    </ul>
</body>
</html>