<?php
/**
 * Standalone API Testing Script
 * Tests all major AI provider APIs to verify endpoints and authentication
 */

echo "API Testing Script\n";
echo "==================\n\n";

// Test OpenRouter
function testOpenRouter($apiKey) {
    echo "Testing OpenRouter...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://openrouter.ai/api/v1/models');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $apiKey,
        'HTTP-Referer: ' . 'http://localhost'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "  ❌ Error: $error\n";
        return false;
    }
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        $modelCount = isset($data['data']) ? count($data['data']) : 0;
        echo "  ✅ Success: $modelCount models available\n";
        return true;
    } else {
        echo "  ❌ HTTP $httpCode: $response\n";
        return false;
    }
}

// Test OpenAI
function testOpenAI($apiKey) {
    echo "Testing OpenAI...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/models');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $apiKey
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "  ❌ Error: $error\n";
        return false;
    }
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        $modelCount = isset($data['data']) ? count($data['data']) : 0;
        echo "  ✅ Success: $modelCount models available\n";
        return true;
    } else {
        echo "  ❌ HTTP $httpCode: $response\n";
        return false;
    }
}

// Test Anthropic
function testAnthropic($apiKey) {
    echo "Testing Anthropic...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.anthropic.com/v1/messages');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'x-api-key: ' . $apiKey,
        'anthropic-version: 2023-06-01',
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
        'model' => 'claude-3-haiku-20240307',
        'max_tokens' => 1,
        'messages' => [
            [
                'role' => 'user',
                'content' => 'Hi'
            ]
        ]
    ]));
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "  ❌ Error: $error\n";
        return false;
    }
    
    if ($httpCode === 200) {
        echo "  ✅ Success: Claude API connected\n";
        return true;
    } else {
        echo "  ❌ HTTP $httpCode: $response\n";
        return false;
    }
}

// Test Groq
function testGroq($apiKey) {
    echo "Testing Groq...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.groq.com/openai/v1/models');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $apiKey
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "  ❌ Error: $error\n";
        return false;
    }
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        $modelCount = isset($data['data']) ? count($data['data']) : 0;
        echo "  ✅ Success: $modelCount models available\n";
        return true;
    } else {
        echo "  ❌ HTTP $httpCode: $response\n";
        return false;
    }
}

// Test Google Gemini
function testGoogle($apiKey) {
    echo "Testing Google Gemini...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://generativelanguage.googleapis.com/v1/models?key=$apiKey");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "  ❌ Error: $error\n";
        return false;
    }
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        $modelCount = isset($data['models']) ? count($data['models']) : 0;
        echo "  ✅ Success: $modelCount models available\n";
        return true;
    } else {
        echo "  ❌ HTTP $httpCode: $response\n";
        return false;
    }
}

// Example usage - replace with actual API keys
echo "To test APIs, edit this file and add your API keys:\n\n";

/*
// Uncomment and add your API keys to test:
testOpenRouter('sk-or-v1-your-key-here');
testOpenAI('sk-your-key-here');
testAnthropic('sk-ant-your-key-here');
testGroq('gsk_your-key-here');
testGoogle('AIza-your-key-here');
*/

echo "All API test functions are ready to use.\n";