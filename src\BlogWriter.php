<?php
/**
 * Blog Writer Plugin Implementation
 * 
 * Main plugin class that coordinates all Blog Writer functionality
 */

namespace BlogWriter;

use VisionFramework\Core\Plugin;
use VisionFramework\Core\DatabaseHandler;
use BlogWriter\BlogWriterDatabaseHandler;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BlogWriter Class
 */
class BlogWriter extends Plugin
{
    /**
     * Database handler instance
     * 
     * @var DatabaseHandler
     */
    protected $databaseHandler;
    
    /**
     * API integration handler instance
     * 
     * @var ApiIntegrationHandler
     */
    protected $apiIntegrationHandler;
    
    /**
     * Keyword extractor instance
     * 
     * @var KeywordExtractor
     */
    protected $keywordExtractor;
    
    /**
     * Article generator instance
     * 
     * @var ArticleGenerator
     */
    protected $articleGenerator;
    
    /**
     * Internal linker instance
     * 
     * @var InternalLinker
     */
    protected $internalLinker;
    
    /**
     * Image generator instance
     * 
     * @var ImageGenerator
     */
    protected $imageGenerator;
    
    /**
     * User manager instance
     * 
     * @var UserManager
     */
    protected $userManager;
    
    /**
     * Initialize plugin configuration
     */
    protected function initConfig()
    {
        $this->config = [
            'plugin_name' => 'Blog Writer',
            'plugin_slug' => 'blog-writer',
            'menu_title' => 'Blog Writer',
            'menu_slug' => 'blog-writer',
            'menu_icon' => 'dashicons-edit-page',
            'menu_position' => 30,
            'version' => BLOG_WRITER_VERSION,
            'text_domain' => BLOG_WRITER_TEXT_DOMAIN,
            'plugin_url' => BLOG_WRITER_PLUGIN_URL,
            'plugin_dir' => BLOG_WRITER_PLUGIN_DIR,
            'db_version' => '1.0.0',
            'author' => 'LVL3 Marketing',
            'website' => 'https://lvl3marketing.com',
            'partner_name' => 'LVL3 Marketing',
            'logo_url' => BLOG_WRITER_PLUGIN_URL . 'assets/images/logo.svg'
        ];
    }
    
    /**
     * Initialize plugin components
     */
    protected function initComponents()
    {
        parent::initComponents();
        
        // Initialize database handler
        $this->databaseHandler = new BlogWriterDatabaseHandler($this->config);
        
        // Initialize API integration handler
        $this->apiIntegrationHandler = new ApiIntegrationHandler($this->databaseHandler, $this->config);
        
        // Initialize Blog Writer components with API handler
        $this->keywordExtractor = new KeywordExtractor($this->databaseHandler, $this->config, $this->apiIntegrationHandler);
        $this->articleGenerator = new ArticleGenerator($this->databaseHandler, $this->config, $this->apiIntegrationHandler);
        $this->internalLinker = new InternalLinker($this->databaseHandler, $this->config);
        $this->imageGenerator = new ImageGenerator($this->databaseHandler, $this->config, $this->apiIntegrationHandler);
        $this->userManager = new UserManager($this->databaseHandler, $this->config);
        
        // Add custom hooks
        add_action('init', [$this, 'checkForUpdates']);
        add_action('admin_notices', [$this, 'showAdminNotices']);
        
        // Add AJAX endpoints
        $this->registerAjaxEndpoints();
        
        // Add custom admin tabs
        $this->registerAdminTabs();
        
        // Hook into framework events
        add_action('vision:form-success', [$this, 'handleFormSuccess'], 10, 2);
        add_action('vision:tab-changed', [$this, 'handleTabChange'], 10, 1);
    }
    
    /**
     * Register AJAX endpoints
     */
    protected function registerAjaxEndpoints()
    {
        $this->apiHandler->registerEndpoint('extract_keywords', [$this, 'handleKeywordExtraction']);
        $this->apiHandler->registerEndpoint('generate_article', [$this, 'handleArticleGeneration']);
        $this->apiHandler->registerEndpoint('generate_image', [$this, 'handleImageGeneration']);
        $this->apiHandler->registerEndpoint('create_user', [$this, 'handleUserCreation']);
        $this->apiHandler->registerEndpoint('change_assigned_user', [$this, 'handleChangeUser']);
        $this->apiHandler->registerEndpoint('assign_user', [$this, 'handleAssignUser']);
        $this->apiHandler->registerEndpoint('save_settings', [$this, 'handleSettingsSave']);
        $this->apiHandler->registerEndpoint('repair_database', [$this, 'handleDatabaseRepair']);
        $this->apiHandler->registerEndpoint('test_api_provider', [$this, 'handleProviderApiTest']);
        $this->apiHandler->registerEndpoint('test_all_apis', [$this, 'handleTestAllApis']);
        $this->apiHandler->registerEndpoint('get_pages', [$this, 'handleGetPages']);
        $this->apiHandler->registerEndpoint('get_articles', [$this, 'handleGetArticles']);
        $this->apiHandler->registerEndpoint('preview_links', [$this, 'handleLinkPreview']);
        $this->apiHandler->registerEndpoint('save_api_key', [$this, 'handleApiKeySave']);
        $this->apiHandler->registerEndpoint('save_mou', [$this, 'handleMouSave']);
        $this->apiHandler->registerEndpoint('generate_mou', [$this, 'handleMouGeneration']);
        $this->apiHandler->registerEndpoint('generate_topics', [$this, 'handleTopicGeneration']);
        $this->apiHandler->registerEndpoint('compare_drafts', [$this, 'handleDraftComparison']);
        $this->apiHandler->registerEndpoint('save_page_balance', [$this, 'handlePageBalanceSave']);
        $this->apiHandler->registerEndpoint('get_page_balance', [$this, 'handlePageBalanceGet']);
        $this->apiHandler->registerEndpoint('get_image_placeholders', [$this, 'handleGetImagePlaceholders']);
        $this->apiHandler->registerEndpoint('generate_placeholder_image', [$this, 'handleGeneratePlaceholderImage']);
        $this->apiHandler->registerEndpoint('update_placeholder_selection', [$this, 'handleUpdatePlaceholderSelection']);
        $this->apiHandler->registerEndpoint('generate_multiple_drafts', [$this, 'handleGenerateMultipleDrafts']);
        $this->apiHandler->registerEndpoint('select_draft', [$this, 'handleSelectDraft']);
        $this->apiHandler->registerEndpoint('refresh_page_balance', [$this, 'handleRefreshPageBalance']);
        $this->apiHandler->registerEndpoint('generate_for_page', [$this, 'handleGenerateForPage']);
    }
    
    /**
     * Register admin tabs
     */
    protected function registerAdminTabs()
    {
        // Clear any default framework tabs first
        $this->adminUI->clearTabs();
        
        // Settings/Config tab (First - setup required)
        $this->adminUI->addTab('settings', [
            'title' => __('Settings', $this->config['text_domain']),
            'icon' => 'admin-settings',
            'callback' => [$this, 'renderSettingsTab']
        ]);
        
        // Keyword Mapping tab (Second - page analysis)
        $this->adminUI->addTab('keyword_mapping', [
            'title' => __('Keyword Mapping', $this->config['text_domain']),
            'icon' => 'admin-page',
            'callback' => [$this, 'renderKeywordMappingTab']
        ]);
        
        // Builder tab (Third - article generation)
        $this->adminUI->addTab('builder', [
            'title' => __('Builder', $this->config['text_domain']),
            'icon' => 'edit-large',
            'callback' => [$this, 'renderBuilderTab']
        ]);
        
        // Dashboard tab (Fourth - monitoring and overview)
        $this->adminUI->addTab('dashboard', [
            'title' => __('Dashboard', $this->config['text_domain']),
            'icon' => 'dashboard',
            'callback' => [$this, 'renderDashboardTab']
        ]);
        
        // Help tab
        $this->adminUI->addTab('help', [
            'title' => __('Help', $this->config['text_domain']),
            'icon' => 'editor-help',
            'callback' => [$this, 'renderHelpTab']
        ]);
        
        // About tab
        $this->adminUI->addTab('about', [
            'title' => __('About', $this->config['text_domain']),
            'icon' => 'info',
            'callback' => [$this, 'renderAboutTab']
        ]);
    }
    
    /**
     * Plugin initialization
     */
    public function init()
    {
        parent::init();
        
        // Ensure database handler has correct table names
        if ($this->databaseHandler) {
            $this->databaseHandler->refreshTables();
        }
        
        // Initialize custom functionality
        $this->initCustomFeatures();
        
        // Log initialization
        if ($this->databaseHandler && $this->databaseHandler->tableExists('logs')) {
            $this->databaseHandler->log('info', 'Blog Writer Plugin initialized', [
                'version' => $this->config['version'],
                'user_id' => get_current_user_id()
            ]);
        }
    }
    
    /**
     * Admin initialization
     */
    public function adminInit()
    {
        parent::adminInit();
        
        // Register custom settings
        $this->registerSettings();
        
        // Add custom admin notices
        $this->checkActivationNotice();
    }
    
    /**
     * Initialize custom features
     */
    protected function initCustomFeatures()
    {
        // Check for required user setup
        add_action('admin_init', [$this, 'checkUserSetup']);
        
        // Schedule daily cleanup
        if (!wp_next_scheduled($this->config['text_domain'] . '_daily_cleanup')) {
            wp_schedule_event(time(), 'daily', $this->config['text_domain'] . '_daily_cleanup');
        }
        
        add_action($this->config['text_domain'] . '_daily_cleanup', [$this, 'dailyCleanup']);
    }
    
    /**
     * Register plugin settings
     */
    protected function registerSettings()
    {
        register_setting(
            $this->config['text_domain'] . '_settings',
            $this->config['text_domain'] . '_settings',
            [
                'sanitize_callback' => [$this, 'sanitizeSettings'],
                'default' => [
                    'openrouter_api_key' => '',
                    'default_ai_model' => 'gpt-4',
                    'default_word_count' => 1000,
                    'assigned_user_id' => 0,
                    'default_prompts' => [
                        'main_content' => 'Write a comprehensive, SEO-optimized blog post about [TOPIC]. Include relevant headings and engaging content.',
                        'image_generation' => 'Create a professional image related to [TOPIC] for a blog post.',
                        'seo_optimization' => 'Focus on relevant keywords and provide value to readers.'
                    ]
                ]
            ]
        );
    }
    
    /**
     * Sanitize settings
     * 
     * @param array $settings
     * @return array
     */
    public function sanitizeSettings($settings)
    {
        $sanitized = [];
        
        $sanitized['openrouter_api_key'] = sanitize_text_field($settings['openrouter_api_key'] ?? '');
        $sanitized['default_ai_model'] = sanitize_text_field($settings['default_ai_model'] ?? 'gpt-4');
        $sanitized['default_word_count'] = absint($settings['default_word_count'] ?? 1000);
        $sanitized['assigned_user_id'] = absint($settings['assigned_user_id'] ?? 0);
        
        if (!empty($settings['default_prompts']) && is_array($settings['default_prompts'])) {
            $sanitized['default_prompts'] = [];
            foreach ($settings['default_prompts'] as $key => $value) {
                $sanitized['default_prompts'][sanitize_key($key)] = sanitize_textarea_field($value);
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Check for plugin updates
     */
    public function checkForUpdates()
    {
        if (Installer::needsUpdate()) {
            Installer::update();
        }
    }
    
    /**
     * Check user setup status
     */
    public function checkUserSetup()
    {
        $settings = get_option($this->config['text_domain'] . '_settings', []);
        if (empty($settings['assigned_user_id'])) {
            set_transient($this->config['text_domain'] . '_user_setup_needed', true, 300);
        }
    }
    
    /**
     * Show admin notices
     */
    public function showAdminNotices()
    {
        // Activation notice
        if (get_transient($this->config['text_domain'] . '_activation_notice')) {
            ?>
            <div class="notice notice-success is-dismissible">
                <p><?php 
                printf(
                    __('Welcome to %s! The plugin has been activated successfully.', $this->config['text_domain']),
                    '<strong>' . $this->config['plugin_name'] . '</strong>'
                ); 
                ?></p>
            </div>
            <?php
            delete_transient($this->config['text_domain'] . '_activation_notice');
        }
    }
    
    /**
     * Check for activation notice
     */
    protected function checkActivationNotice()
    {
        $activated = get_option($this->config['text_domain'] . '_activated');
        if ($activated && (time() - $activated) < 60) {
            set_transient($this->config['text_domain'] . '_activation_notice', true, 300);
        }
    }
    
    /**
     * Daily cleanup task
     */
    public function dailyCleanup()
    {
        if ($this->databaseHandler && $this->databaseHandler->tableExists('logs')) {
            // Clean up old logs (older than 30 days)
            $this->databaseHandler->clearLogs(['older_than_days' => 30]);
            
            // Log cleanup
            $this->databaseHandler->log('info', 'Daily cleanup completed', [
                'timestamp' => current_time('mysql')
            ]);
        }
    }
    
    /**
     * Render About tab content
     */
    public function renderAboutTab()
    {
        // Use the framework's default about content
        echo $this->adminUI->getDefaultAbout();
    }
    
    /**
     * Render Dashboard tab content
     */
    public function renderDashboardTab()
    {
        $user_setup = $this->userManager->getUserSetupStatus();
        $keyword_stats = $this->keywordExtractor->getExtractionStats();
        $article_stats = $this->articleGenerator->getGenerationStats();
        $linking_stats = $this->internalLinker->getLinkingStats();
        $image_stats = $this->imageGenerator->getImageStats();

        // Get page balance data
        $balance_summary = $this->databaseHandler->getPageBalanceSummary();
        $balance_insights = $this->databaseHandler->getPageBalanceInsights();
        $pages_needing_attention = $this->databaseHandler->getPagesNeedingAttention(5);
        
        ob_start();
        ?>
        <div class="vision-dashboard">
            <div class="vision-welcome-section">
                <h1><?php _e('Welcome to Blog Writer', $this->config['text_domain']); ?></h1>
                <p class="vision-subtitle"><?php _e('AI-powered blog post generation with intelligent internal linking and SEO optimization.', $this->config['text_domain']); ?></p>
            </div>
            
            <?php if (!$user_setup['setup_complete']): ?>
            <div class="vision-card vision-setup-card">
                <div class="vision-card-header">
                    <h2><?php _e('🚀 Setup Required', $this->config['text_domain']); ?></h2>
                    <p><?php _e('Complete the setup to start generating amazing content.', $this->config['text_domain']); ?></p>
                </div>
                
                <div class="vision-setup-workflow">
                    <?php foreach ($this->userManager->getWorkflowSteps() as $step): ?>
                    <div class="vision-workflow-step <?php echo $step['completed'] ? 'completed' : 'pending'; ?>">
                        <div class="vision-step-number"><?php echo $step['step']; ?></div>
                        <div class="vision-step-content">
                            <h3><?php echo esc_html($step['title']); ?></h3>
                            <p><?php echo esc_html($step['description']); ?></p>
                            <?php if (!$step['completed']): ?>
                            <button type="button" class="vision-button vision-button-primary" 
                                    data-action="<?php echo esc_attr($step['action']); ?>">
                                <?php _e('Complete Step', $this->config['text_domain']); ?>
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
            
            <div class="vision-dashboard-grid">
                <!-- User Status Card -->
                <div class="vision-card">
                    <div class="vision-card-header">
                        <h3><?php _e('👤 User Management', $this->config['text_domain']); ?></h3>
                    </div>
                    <div class="vision-card-content">
                        <?php if ($user_setup['assigned_user']): ?>
                        <div class="vision-user-info">
                            <img src="<?php echo esc_url($user_setup['assigned_user']['avatar_url']); ?>" 
                                 alt="Avatar" class="vision-avatar">
                            <div class="vision-user-details">
                                <strong><?php echo esc_html($user_setup['assigned_user']['display_name']); ?></strong>
                                <span class="vision-user-meta">
                                    <?php echo esc_html($user_setup['assigned_user']['username']); ?> • 
                                    <?php echo esc_html($user_setup['assigned_user']['role']); ?>
                                </span>
                                <div class="vision-user-stats">
                                    <?php printf(__('%d articles generated', $this->config['text_domain']), 
                                          $user_setup['assigned_user']['article_count']); ?>
                                </div>
                            </div>
                        </div>
                        <div class="vision-button-group">
                            <a href="<?php echo esc_url($user_setup['assigned_user']['edit_url']); ?>" 
                               class="vision-button vision-button-secondary">
                                <?php _e('Edit in WordPress', $this->config['text_domain']); ?>
                            </a>
                            <button type="button" class="vision-button vision-button-primary" 
                                    data-action="change_assigned_user">
                                <?php _e('Change User', $this->config['text_domain']); ?>
                            </button>
                        </div>
                        <?php else: ?>
                        <div class="vision-empty-state">
                            <p><?php _e('⚠️ Setup Required: Blog Writer requires a dedicated user to be assigned for article ownership. Please complete the setup below.', $this->config['text_domain']); ?></p>
                            <button type="button" class="vision-button vision-button-primary" 
                                    data-action="change_assigned_user">
                                <?php _e('Manage Blog Writer User', $this->config['text_domain']); ?>
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Page Balance Visualization -->
                <div class="vision-card">
                    <div class="vision-card-header">
                        <h3><?php _e('⚖️ Page Balance', $this->config['text_domain']); ?></h3>
                        <button type="button" class="vision-button-link refresh-balance" data-action="refresh_page_balance">
                            <?php _e('Refresh', $this->config['text_domain']); ?>
                        </button>
                    </div>
                    <div class="vision-card-content">
                        <div class="balance-visualization">
                            <div class="balance-summary">
                                <div class="balance-metric">
                                    <span class="metric-value <?php echo $balance_summary['content_balance'] < 50 ? 'warning' : ''; ?>">
                                        <?php echo $balance_summary['content_balance']; ?>%
                                    </span>
                                    <span class="metric-label"><?php _e('Content Balance', $this->config['text_domain']); ?></span>
                                </div>
                                <div class="balance-metric">
                                    <span class="metric-value"><?php echo $balance_summary['pages_covered']; ?></span>
                                    <span class="metric-label"><?php _e('Pages Covered', $this->config['text_domain']); ?></span>
                                </div>
                                <div class="balance-metric">
                                    <span class="metric-value <?php echo $balance_summary['needs_attention'] > 0 ? 'warning' : ''; ?>">
                                        <?php echo $balance_summary['needs_attention']; ?>
                                    </span>
                                    <span class="metric-label"><?php _e('Needs Attention', $this->config['text_domain']); ?></span>
                                </div>
                            </div>

                            <?php if (!empty($balance_summary['categories'])): ?>
                            <div class="balance-chart">
                                <div class="category-breakdown">
                                    <?php foreach ($balance_summary['categories'] as $category): ?>
                                    <div class="category-bar">
                                        <div class="category-info">
                                            <span class="category-name"><?php echo esc_html(ucfirst($category['service_category'])); ?></span>
                                            <span class="category-stats"><?php echo $category['total_articles']; ?> articles</span>
                                        </div>
                                        <div class="category-progress">
                                            <div class="progress-bar">
                                                <div class="progress-fill" style="width: <?php echo min(100, ($category['total_articles'] / max(1, $category['page_count']) * 20)); ?>%"></div>
                                            </div>
                                            <span class="progress-text"><?php echo $category['page_count']; ?> pages</span>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php else: ?>
                            <div class="balance-chart">
                                <div class="no-data-message">
                                    <p><?php _e('No page balance data available. Run a balance refresh to analyze your content distribution.', $this->config['text_domain']); ?></p>
                                    <button type="button" class="vision-button vision-button-primary" data-action="refresh_page_balance">
                                        <?php _e('Refresh Page Balance', $this->config['text_domain']); ?>
                                    </button>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="balance-insights">
                            <h4><?php _e('Balance Insights:', $this->config['text_domain']); ?></h4>
                            <?php if (!empty($balance_insights)): ?>
                            <ul>
                                <?php foreach ($balance_insights as $insight): ?>
                                <li class="<?php echo esc_attr($insight['type']); ?>">
                                    <?php echo esc_html($insight['message']); ?>
                                    <?php if ($insight['action']): ?>
                                    <button type="button" class="insight-action-btn"
                                            data-action="<?php echo esc_attr($insight['action']); ?>"
                                            data-category="<?php echo esc_attr($insight['category']); ?>">
                                        <?php _e('Take Action', $this->config['text_domain']); ?>
                                    </button>
                                    <?php endif; ?>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                            <?php else: ?>
                            <p><?php _e('No insights available. Generate some articles to see balance recommendations.', $this->config['text_domain']); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Article Metrics -->
                <div class="vision-card">
                    <div class="vision-card-header">
                        <h3><?php _e('📈 Article Metrics', $this->config['text_domain']); ?></h3>
                    </div>
                    <div class="vision-card-content">
                        <div class="article-metrics-grid">
                            <div class="metric-card">
                                <div class="metric-header">
                                    <span class="metric-icon">📄</span>
                                    <span class="metric-title"><?php _e('Total Articles', $this->config['text_domain']); ?></span>
                                </div>
                                <div class="metric-value"><?php echo $article_stats['total_articles']; ?></div>
                                <div class="metric-trend positive">+12% <?php _e('this month', $this->config['text_domain']); ?></div>
                            </div>
                            
                            <div class="metric-card">
                                <div class="metric-header">
                                    <span class="metric-icon">🔗</span>
                                    <span class="metric-title"><?php _e('Internal Links', $this->config['text_domain']); ?></span>
                                </div>
                                <div class="metric-value"><?php echo $linking_stats['total_internal_links']; ?></div>
                                <div class="metric-trend positive">+8% <?php _e('this month', $this->config['text_domain']); ?></div>
                            </div>
                            
                            <div class="metric-card">
                                <div class="metric-header">
                                    <span class="metric-icon">🖼️</span>
                                    <span class="metric-title"><?php _e('AI Images', $this->config['text_domain']); ?></span>
                                </div>
                                <div class="metric-value"><?php echo $image_stats['total_generated']; ?></div>
                                <div class="metric-trend neutral">+0% <?php _e('this month', $this->config['text_domain']); ?></div>
                            </div>
                            
                            <div class="metric-card">
                                <div class="metric-header">
                                    <span class="metric-icon">💰</span>
                                    <span class="metric-title"><?php _e('API Cost', $this->config['text_domain']); ?></span>
                                </div>
                                <div class="metric-value">$42.50</div>
                                <div class="metric-trend negative">+25% <?php _e('this month', $this->config['text_domain']); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions Card -->
                <div class="vision-card">
                    <div class="vision-card-header">
                        <h3><?php _e('⚡ Quick Actions', $this->config['text_domain']); ?></h3>
                    </div>
                    <div class="vision-card-content">
                        <div class="vision-action-grid">
                            <button type="button" class="vision-action-button" 
                                    data-action="extract_all_keywords">
                                <span class="vision-action-icon">🔍</span>
                                <span class="vision-action-text"><?php _e('Extract All Keywords', $this->config['text_domain']); ?></span>
                            </button>
                            <button type="button" class="vision-action-button" 
                                    data-action="generate_article" 
                                    onclick="VisionFramework.switchTab('article_builder')">
                                <span class="vision-action-icon">✍️</span>
                                <span class="vision-action-text"><?php _e('Generate Article', $this->config['text_domain']); ?></span>
                            </button>
                            <button type="button" class="vision-action-button" 
                                    data-action="test_api_connection">
                                <span class="vision-action-icon">🔗</span>
                                <span class="vision-action-text"><?php _e('Test API Connection', $this->config['text_domain']); ?></span>
                            </button>
                            <button type="button" class="vision-action-button" 
                                    onclick="VisionFramework.switchTab('settings')">
                                <span class="vision-action-icon">⚙️</span>
                                <span class="vision-action-text"><?php _e('Configure Settings', $this->config['text_domain']); ?></span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- System Status Card -->
                <div class="vision-card">
                    <div class="vision-card-header">
                        <h3><?php _e('🏥 System Status', $this->config['text_domain']); ?></h3>
                    </div>
                    <div class="vision-card-content">
                        <div class="vision-status-list">
                            <div class="vision-status-item <?php echo !empty($this->databaseHandler->getBlogSetting('openrouter_api_key')) ? 'status-good' : 'status-warning'; ?>">
                                <span class="vision-status-icon"></span>
                                <span class="vision-status-text"><?php _e('API Configuration', $this->config['text_domain']); ?></span>
                            </div>
                            <div class="vision-status-item <?php echo $this->databaseHandler->tableExists('pages') ? 'status-good' : 'status-error'; ?>">
                                <span class="vision-status-icon"></span>
                                <span class="vision-status-text"><?php _e('Database Tables', $this->config['text_domain']); ?></span>
                            </div>
                            <div class="vision-status-item <?php echo $user_setup['setup_complete'] ? 'status-good' : 'status-warning'; ?>">
                                <span class="vision-status-icon"></span>
                                <span class="vision-status-text"><?php _e('User Setup', $this->config['text_domain']); ?></span>
                            </div>
                            <div class="vision-status-item <?php echo $keyword_stats['extraction_coverage'] > 50 ? 'status-good' : 'status-warning'; ?>">
                                <span class="vision-status-icon"></span>
                                <span class="vision-status-text"><?php printf(__('Keyword Coverage (%s%%)', $this->config['text_domain']), $keyword_stats['extraction_coverage']); ?></span>
                            </div>
                        </div>
                        
                        <div class="vision-button-group">
                            <button type="button" class="vision-button vision-button-secondary" 
                                    data-action="repair_database">
                                <?php _e('Repair Database', $this->config['text_domain']); ?>
                            </button>
                            <button type="button" class="vision-button vision-button-secondary" 
                                    onclick="VisionFramework.switchTab('help')">
                                <?php _e('Get Help', $this->config['text_domain']); ?>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Pages Needing Attention -->
                <?php if (!empty($pages_needing_attention)): ?>
                <div class="vision-card vision-full-width">
                    <div class="vision-card-header">
                        <h3><?php _e('🎯 Pages Needing Attention', $this->config['text_domain']); ?></h3>
                        <p class="vision-card-subtitle"><?php _e('Underrepresented pages that could benefit from more content', $this->config['text_domain']); ?></p>
                    </div>
                    <div class="vision-card-content">
                        <div class="pages-attention-grid">
                            <?php foreach ($pages_needing_attention as $page): ?>
                            <div class="page-attention-card">
                                <div class="page-info">
                                    <h4 class="page-title"><?php echo esc_html($page['page_title']); ?></h4>
                                    <div class="page-meta">
                                        <span class="page-category"><?php echo esc_html(ucfirst($page['service_category'])); ?></span>
                                        <span class="page-articles"><?php echo $page['article_count']; ?>/<?php echo $page['target_article_count']; ?> articles</span>
                                    </div>
                                    <div class="page-priority">
                                        <span class="priority-label"><?php _e('Priority:', $this->config['text_domain']); ?></span>
                                        <div class="priority-bar">
                                            <div class="priority-fill" style="width: <?php echo $page['priority_score']; ?>%"></div>
                                        </div>
                                        <span class="priority-score"><?php echo round($page['priority_score']); ?>%</span>
                                    </div>
                                </div>
                                <div class="page-actions">
                                    <button type="button" class="vision-button vision-button-small vision-button-primary"
                                            data-action="generate_for_page"
                                            data-page-id="<?php echo esc_attr($page['page_id']); ?>"
                                            data-page-title="<?php echo esc_attr($page['page_title']); ?>">
                                        <?php _e('Generate Article', $this->config['text_domain']); ?>
                                    </button>
                                    <a href="<?php echo esc_url(get_edit_post_link($page['page_id'])); ?>"
                                       class="vision-button vision-button-small vision-button-secondary">
                                        <?php _e('Edit Page', $this->config['text_domain']); ?>
                                    </a>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="pages-attention-actions">
                            <button type="button" class="vision-button vision-button-secondary"
                                    data-action="refresh_page_balance">
                                <?php _e('Refresh Analysis', $this->config['text_domain']); ?>
                            </button>
                            <button type="button" class="vision-button vision-button-primary"
                                    onclick="VisionFramework.switchTab('builder')">
                                <?php _e('Generate Articles', $this->config['text_domain']); ?>
                            </button>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Recent Activity -->
                <div class="vision-card vision-full-width">
                    <div class="vision-card-header">
                        <h3><?php _e('📝 Recent Activity', $this->config['text_domain']); ?></h3>
                    </div>
                    <div class="vision-card-content">
                        <?php 
                        $recent_logs = $this->databaseHandler->getLogs([
                            'limit' => 10,
                            'order' => 'DESC'
                        ]);
                        ?>
                        <?php if (!empty($recent_logs)): ?>
                        <div class="vision-activity-list">
                            <?php foreach ($recent_logs as $log): ?>
                            <div class="vision-activity-item <?php echo esc_attr($log['level']); ?>">
                                <div class="vision-activity-content">
                                    <span class="vision-activity-message"><?php echo esc_html($log['message']); ?></span>
                                    <span class="vision-activity-time"><?php echo human_time_diff(strtotime($log['created_at'])); ?> <?php _e('ago', $this->config['text_domain']); ?></span>
                                </div>
                                <span class="vision-activity-level"><?php echo esc_html($log['level']); ?></span>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <div class="vision-empty-state">
                            <p><?php _e('No recent activity to display.', $this->config['text_domain']); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .vision-dashboard {
            max-width: 1200px;
        }
        
        .vision-welcome-section {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }
        
        .vision-welcome-section h1 {
            font-size: 2.5rem;
            color: var(--color-primary);
            margin-bottom: var(--spacing-sm);
        }
        
        .vision-subtitle {
            font-size: 1.2rem;
            color: var(--color-text-secondary);
            margin: 0;
        }
        
        .vision-setup-card {
            border-left: 4px solid var(--color-warning);
            background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
        }
        
        .vision-setup-workflow {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .vision-workflow-step {
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            background: var(--color-bg-secondary);
        }
        
        .vision-workflow-step.completed {
            background: var(--color-success-light);
            border: 1px solid var(--color-success);
        }
        
        .vision-step-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--color-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            flex-shrink: 0;
        }
        
        .vision-workflow-step.completed .vision-step-number {
            background: var(--color-success);
        }
        
        .vision-step-content h3 {
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--color-text-primary);
        }
        
        .vision-step-content p {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--color-text-secondary);
        }
        
        .vision-dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--spacing-lg);
        }
        
        .vision-full-width {
            grid-column: 1 / -1;
        }
        
        .vision-user-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }
        
        .vision-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            border: 2px solid var(--color-border);
        }
        
        .vision-user-details strong {
            display: block;
            color: var(--color-text-primary);
        }
        
        .vision-user-meta {
            font-size: 0.9rem;
            color: var(--color-text-secondary);
        }
        
        .vision-user-stats {
            font-size: 0.85rem;
            color: var(--color-primary);
            margin-top: var(--spacing-xs);
        }
        
        .vision-stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .vision-stat-item {
            text-align: center;
            padding: var(--spacing-md);
            background: var(--color-bg-secondary);
            border-radius: var(--radius-md);
        }
        
        .vision-stat-number {
            display: block;
            font-size: 24px;
            font-weight: 700;
            color: var(--color-primary);
        }
        
        .vision-stat-label {
            display: block;
            font-size: 12px;
            color: var(--color-text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: var(--spacing-xs);
        }
        
        .vision-action-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }
        
        .vision-action-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-md);
            background: var(--color-bg-secondary);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .vision-action-button:hover {
            background: var(--color-primary-light);
            border-color: var(--color-primary);
            transform: translateY(-2px);
        }
        
        .vision-action-icon {
            font-size: 24px;
        }
        
        .vision-action-text {
            font-size: 12px;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.5px;
            text-align: center;
        }
        
        .vision-status-list {
            margin-bottom: var(--spacing-md);
        }
        
        .vision-status-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-xs) 0;
        }
        
        .vision-status-icon {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--color-text-secondary);
        }
        
        .vision-status-item.status-good .vision-status-icon {
            background: var(--color-success);
        }
        
        .vision-status-item.status-warning .vision-status-icon {
            background: var(--color-warning);
        }
        
        .vision-status-item.status-error .vision-status-icon {
            background: var(--color-error);
        }
        
        .vision-activity-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .vision-activity-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--color-border);
        }
        
        .vision-activity-item:last-child {
            border-bottom: none;
        }
        
        .vision-activity-content {
            flex: 1;
        }
        
        .vision-activity-message {
            display: block;
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .vision-activity-time {
            font-size: 0.85rem;
            color: var(--color-text-secondary);
        }
        
        .vision-activity-level {
            padding: 2px 8px;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .vision-activity-level {
            background: var(--color-bg-secondary);
            color: var(--color-text-secondary);
        }
        
        .vision-activity-item.info .vision-activity-level {
            background: var(--color-primary-light);
            color: var(--color-primary);
        }
        
        .vision-activity-item.warning .vision-activity-level {
            background: var(--color-warning-light);
            color: var(--color-warning);
        }
        
        .vision-activity-item.error .vision-activity-level {
            background: var(--color-error-light);
            color: var(--color-error);
        }
        
        .vision-empty-state {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--color-text-secondary);
        }

        /* Page Balance Styles */
        .category-breakdown {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .category-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            background: var(--color-bg-secondary);
            border-radius: var(--radius-md);
        }

        .category-info {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }

        .category-name {
            font-weight: 600;
            color: var(--color-text-primary);
        }

        .category-stats {
            font-size: 0.85rem;
            color: var(--color-text-secondary);
        }

        .category-progress {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            min-width: 150px;
        }

        .progress-bar {
            width: 100px;
            height: 8px;
            background: var(--color-border);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: var(--color-primary);
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 0.8rem;
            color: var(--color-text-secondary);
            white-space: nowrap;
        }

        .balance-insights ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .balance-insights li {
            padding: var(--spacing-sm);
            margin-bottom: var(--spacing-xs);
            border-radius: var(--radius-sm);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .balance-insights li.success {
            background: var(--color-success-light);
            color: var(--color-success);
        }

        .balance-insights li.warning {
            background: var(--color-warning-light);
            color: var(--color-warning);
        }

        .balance-insights li.error {
            background: var(--color-error-light);
            color: var(--color-error);
        }

        .insight-action-btn {
            padding: var(--spacing-xs) var(--spacing-sm);
            border: 1px solid currentColor;
            background: transparent;
            color: inherit;
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .insight-action-btn:hover {
            background: currentColor;
            color: white;
        }

        .no-data-message {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--color-text-secondary);
        }

        /* Pages Needing Attention Styles */
        .pages-attention-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .page-attention-card {
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            background: var(--color-bg);
            transition: all 0.2s ease;
        }

        .page-attention-card:hover {
            border-color: var(--color-primary);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .page-info {
            margin-bottom: var(--spacing-md);
        }

        .page-title {
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--color-text-primary);
            font-size: 1.1rem;
        }

        .page-meta {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
        }

        .page-category {
            padding: 2px 8px;
            background: var(--color-primary-light);
            color: var(--color-primary);
            border-radius: var(--radius-sm);
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .page-articles {
            font-size: 0.9rem;
            color: var(--color-text-secondary);
        }

        .page-priority {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .priority-label {
            font-size: 0.85rem;
            color: var(--color-text-secondary);
        }

        .priority-bar {
            flex: 1;
            height: 6px;
            background: var(--color-border);
            border-radius: 3px;
            overflow: hidden;
        }

        .priority-fill {
            height: 100%;
            background: var(--color-warning);
            transition: width 0.3s ease;
        }

        .priority-score {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--color-text-primary);
        }

        .page-actions {
            display: flex;
            gap: var(--spacing-xs);
        }

        .pages-attention-actions {
            display: flex;
            justify-content: center;
            gap: var(--spacing-md);
            padding-top: var(--spacing-lg);
            border-top: 1px solid var(--color-border);
        }

        @media (max-width: 768px) {
            .vision-dashboard-grid {
                grid-template-columns: 1fr;
            }

            .vision-action-grid {
                grid-template-columns: 1fr;
            }

            .vision-stats-grid {
                grid-template-columns: 1fr;
            }

            .pages-attention-grid {
                grid-template-columns: 1fr;
            }

            .category-bar {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-sm);
            }

            .category-progress {
                width: 100%;
                justify-content: space-between;
            }
        }
        </style>
        <?php
        echo ob_get_clean();
    }
    /**
     * Render Keyword Mapping tab content
     */
    public function renderKeywordMappingTab()
    {
        $pages_data = $this->keywordExtractor->getPagesForExtraction();
        $extraction_stats = $this->keywordExtractor->getExtractionStats();
        $active_mou = $this->databaseHandler->getActiveMoU();
        $setup_complete = !empty($this->databaseHandler->getBlogSetting('assigned_user_id', 0)) && !empty($active_mou);
        
        ob_start();
        ?>
        <div class="vision-page-keywords">
            <div class="vision-page-header">
                <h1><?php _e('Keyword Mapping', $this->config['text_domain']); ?></h1>
                <p class="vision-subtitle"><?php _e('AI-powered keyword extraction with site context awareness. Generate SEO-relevant keywords for intelligent internal linking.', $this->config['text_domain']); ?></p>
            </div>
            
            <?php if (!$setup_complete): ?>
            <div class="vision-warning-card">
                <h3><?php _e('⚠️ Setup Required', $this->config['text_domain']); ?></h3>
                <p><?php _e('Please complete the Settings configuration (author assignment and site MoU) before extracting keywords for best results.', $this->config['text_domain']); ?></p>
                <button type="button" class="vision-button vision-button-primary" onclick="VisionFramework.switchTab('settings')">
                    <?php _e('Complete Setup', $this->config['text_domain']); ?>
                </button>
            </div>
            <?php endif; ?>
            
            <!-- MoU Context Display -->
            <?php if ($active_mou): ?>
            <div class="vision-card mou-context">
                <div class="vision-card-header">
                    <h3><?php _e('📋 Site Context', $this->config['text_domain']); ?></h3>
                    <button type="button" class="vision-button-link" onclick="VisionFramework.switchTab('settings')">
                        <?php _e('Edit MoU', $this->config['text_domain']); ?>
                    </button>
                </div>
                <div class="vision-card-content">
                    <div class="mou-preview">
                        <?php echo esc_html(wp_trim_words($active_mou, 25)); ?>
                        <?php if (str_word_count($active_mou) > 25): ?>
                        <span class="mou-more">... <button type="button" class="vision-button-link show-full-mou"><?php _e('Show full', $this->config['text_domain']); ?></button></span>
                        <div class="mou-full" style="display: none;"><?php echo esc_html($active_mou); ?></div>
                        <?php endif; ?>
                    </div>
                    <small class="mou-help"><?php _e('This context helps AI generate more relevant, location-specific keywords.', $this->config['text_domain']); ?></small>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Enhanced Stats and Controls -->
            <div class="vision-controls-section">
                <div class="vision-stats-summary">
                    <div class="vision-stat">
                        <span class="vision-stat-number"><?php echo $extraction_stats['total_pages']; ?></span>
                        <span class="vision-stat-label"><?php _e('Total Pages', $this->config['text_domain']); ?></span>
                    </div>
                    <div class="vision-stat">
                        <span class="vision-stat-number"><?php echo $extraction_stats['pages_with_keywords']; ?></span>
                        <span class="vision-stat-label"><?php _e('With Keywords', $this->config['text_domain']); ?></span>
                    </div>
                    <div class="vision-stat">
                        <span class="vision-stat-number"><?php echo $extraction_stats['extraction_coverage']; ?>%</span>
                        <span class="vision-stat-label"><?php _e('Coverage', $this->config['text_domain']); ?></span>
                    </div>
                    <div class="vision-stat priority">
                        <span class="vision-stat-number"><?php echo $extraction_stats['needs_attention']; ?></span>
                        <span class="vision-stat-label"><?php _e('Needs Attention', $this->config['text_domain']); ?></span>
                    </div>
                </div>
                
                <!-- AI Configuration Panel -->
                <div class="ai-config-panel">
                    <div class="ai-config-header">
                        <h4><?php _e('🤖 AI Configuration', $this->config['text_domain']); ?></h4>
                        <span class="context-indicator <?php echo $active_mou ? 'active' : 'inactive'; ?>">
                            <?php echo $active_mou ? '✓ Context Aware' : '⚠ No Context'; ?>
                        </span>
                    </div>
                    
                    <div class="vision-controls">
                        <div class="vision-control-group">
                            <label for="extraction-model"><?php _e('AI Model:', $this->config['text_domain']); ?></label>
                            <select id="extraction-model" class="vision-select">
                                <option value="gpt-3.5-turbo"><?php _e('GPT-3.5 Turbo', $this->config['text_domain']); ?> <span class="model-badge fast">Fast</span></option>
                                <option value="gpt-4" selected><?php _e('GPT-4', $this->config['text_domain']); ?> <span class="model-badge recommended">Recommended</span></option>
                                <option value="claude-3-sonnet"><?php _e('Claude 3.5 Sonnet', $this->config['text_domain']); ?> <span class="model-badge quality">Quality</span></option>
                                <option value="claude-3-haiku"><?php _e('Claude 3 Haiku', $this->config['text_domain']); ?> <span class="model-badge budget">Budget</span></option>
                            </select>
                        </div>
                        
                        <div class="vision-control-group">
                            <label for="keyword-count"><?php _e('Keywords per Page:', $this->config['text_domain']); ?></label>
                            <input type="range" id="keyword-count" class="vision-range" 
                                   min="5" max="25" value="15" step="1">
                            <span class="range-value">15</span>
                        </div>
                        
                        <div class="vision-control-group">
                            <label for="extraction-focus"><?php _e('Extraction Focus:', $this->config['text_domain']); ?></label>
                            <select id="extraction-focus" class="vision-select">
                                <option value="balanced"><?php _e('Balanced SEO + Semantic', $this->config['text_domain']); ?></option>
                                <option value="seo"><?php _e('SEO-Focused Keywords', $this->config['text_domain']); ?></option>
                                <option value="semantic"><?php _e('Semantic Relationships', $this->config['text_domain']); ?></option>
                                <option value="local"><?php _e('Local + Geographic', $this->config['text_domain']); ?></option>
                                <option value="industry"><?php _e('Industry-Specific', $this->config['text_domain']); ?></option>
                            </select>
                        </div>
                        
                        <div class="vision-control-group">
                            <label><?php _e('Options:', $this->config['text_domain']); ?></label>
                            <div class="checkbox-group">
                                <label><input type="checkbox" id="include-variations" checked> <?php _e('Include variations', $this->config['text_domain']); ?></label>
                                <label><input type="checkbox" id="location-aware" <?php echo $active_mou ? 'checked' : ''; ?>> <?php _e('Location-aware', $this->config['text_domain']); ?></label>
                                <label><input type="checkbox" id="competitor-analysis"> <?php _e('Competitor analysis', $this->config['text_domain']); ?></label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="vision-bulk-actions">
                    <button type="button" class="vision-button vision-button-primary extract-all-btn" 
                            data-action="extract_all_pages" <?php echo !$setup_complete ? 'disabled' : ''; ?>>
                        <?php _e('Extract All Pages', $this->config['text_domain']); ?>
                    </button>
                    <button type="button" class="vision-button vision-button-secondary extract-selected-btn" 
                            data-action="extract_selected" <?php echo !$setup_complete ? 'disabled' : ''; ?>>
                        <?php _e('Extract Selected', $this->config['text_domain']); ?>
                    </button>
                    <button type="button" class="vision-button vision-button-secondary refresh-pages-btn" 
                            data-action="refresh_pages">
                        <?php _e('Refresh Pages', $this->config['text_domain']); ?>
                    </button>
                    <button type="button" class="vision-button vision-button-secondary regenerate-low-btn" 
                            data-action="regenerate_low_coverage">
                        <?php _e('Regenerate Low Coverage', $this->config['text_domain']); ?>
                    </button>
                </div>
            </div>
            
            <!-- Pages Table -->
            <div class="vision-card">
                <div class="vision-card-header">
                    <h3><?php _e('WordPress Pages', $this->config['text_domain']); ?></h3>
                    <div class="vision-table-controls">
                        <input type="text" id="pages-search" class="vision-input" 
                               placeholder="<?php esc_attr_e('Search pages...', $this->config['text_domain']); ?>">
                        <select id="status-filter" class="vision-select">
                            <option value="all"><?php _e('All Statuses', $this->config['text_domain']); ?></option>
                            <option value="publish"><?php _e('Published', $this->config['text_domain']); ?></option>
                            <option value="draft"><?php _e('Draft', $this->config['text_domain']); ?></option>
                        </select>
                    </div>
                </div>
                
                <div class="vision-table-container">
                    <table class="vision-table" id="pages-table">
                        <thead>
                            <tr>
                                <th class="vision-table-checkbox">
                                    <input type="checkbox" id="select-all-pages">
                                </th>
                                <th><?php _e('Page Title', $this->config['text_domain']); ?></th>
                                <th><?php _e('Status', $this->config['text_domain']); ?></th>
                                <th><?php _e('Word Count', $this->config['text_domain']); ?></th>
                                <th><?php _e('Keywords', $this->config['text_domain']); ?></th>
                                <th><?php _e('Last Scan', $this->config['text_domain']); ?></th>
                                <th><?php _e('Actions', $this->config['text_domain']); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pages_data as $page): ?>
                            <tr class="page-row <?php echo $page['status']; ?>" 
                                data-page-id="<?php echo esc_attr($page['id']); ?>">
                                <td class="vision-table-checkbox">
                                    <input type="checkbox" class="page-checkbox" 
                                           value="<?php echo esc_attr($page['id']); ?>"
                                           <?php echo $page['status'] === 'publish' ? '' : 'disabled'; ?>>
                                </td>
                                <td class="page-title">
                                    <strong><?php echo esc_html($page['title']); ?></strong>
                                    <div class="page-url">
                                        <a href="<?php echo esc_url($page['url']); ?>" target="_blank">
                                            <?php echo esc_html(parse_url($page['url'], PHP_URL_PATH)); ?>
                                        </a>
                                    </div>
                                </td>
                                <td>
                                    <span class="vision-status-badge status-<?php echo esc_attr($page['status']); ?>">
                                        <?php echo esc_html(ucfirst($page['status'])); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="word-count"><?php echo number_format($page['word_count']); ?></span>
                                </td>
                                <td class="keywords-cell">
                                    <?php if ($page['has_keywords']): ?>
                                    <div class="keywords-preview" 
                                         title="<?php echo esc_attr($page['keywords']); ?>">
                                        <?php 
                                        $keywords = explode(',', $page['keywords']);
                                        $display_keywords = array_slice($keywords, 0, 3);
                                        echo esc_html(implode(', ', array_map('trim', $display_keywords)));
                                        if (count($keywords) > 3) {
                                            echo ' <span class="more-keywords">+' . (count($keywords) - 3) . '</span>';
                                        }
                                        ?>
                                    </div>
                                    <button type="button" class="vision-button-link edit-keywords" 
                                            data-page-id="<?php echo esc_attr($page['id']); ?>">
                                        <?php _e('Edit', $this->config['text_domain']); ?>
                                    </button>
                                    <?php else: ?>
                                    <span class="no-keywords"><?php _e('No keywords extracted', $this->config['text_domain']); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($page['last_scan']): ?>
                                    <span class="last-scan" title="<?php echo esc_attr($page['last_scan']); ?>">
                                        <?php echo human_time_diff(strtotime($page['last_scan'])); ?> <?php _e('ago', $this->config['text_domain']); ?>
                                    </span>
                                    <?php else: ?>
                                    <span class="never-scanned"><?php _e('Never', $this->config['text_domain']); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td class="actions-cell">
                                    <div class="vision-button-group">
                                        <button type="button" class="vision-button vision-button-small" 
                                                data-action="extract_single" 
                                                data-page-id="<?php echo esc_attr($page['id']); ?>"
                                                <?php echo $page['status'] !== 'publish' ? 'disabled' : ''; ?>>
                                            <?php _e('Extract', $this->config['text_domain']); ?>
                                        </button>
                                        <button type="button" class="vision-button vision-button-small vision-button-secondary" 
                                                data-action="view_page" 
                                                data-url="<?php echo esc_attr($page['url']); ?>">
                                            <?php _e('View', $this->config['text_domain']); ?>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <?php if (empty($pages_data)): ?>
                <div class="vision-empty-state">
                    <p><?php _e('No pages found. Create some pages to get started with keyword extraction.', $this->config['text_domain']); ?></p>
                    <a href="<?php echo admin_url('post-new.php?post_type=page'); ?>" 
                       class="vision-button vision-button-primary">
                        <?php _e('Create New Page', $this->config['text_domain']); ?>
                    </a>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Progress Modal -->
            <div id="extraction-progress-modal" class="vision-modal" style="display: none;">
                <div class="vision-modal-content">
                    <div class="vision-modal-header">
                        <h3><?php _e('Extracting Keywords', $this->config['text_domain']); ?></h3>
                    </div>
                    <div class="vision-modal-body">
                        <div class="vision-progress-container">
                            <div class="vision-progress-bar">
                                <div class="vision-progress-fill" style="width: 0%"></div>
                            </div>
                            <div class="vision-progress-text">
                                <span id="progress-current">0</span> / <span id="progress-total">0</span> pages processed
                            </div>
                        </div>
                        <div id="progress-details" class="vision-progress-details">
                            <div class="current-page">Processing: <span id="current-page-title">-</span></div>
                        </div>
                    </div>
                    <div class="vision-modal-footer">
                        <button type="button" id="cancel-extraction" class="vision-button vision-button-secondary">
                            <?php _e('Cancel', $this->config['text_domain']); ?>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Edit Keywords Modal -->
            <div id="edit-keywords-modal" class="vision-modal" style="display: none;">
                <div class="vision-modal-content">
                    <div class="vision-modal-header">
                        <h3><?php _e('Edit Keywords', $this->config['text_domain']); ?></h3>
                        <button type="button" class="vision-modal-close">&times;</button>
                    </div>
                    <div class="vision-modal-body">
                        <div class="vision-form-group">
                            <label for="edit-page-title"><?php _e('Page:', $this->config['text_domain']); ?></label>
                            <input type="text" id="edit-page-title" class="vision-input" readonly>
                        </div>
                        <div class="vision-form-group">
                            <label for="edit-keywords-text"><?php _e('Keywords (comma-separated):', $this->config['text_domain']); ?></label>
                            <textarea id="edit-keywords-text" class="vision-textarea" rows="6"
                                      placeholder="<?php esc_attr_e('Enter keywords separated by commas...', $this->config['text_domain']); ?>"></textarea>
                            <div class="vision-form-help">
                                <?php _e('Enter relevant SEO keywords that describe this page. Use phrases that potential visitors might search for.', $this->config['text_domain']); ?>
                            </div>
                        </div>
                    </div>
                    <div class="vision-modal-footer">
                        <button type="button" class="vision-button vision-button-secondary" data-action="close-modal">
                            <?php _e('Cancel', $this->config['text_domain']); ?>
                        </button>
                        <button type="button" class="vision-button vision-button-primary" data-action="save-keywords">
                            <?php _e('Save Keywords', $this->config['text_domain']); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .vision-page-keywords {
            max-width: 1400px;
        }
        
        .vision-page-header {
            margin-bottom: var(--spacing-xl);
        }
        
        .vision-page-header h1 {
            color: var(--color-primary);
            margin-bottom: var(--spacing-sm);
        }
        
        .vision-controls-section {
            background: var(--color-bg-secondary);
            padding: var(--spacing-lg);
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-lg);
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-lg);
            align-items: center;
        }
        
        .vision-stats-summary {
            display: flex;
            gap: var(--spacing-lg);
        }
        
        .vision-stat {
            text-align: center;
        }
        
        .vision-stat-number {
            display: block;
            font-size: 28px;
            font-weight: 700;
            color: var(--color-primary);
        }
        
        .vision-stat-label {
            display: block;
            font-size: 12px;
            color: var(--color-text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .vision-controls {
            display: flex;
            gap: var(--spacing-md);
            flex: 1;
        }
        
        .vision-control-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }
        
        .vision-control-group label {
            font-size: 12px;
            font-weight: 600;
            color: var(--color-text-secondary);
            text-transform: uppercase;
        }
        
        .vision-bulk-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .vision-table-controls {
            display: flex;
            gap: var(--spacing-md);
            align-items: center;
        }
        
        .vision-table-container {
            overflow-x: auto;
        }
        
        .vision-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        
        .vision-table th,
        .vision-table td {
            padding: var(--spacing-sm) var(--spacing-md);
            text-align: left;
            border-bottom: 1px solid var(--color-border);
        }
        
        .vision-table th {
            background: var(--color-bg-secondary);
            font-weight: 600;
            color: var(--color-text-secondary);
            text-transform: uppercase;
            font-size: 11px;
            letter-spacing: 0.5px;
        }
        
        .vision-table-checkbox {
            width: 40px;
            text-align: center;
        }
        
        .page-row.draft {
            opacity: 0.6;
        }
        
        .page-title strong {
            color: var(--color-text-primary);
        }
        
        .page-url {
            font-size: 12px;
            margin-top: var(--spacing-xs);
        }
        
        .page-url a {
            color: var(--color-text-secondary);
            text-decoration: none;
        }
        
        .page-url a:hover {
            color: var(--color-primary);
        }
        
        .vision-status-badge {
            padding: 2px 8px;
            border-radius: var(--radius-sm);
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-publish {
            background: var(--color-success-light);
            color: var(--color-success);
        }
        
        .status-draft {
            background: var(--color-warning-light);
            color: var(--color-warning);
        }
        
        .word-count {
            font-family: monospace;
            color: var(--color-text-secondary);
        }
        
        .keywords-cell {
            max-width: 200px;
        }
        
        .keywords-preview {
            font-size: 12px;
            color: var(--color-text-primary);
            line-height: 1.4;
            margin-bottom: var(--spacing-xs);
        }
        
        .more-keywords {
            color: var(--color-primary);
            font-weight: 600;
        }
        
        .vision-button-link {
            background: none;
            border: none;
            color: var(--color-primary);
            cursor: pointer;
            font-size: 12px;
            text-decoration: underline;
        }
        
        .vision-button-link:hover {
            color: var(--color-primary-dark);
        }
        
        .no-keywords,
        .never-scanned {
            color: var(--color-text-secondary);
            font-style: italic;
            font-size: 12px;
        }
        
        .last-scan {
            font-size: 12px;
            color: var(--color-text-secondary);
        }
        
        .actions-cell {
            white-space: nowrap;
        }
        
        .vision-button-small {
            padding: 4px 8px;
            font-size: 11px;
        }
        
        .vision-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .vision-modal-content {
            background: var(--color-bg-primary);
            border-radius: var(--radius-lg);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .vision-modal-header {
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--color-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .vision-modal-header h3 {
            margin: 0;
            color: var(--color-primary);
        }
        
        .vision-modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--color-text-secondary);
        }
        
        .vision-modal-body {
            padding: var(--spacing-lg);
        }
        
        .vision-modal-footer {
            padding: var(--spacing-lg);
            border-top: 1px solid var(--color-border);
            display: flex;
            justify-content: flex-end;
            gap: var(--spacing-sm);
        }
        
        .vision-progress-container {
            margin-bottom: var(--spacing-lg);
        }
        
        .vision-progress-bar {
            width: 100%;
            height: 12px;
            background: var(--color-bg-secondary);
            border-radius: var(--radius-md);
            overflow: hidden;
        }
        
        .vision-progress-fill {
            height: 100%;
            background: var(--color-primary);
            transition: width 0.3s ease;
        }
        
        .vision-progress-text {
            text-align: center;
            margin-top: var(--spacing-sm);
            color: var(--color-text-secondary);
        }
        
        .vision-progress-details {
            margin-top: var(--spacing-md);
            padding: var(--spacing-md);
            background: var(--color-bg-secondary);
            border-radius: var(--radius-md);
        }
        
        .current-page {
            font-size: 14px;
            color: var(--color-text-primary);
        }
        
        .vision-form-group {
            margin-bottom: var(--spacing-lg);
        }
        
        .vision-form-group label {
            display: block;
            margin-bottom: var(--spacing-xs);
            font-weight: 600;
            color: var(--color-text-primary);
        }
        
        .vision-textarea {
            width: 100%;
            min-height: 120px;
            padding: var(--spacing-sm);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
        }
        
        .vision-form-help {
            margin-top: var(--spacing-xs);
            font-size: 12px;
            color: var(--color-text-secondary);
        }
        
        @media (max-width: 768px) {
            .vision-controls-section {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .vision-controls {
                flex-direction: column;
                width: 100%;
            }
            
            .vision-bulk-actions {
                flex-direction: column;
                width: 100%;
            }
            
            .vision-table-controls {
                flex-direction: column;
                align-items: stretch;
            }
        }
        </style>
        
        <script>
        // Page Keywords JavaScript functionality will be added via admin.js
        </script>
        <?php
        echo ob_get_clean();
    }
    /**
     * Render Builder tab content
     */
    public function renderBuilderTab()
    {
        $available_models = $this->articleGenerator->getAvailableModels();
        $user_setup = $this->userManager->getUserSetupStatus();
        $active_mou = $this->databaseHandler->getActiveMoU();
        $pages_with_keywords = $this->databaseHandler->getAllPages(['status' => 'publish']);
        $setup_complete = !empty($this->databaseHandler->getBlogSetting('assigned_user_id', 0)) && !empty($active_mou);
        
        // Filter pages that have keywords for better targeting
        $pages_with_keywords = array_filter($pages_with_keywords, function($page) {
            return !empty($page['keywords']);
        });
        
        $default_prompts = [
            'main_content' => $this->databaseHandler->getBlogSetting('main_content_prompt', ''),
            'seo_optimization' => $this->databaseHandler->getBlogSetting('seo_optimization_prompt', ''),
            'image_generation' => $this->databaseHandler->getBlogSetting('image_generation_prompt', '')
        ];
        
        ob_start();
        ?>
        <div class="vision-article-builder">
            <div class="vision-page-header">
                <h1><?php _e('Article Builder', $this->config['text_domain']); ?></h1>
                <p class="vision-subtitle"><?php _e('Page-centric AI article generation with multi-model comparison and intelligent image placement.', $this->config['text_domain']); ?></p>
            </div>
            
            <?php if (!$setup_complete): ?>
            <div class="vision-warning-card">
                <h3><?php _e('⚠️ Setup Required', $this->config['text_domain']); ?></h3>
                <p><?php _e('Please complete the Settings configuration (author assignment and site MoU) before generating articles.', $this->config['text_domain']); ?></p>
                <button type="button" class="vision-button vision-button-primary" 
                        onclick="VisionFramework.switchTab('settings')">
                    <?php _e('Complete Setup', $this->config['text_domain']); ?>
                </button>
            </div>
            <?php endif; ?>
            
            <?php if (empty($pages_with_keywords)): ?>
            <div class="vision-info-card">
                <h3><?php _e('📋 No Pages with Keywords', $this->config['text_domain']); ?></h3>
                <p><?php _e('To enable intelligent internal linking, you need to extract keywords from your pages in the Keyword Mapping tab.', $this->config['text_domain']); ?></p>
                <button type="button" class="vision-button vision-button-primary" 
                        onclick="VisionFramework.switchTab('keyword_mapping')">
                    <?php _e('Extract Keywords', $this->config['text_domain']); ?>
                </button>
            </div>
            <?php endif; ?>
            
            <div class="vision-builder-workflow">
                <!-- Step 1: Topic and Parameters -->
                <div class="vision-step-card active" id="step-1">
                    <div class="vision-step-header">
                        <div class="vision-step-number">1</div>
                        <h3><?php _e('Topic & Parameters', $this->config['text_domain']); ?></h3>
                        <div class="vision-step-status"></div>
                    </div>
                    
                    <div class="vision-step-content">
                        <div class="vision-form-grid">
                            <!-- Page-Centric Selection -->
                            <div class="vision-form-group">
                                <label for="target-page"><?php _e('Target Page for Internal Linking:', $this->config['text_domain']); ?></label>
                                <select id="target-page" class="vision-select">
                                    <option value=""><?php _e('Select a page to target for internal linking', $this->config['text_domain']); ?></option>
                                    <?php foreach ($pages_with_keywords as $page): ?>
                                    <option value="<?php echo esc_attr($page['page_id']); ?>" 
                                            data-keywords="<?php echo esc_attr($page['keywords']); ?>">
                                        <?php echo esc_html($page['page_title']); ?> 
                                        (<?php echo esc_html(wp_trim_words($page['keywords'], 5)); ?>)
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="vision-form-help">
                                    <?php _e('Select a page to target for internal linking. The AI will generate content that naturally connects to this page.', $this->config['text_domain']); ?>
                                </div>
                            </div>
                            
                            <div class="vision-form-group">
                                <label for="article-topic"><?php _e('Article Topic or Theme:', $this->config['text_domain']); ?></label>
                                <input type="text" id="article-topic" class="vision-input" 
                                       placeholder="<?php esc_attr_e('e.g., Commercial Plumbing Solutions for Office Buildings', $this->config['text_domain']); ?>">
                                <div class="vision-form-help">
                                    <?php _e('Describe the main topic or theme for your article. Be specific for better results.', $this->config['text_domain']); ?>
                                </div>
                            </div>
                            
                            <div class="vision-form-row">
                                <div class="vision-form-group">
                                    <label for="word-count"><?php _e('Target Word Count:', $this->config['text_domain']); ?></label>
                                    <select id="word-count" class="vision-select">
                                        <option value="500">500 words</option>
                                        <option value="750">750 words</option>
                                        <option value="1000" selected>1000 words</option>
                                        <option value="1500">1500 words</option>
                                        <option value="2000">2000 words</option>
                                        <option value="custom">Custom...</option>
                                    </select>
                                </div>
                                
                                <div class="vision-form-group">
                                    <label for="article-tone"><?php _e('Tone & Style:', $this->config['text_domain']); ?></label>
                                    <select id="article-tone" class="vision-select">
                                        <option value="professional"><?php _e('Professional', $this->config['text_domain']); ?></option>
                                        <option value="conversational"><?php _e('Conversational', $this->config['text_domain']); ?></option>
                                        <option value="authoritative"><?php _e('Authoritative', $this->config['text_domain']); ?></option>
                                        <option value="friendly"><?php _e('Friendly', $this->config['text_domain']); ?></option>
                                        <option value="technical"><?php _e('Technical', $this->config['text_domain']); ?></option>
                                    </select>
                                </div>
                                
                                <div class="vision-form-group">
                                    <label for="content-structure"><?php _e('Structure:', $this->config['text_domain']); ?></label>
                                    <select id="content-structure" class="vision-select">
                                        <option value="standard"><?php _e('Standard (H1, H2, Conclusion)', $this->config['text_domain']); ?></option>
                                        <option value="detailed"><?php _e('Detailed (H1, H2, H3, FAQ)', $this->config['text_domain']); ?></option>
                                        <option value="guide"><?php _e('How-to Guide', $this->config['text_domain']); ?></option>
                                        <option value="comparison"><?php _e('Comparison Article', $this->config['text_domain']); ?></option>
                                    </select>
                                </div>
                                
                                <div class="vision-form-group">
                                    <label for="article-tone"><?php _e('Tone & Style:', $this->config['text_domain']); ?></label>
                                    <select id="article-tone" class="vision-select">
                                        <option value="professional">Professional</option>
                                        <option value="conversational">Conversational</option>
                                        <option value="academic">Academic</option>
                                        <option value="casual">Casual</option>
                                        <option value="authoritative">Authoritative</option>
                                    </select>
                                </div>
                                
                                <div class="vision-form-group">
                                    <label for="target-audience"><?php _e('Target Audience:', $this->config['text_domain']); ?></label>
                                    <select id="target-audience" class="vision-select">
                                        <option value="general">General Public</option>
                                        <option value="professionals">Industry Professionals</option>
                                        <option value="beginners">Beginners</option>
                                        <option value="experts">Experts</option>
                                        <option value="business-owners">Business Owners</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="vision-form-group">
                                <label for="additional-instructions"><?php _e('Additional Instructions (Optional):', $this->config['text_domain']); ?></label>
                                <textarea id="additional-instructions" class="vision-textarea" rows="3"
                                          placeholder="<?php esc_attr_e('Any specific requirements, keywords to include, or style preferences...', $this->config['text_domain']); ?>"></textarea>
                            </div>
                            
                            <!-- MoU Context Display -->
                            <?php if ($active_mou): ?>
                            <div class="mou-context-preview">
                                <h4><?php _e('📋 Site Context (MoU)', $this->config['text_domain']); ?></h4>
                                <div class="mou-preview">
                                    <?php echo esc_html(wp_trim_words($active_mou, 15)); ?>
                                    <button type="button" class="vision-button-link" onclick="VisionFramework.switchTab('settings')">
                                        <?php _e('Edit MoU', $this->config['text_domain']); ?>
                                    </button>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <!-- Global Settings Override -->
                            <div class="global-settings-override">
                                <h4><?php _e('⚙️ Override Global Settings', $this->config['text_domain']); ?></h4>
                                <div class="checkbox-group">
                                    <label><input type="checkbox" id="override-seo"> <?php _e('Override SEO settings', $this->config['text_domain']); ?></label>
                                    <label><input type="checkbox" id="override-image-style"> <?php _e('Override image style', $this->config['text_domain']); ?></label>
                                    <label><input type="checkbox" id="include-toc"> <?php _e('Include table of contents', $this->config['text_domain']); ?></label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="vision-step-actions">
                            <button type="button" class="vision-button vision-button-primary" 
                                    data-action="generate-topics">
                                <?php _e('Generate Topic Suggestions', $this->config['text_domain']); ?>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Step 2: AI Model Competition -->
                <div class="vision-step-card" id="step-2">
                    <div class="vision-step-header">
                        <div class="vision-step-number">2</div>
                        <h3><?php _e('AI Model Competition', $this->config['text_domain']); ?></h3>
                        <div class="vision-step-status"></div>
                    </div>
                    
                    <div class="vision-step-content">
                        <div class="vision-competition-info">
                            <p><?php _e('Multiple AI models will compete to suggest the best article topics based on your parameters. Select models to compare their outputs side-by-side.', $this->config['text_domain']); ?></p>
                        </div>
                        
                        <div class="vision-model-selection">
                            <h4><?php _e('Select AI Models for Competition:', $this->config['text_domain']); ?></h4>
                            <div class="model-selection-controls">
                                <button type="button" class="vision-button vision-button-small select-all-models"><?php _e('Select All', $this->config['text_domain']); ?></button>
                                <button type="button" class="vision-button vision-button-small deselect-all-models"><?php _e('Deselect All', $this->config['text_domain']); ?></button>
                                <span class="selected-count"><?php _e('0 models selected', $this->config['text_domain']); ?></span>
                            </div>
                            <div class="vision-model-grid">
                                <?php foreach ($available_models as $model_id => $model_info): ?>
                                <div class="vision-model-card">
                                    <input type="checkbox" id="model-<?php echo esc_attr($model_id); ?>" 
                                           class="competition-model" value="<?php echo esc_attr($model_id); ?>"
                                           <?php echo in_array($model_id, ['gpt-4', 'claude-3-sonnet', 'gemini-pro']) ? 'checked' : ''; ?>>
                                    <label for="model-<?php echo esc_attr($model_id); ?>" class="vision-model-label">
                                        <div class="vision-model-header">
                                            <div class="vision-model-name"><?php echo esc_html($model_info['name']); ?></div>
                                            <div class="vision-model-badge <?php echo esc_attr($model_info['recommended'] ? 'recommended' : ''); ?>
                                                <?php echo esc_attr($model_info['fast'] ? 'fast' : ''); ?>
                                                <?php echo esc_attr($model_info['budget'] ? 'budget' : ''); ?>
                                                <?php echo esc_attr($model_info['quality'] ? 'quality' : ''); ?>">
                                                <?php if ($model_info['recommended']): ?>
                                                    <?php _e('Recommended', $this->config['text_domain']); ?>
                                                <?php elseif ($model_info['fast']): ?>
                                                    <?php _e('Fast', $this->config['text_domain']); ?>
                                                <?php elseif ($model_info['budget']): ?>
                                                    <?php _e('Budget', $this->config['text_domain']); ?>
                                                <?php elseif ($model_info['quality']): ?>
                                                    <?php _e('Quality', $this->config['text_domain']); ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="vision-model-provider"><?php echo esc_html($model_info['provider']); ?></div>
                                        <div class="vision-model-cost">$<?php echo esc_html($model_info['cost_per_1k']); ?>/1K tokens</div>
                                        <div class="vision-model-strengths">
                                            <?php echo esc_html(implode(', ', $model_info['strengths'])); ?>
                                        </div>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <!-- Topic Generation Options -->
                        <div class="topic-generation-options">
                            <h4><?php _e('Topic Generation Options:', $this->config['text_domain']); ?></h4>
                            <div class="checkbox-group">
                                <label><input type="checkbox" id="include-trends" checked> <?php _e('Include trending topics', $this->config['text_domain']); ?></label>
                                <label><input type="checkbox" id="include-seasonal"> <?php _e('Include seasonal relevance', $this->config['text_domain']); ?></label>
                                <label><input type="checkbox" id="include-local" <?php echo $active_mou ? 'checked' : ''; ?>> <?php _e('Include local context', $this->config['text_domain']); ?></label>
                            </div>
                        </div>
                        
                        <div id="topic-suggestions" class="vision-topic-suggestions" style="display: none;">
                            <h4><?php _e('Topic Suggestions by AI Model:', $this->config['text_domain']); ?></h4>
                            <div id="suggestions-grid" class="vision-suggestions-grid">
                                <!-- Dynamic content will be loaded here -->
                            </div>
                        </div>
                        
                        <div class="vision-step-actions">
                            <button type="button" class="vision-button vision-button-secondary" 
                                    onclick="document.getElementById('step-1').scrollIntoView()">
                                <?php _e('← Back to Parameters', $this->config['text_domain']); ?>
                            </button>
                            <button type="button" class="vision-button vision-button-primary" 
                                    data-action="proceed-to-generation" disabled>
                                <?php _e('Generate Full Article', $this->config['text_domain']); ?>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Step 3: Article Generation -->
                <div class="vision-step-card" id="step-3">
                    <div class="vision-step-header">
                        <div class="vision-step-number">3</div>
                        <h3><?php _e('Article Generation', $this->config['text_domain']); ?></h3>
                        <div class="vision-step-status"></div>
                    </div>
                    
                    <div class="vision-step-content">
                        <div class="vision-generation-options">
                            <div class="generation-options-grid">
                                <div class="option-group">
                                    <h4><?php _e('Content Options', $this->config['text_domain']); ?></h4>
                                    <div class="checkbox-group">
                                        <label for="include-faq">
                                            <input type="checkbox" id="include-faq" checked>
                                            <?php _e('Include FAQ Section', $this->config['text_domain']); ?>
                                        </label>
                                        <label for="enable-internal-linking">
                                            <input type="checkbox" id="enable-internal-linking" checked>
                                            <?php _e('Enable Internal Linking', $this->config['text_domain']); ?>
                                        </label>
                                        <label for="include-conclusion">
                                            <input type="checkbox" id="include-conclusion" checked>
                                            <?php _e('Include Conclusion', $this->config['text_domain']); ?>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="option-group">
                                    <h4><?php _e('Image Options', $this->config['text_domain']); ?></h4>
                                    <div class="checkbox-group">
                                        <label for="include-images">
                                            <input type="checkbox" id="include-images" checked>
                                            <?php _e('Generate AI Images', $this->config['text_domain']); ?>
                                        </label>
                                        <label for="detect-placeholders">
                                            <input type="checkbox" id="detect-placeholders" checked>
                                            <?php _e('Auto-detect Image Placeholders', $this->config['text_domain']); ?>
                                        </label>
                                        <label for="optimize-alt-text">
                                            <input type="checkbox" id="optimize-alt-text" checked>
                                            <?php _e('Optimize Alt Text', $this->config['text_domain']); ?>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="option-group">
                                    <h4><?php _e('SEO Options', $this->config['text_domain']); ?></h4>
                                    <div class="checkbox-group">
                                        <label for="optimize-keywords">
                                            <input type="checkbox" id="optimize-keywords" checked>
                                            <?php _e('Optimize for Keywords', $this->config['text_domain']); ?>
                                        </label>
                                        <label for="include-meta">
                                            <input type="checkbox" id="include-meta" checked>
                                            <?php _e('Generate Meta Description', $this->config['text_domain']); ?>
                                        </label>
                                        <label for="include-schema">
                                            <input type="checkbox" id="include-schema">
                                            <?php _e('Include Schema Markup', $this->config['text_domain']); ?>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Image Provider Selection -->
                        <div class="image-provider-selection" id="image-providers-section" style="display: none;">
                            <h4><?php _e('Select Image Generation Providers:', $this->config['text_domain']); ?></h4>
                            <div class="provider-selection-grid">
                                <div class="provider-option">
                                    <input type="radio" id="provider-dalle" name="image-provider" value="dalle" checked>
                                    <label for="provider-dalle" class="provider-label">
                                        <div class="provider-name">DALL-E 3</div>
                                        <div class="provider-quality">High Quality</div>
                                        <div class="provider-cost">$0.08/image</div>
                                    </label>
                                </div>
                                <div class="provider-option">
                                    <input type="radio" id="provider-stable" name="image-provider" value="stable-diffusion">
                                    <label for="provider-stable" class="provider-label">
                                        <div class="provider-name">Stable Diffusion</div>
                                        <div class="provider-quality">Good Quality</div>
                                        <div class="provider-cost">$0.02/image</div>
                                    </label>
                                </div>
                                <div class="provider-option">
                                    <input type="radio" id="provider-midjourney" name="image-provider" value="midjourney">
                                    <label for="provider-midjourney" class="provider-label">
                                        <div class="provider-name">Midjourney</div>
                                        <div class="provider-quality">Artistic Style</div>
                                        <div class="provider-cost">$0.10/image</div>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div id="generation-progress" class="vision-generation-progress" style="display: none;">
                            <div class="vision-progress-steps">
                                <div class="progress-step" id="progress-content">
                                    <div class="step-icon">📝</div>
                                    <div class="step-text">Generating Content</div>
                                    <div class="step-status">⏳</div>
                                </div>
                                <div class="progress-step" id="progress-links">
                                    <div class="step-icon">🔗</div>
                                    <div class="step-text">Adding Internal Links</div>
                                    <div class="step-status">⏳</div>
                                </div>
                                <div class="progress-step" id="progress-images">
                                    <div class="step-icon">🖼️</div>
                                    <div class="step-text">Generating Images</div>
                                    <div class="step-status">⏳</div>
                                </div>
                                <div class="progress-step" id="progress-final">
                                    <div class="step-icon">✅</div>
                                    <div class="step-text">Finalizing Article</div>
                                    <div class="step-status">⏳</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="vision-step-actions">
                            <button type="button" class="vision-button vision-button-secondary"
                                    data-action="back-to-topics">
                                <?php _e('← Back to Topics', $this->config['text_domain']); ?>
                            </button>
                            <button type="button" class="vision-button vision-button-primary"
                                    data-action="start-generation">
                                <?php _e('Generate Multiple Drafts', $this->config['text_domain']); ?>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Draft Comparison -->
                <div class="vision-step-card" id="step-4">
                    <div class="vision-step-header">
                        <div class="vision-step-number">4</div>
                        <h3><?php _e('Draft Comparison', $this->config['text_domain']); ?></h3>
                        <div class="vision-step-status"></div>
                    </div>

                    <div class="vision-step-content">
                        <div class="comparison-info">
                            <p><?php _e('Compare articles generated by different AI models side-by-side. Select the best version or combine elements from multiple drafts.', $this->config['text_domain']); ?></p>
                        </div>

                        <div class="comparison-controls">
                            <div class="view-controls">
                                <button type="button" class="vision-button vision-button-small" data-action="show-all-drafts">
                                    <?php _e('Show All', $this->config['text_domain']); ?>
                                </button>
                                <button type="button" class="vision-button vision-button-small" data-action="hide-weak-drafts">
                                    <?php _e('Hide Weak Drafts', $this->config['text_domain']); ?>
                                </button>
                                <button type="button" class="vision-button vision-button-small" data-action="show-only-favorites">
                                    <?php _e('Show Favorites', $this->config['text_domain']); ?>
                                </button>
                            </div>

                            <div class="comparison-metrics">
                                <div class="metric-toggle">
                                    <label><input type="checkbox" id="show-word-count" checked> <?php _e('Word Count', $this->config['text_domain']); ?></label>
                                </div>
                                <div class="metric-toggle">
                                    <label><input type="checkbox" id="show-readability" checked> <?php _e('Readability', $this->config['text_domain']); ?></label>
                                </div>
                                <div class="metric-toggle">
                                    <label><input type="checkbox" id="show-seo-score" checked> <?php _e('SEO Score', $this->config['text_domain']); ?></label>
                                </div>
                                <div class="metric-toggle">
                                    <label><input type="checkbox" id="show-internal-links" checked> <?php _e('Internal Links', $this->config['text_domain']); ?></label>
                                </div>
                            </div>
                        </div>

                        <div id="drafts-comparison" class="drafts-comparison-container">
                            <!-- Dynamic comparison content will be loaded here -->
                            <div class="comparison-loading" style="display: none;">
                                <div class="loading-spinner"></div>
                                <p><?php _e('Generating drafts from multiple AI models...', $this->config['text_domain']); ?></p>
                                <div class="generation-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%"></div>
                                    </div>
                                    <div class="progress-text">0% complete</div>
                                </div>
                            </div>

                            <div class="comparison-grid" id="comparison-grid" style="display: none;">
                                <!-- Draft comparison cards will be inserted here -->
                            </div>
                        </div>

                        <div class="vision-step-actions">
                            <button type="button" class="vision-button vision-button-secondary"
                                    data-action="back-to-generation">
                                <?php _e('← Back to Generation', $this->config['text_domain']); ?>
                            </button>
                            <button type="button" class="vision-button vision-button-primary"
                                    data-action="proceed-to-preview" disabled>
                                <?php _e('Continue with Selected Draft', $this->config['text_domain']); ?>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Step 5: Article Preview -->
                <div class="vision-step-card" id="step-5">
                    <div class="vision-step-header">
                        <div class="vision-step-number">5</div>
                        <h3><?php _e('Article Preview & Publish', $this->config['text_domain']); ?></h3>
                        <div class="vision-step-status"></div>
                    </div>
                    
                    <div class="vision-step-content">
                        <div class="preview-tabs">
                            <button type="button" class="preview-tab active" data-tab="content"><?php _e('Article Content', $this->config['text_domain']); ?></button>
                            <button type="button" class="preview-tab" data-tab="images"><?php _e('Image Placeholders', $this->config['text_domain']); ?></button>
                            <button type="button" class="preview-tab" data-tab="seo"><?php _e('SEO Analysis', $this->config['text_domain']); ?></button>
                        </div>
                        
                        <div id="article-preview" class="vision-article-preview">
                            <!-- Generated article content will appear here -->
                        </div>
                        
                        <div id="image-placeholders" class="image-placeholders-section" style="display: none;">
                            <h4><?php _e('Image Placeholders', $this->config['text_domain']); ?></h4>
                            <div class="placeholders-info">
                                <p><?php _e('The AI detected the following image placeholders in your article. You can regenerate images or adjust prompts.', $this->config['text_domain']); ?></p>
                            </div>
                            <div id="placeholders-container" class="placeholders-container">
                                <!-- Image placeholders will be dynamically loaded here -->
                                <div class="placeholder-item">
                                    <div class="placeholder-text">Diagram of commercial plumbing system</div>
                                    <div class="placeholder-controls">
                                        <input type="text" class="vision-input placeholder-prompt" 
                                               value="<?php esc_attr_e('Professional diagram of commercial plumbing system with labeled components', $this->config['text_domain']); ?>"
                                               placeholder="<?php esc_attr_e('Enter image prompt...', $this->config['text_domain']); ?>">
                                        <select class="vision-select provider-select">
                                            <option value="dalle">DALL-E 3</option>
                                            <option value="stable-diffusion">Stable Diffusion</option>
                                            <option value="midjourney">Midjourney</option>
                                        </select>
                                        <button type="button" class="vision-button vision-button-small regenerate-image">
                                            <?php _e('Regenerate', $this->config['text_domain']); ?>
                                        </button>
                                    </div>
                                    <div class="placeholder-preview">
                                        <div class="image-placeholder">
                                            <span class="placeholder-icon">🖼️</span>
                                            <span class="placeholder-text"><?php _e('Image will appear here', $this->config['text_domain']); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div id="seo-analysis" class="seo-analysis-section" style="display: none;">
                            <h4><?php _e('SEO Analysis', $this->config['text_domain']); ?></h4>
                            <div class="seo-metrics">
                                <div class="seo-metric">
                                    <div class="metric-value">85%</div>
                                    <div class="metric-label"><?php _e('SEO Score', $this->config['text_domain']); ?></div>
                                </div>
                                <div class="seo-metric">
                                    <div class="metric-value">12</div>
                                    <div class="metric-label"><?php _e('Keywords', $this->config['text_domain']); ?></div>
                                </div>
                                <div class="seo-metric">
                                    <div class="metric-value">8</div>
                                    <div class="metric-label"><?php _e('Internal Links', $this->config['text_domain']); ?></div>
                                </div>
                                <div class="seo-metric">
                                    <div class="metric-value">H2</div>
                                    <div class="metric-label"><?php _e('Heading Structure', $this->config['text_domain']); ?></div>
                                </div>
                            </div>
                            <div class="seo-recommendations">
                                <h5><?php _e('Recommendations:', $this->config['text_domain']); ?></h5>
                                <ul>
                                    <li><?php _e('Add meta description for better click-through rates', $this->config['text_domain']); ?></li>
                                    <li><?php _e('Include alt text for all images', $this->config['text_domain']); ?></li>
                                    <li><?php _e('Consider adding a FAQ section', $this->config['text_domain']); ?></li>
                                </ul>
                            </div>
                        </div>
                        
                        <div id="article-metadata" class="vision-article-metadata" style="display: none;">
                            <div class="vision-metadata-grid">
                                <div class="vision-metadata-item">
                                    <strong><?php _e('Word Count:', $this->config['text_domain']); ?></strong>
                                    <span id="final-word-count">-</span>
                                </div>
                                <div class="vision-metadata-item">
                                    <strong><?php _e('AI Model Used:', $this->config['text_domain']); ?></strong>
                                    <span id="model-used">-</span>
                                </div>
                                <div class="vision-metadata-item">
                                    <strong><?php _e('Generation Time:', $this->config['text_domain']); ?></strong>
                                    <span id="generation-time">-</span>
                                </div>
                                <div class="vision-metadata-item">
                                    <strong><?php _e('Internal Links:', $this->config['text_domain']); ?></strong>
                                    <span id="links-count">-</span>
                                </div>
                            </div>
                        </div>
                        
                        <div id="internal-links-summary" class="vision-links-summary" style="display: none;">
                            <h4><?php _e('Internal Links Summary:', $this->config['text_domain']); ?></h4>
                            <div id="links-table-container"></div>
                        </div>
                        
                        <div class="vision-step-actions">
                            <button type="button" class="vision-button vision-button-secondary" 
                                    data-action="edit-article">
                                <?php _e('Edit Article', $this->config['text_domain']); ?>
                            </button>
                            <button type="button" class="vision-button vision-button-secondary" 
                                    data-action="regenerate">
                                <?php _e('Regenerate', $this->config['text_domain']); ?>
                            </button>
                            <button type="button" class="vision-button vision-button-primary" 
                                    data-action="publish-article">
                                <?php _e('Publish Article', $this->config['text_domain']); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .vision-article-builder {
            max-width: 1200px;
        }
        
        .vision-page-header {
            margin-bottom: var(--spacing-xl);
        }
        
        .vision-page-header h1 {
            color: var(--color-primary);
            margin-bottom: var(--spacing-sm);
        }
        
        .vision-warning-card {
            background: linear-gradient(135deg, var(--color-warning-bg) 0%, var(--color-warning-bg-secondary) 100%);
            border: 1px solid var(--color-warning);
            border-left: 4px solid var(--color-warning);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            color: var(--color-text-primary);
        }
        
        .vision-warning-card h3 {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--color-text-primary);
            font-weight: 600;
        }
        
        .vision-builder-workflow {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }
        
        .vision-step-card {
            background: var(--color-bg-primary);
            border: 2px solid var(--color-border);
            border-radius: var(--radius-lg);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .vision-step-card.active {
            border-color: var(--color-primary);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .vision-step-card.completed {
            border-color: var(--color-success);
        }
        
        .vision-step-header {
            background: var(--color-bg-secondary);
            padding: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .vision-step-card.active .vision-step-header {
            background: var(--color-primary-light);
        }
        
        .vision-step-card.completed .vision-step-header {
            background: var(--color-success-light);
        }
        
        .vision-step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--color-text-secondary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 18px;
        }
        
        .vision-step-card.active .vision-step-number {
            background: var(--color-primary);
        }
        
        .vision-step-card.completed .vision-step-number {
            background: var(--color-success);
        }
        
        .vision-step-header h3 {
            margin: 0;
            color: var(--color-text-primary);
            flex: 1;
        }
        
        .vision-step-content {
            padding: var(--spacing-lg);
        }
        
        .vision-form-grid {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }
        
        .vision-form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
        }
        
        .vision-form-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }
        
        .vision-form-group label {
            font-weight: 600;
            color: var(--color-text-primary);
        }
        
        .vision-form-help {
            font-size: 12px;
            color: var(--color-text-secondary);
            margin-top: var(--spacing-xs);
        }
        
        .vision-model-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }
        
        .vision-model-card {
            position: relative;
        }
        
        .vision-model-card input[type="checkbox"] {
            position: absolute;
            opacity: 0;
        }
        
        .vision-model-label {
            display: block;
            padding: var(--spacing-md);
            border: 2px solid var(--color-border);
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: all 0.2s ease;
            background: var(--color-bg-secondary);
        }
        
        .vision-model-card input:checked + .vision-model-label {
            border-color: var(--color-primary);
            background: var(--color-primary-light);
        }
        
        .vision-model-name {
            font-weight: 700;
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .vision-model-provider {
            font-size: 12px;
            color: var(--color-text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .vision-model-cost {
            font-weight: 600;
            color: var(--color-primary);
            margin: var(--spacing-xs) 0;
        }
        
        .vision-model-strengths {
            font-size: 12px;
            color: var(--color-text-secondary);
        }
        
        .vision-suggestions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-md);
        }
        
        .vision-step-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-lg);
            border-top: 1px solid var(--color-border);
        }
        
        .vision-generation-progress {
            margin: var(--spacing-lg) 0;
        }
        
        .vision-progress-steps {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-md);
        }
        
        .progress-step {
            text-align: center;
            padding: var(--spacing-md);
            background: var(--color-bg-secondary);
            border-radius: var(--radius-lg);
            border: 2px solid var(--color-border);
        }
        
        .progress-step.active {
            border-color: var(--color-primary);
            background: var(--color-primary-light);
        }
        
        .progress-step.completed {
            border-color: var(--color-success);
            background: var(--color-success-light);
        }
        
        .step-icon {
            font-size: 24px;
            margin-bottom: var(--spacing-xs);
        }
        
        .step-text {
            font-weight: 600;
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .step-status {
            font-size: 18px;
        }
        
        .vision-article-preview {
            background: var(--color-bg-primary);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-lg);
            max-height: 600px;
            overflow-y: auto;
        }
        
        .vision-metadata-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .vision-metadata-item {
            background: var(--color-bg-secondary);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
        }
        
        .vision-metadata-item strong {
            display: block;
            color: var(--color-text-secondary);
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: var(--spacing-xs);
        }
        
        .vision-metadata-item span {
            font-size: 18px;
            font-weight: 700;
            color: var(--color-primary);
        }
        
        .vision-links-summary {
            background: var(--color-bg-secondary);
            padding: var(--spacing-lg);
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .vision-links-summary h4 {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--color-text-primary);
        }
        
        @media (max-width: 768px) {
            .vision-form-row {
                grid-template-columns: 1fr;
            }
            
            .vision-model-grid {
                grid-template-columns: 1fr;
            }
            
            .vision-suggestions-grid {
                grid-template-columns: 1fr;
            }
            
            .vision-progress-steps {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .vision-step-actions {
                flex-direction: column;
                gap: var(--spacing-sm);
            }
        }
        </style>
        <?php
        echo ob_get_clean();
    }
    /**
     * Render Generated Articles tab content
     */
    public function renderGeneratedArticlesTab()
    {
        $articles = $this->databaseHandler->getArticles(['limit' => 50]);
        
        ob_start();
        ?>
        <div class="vision-generated-articles">
            <div class="vision-page-header">
                <h1><?php _e('Generated Articles', $this->config['text_domain']); ?></h1>
                <p class="vision-subtitle"><?php _e('Manage and review all AI-generated blog posts.', $this->config['text_domain']); ?></p>
            </div>
            
            <div class="vision-card">
                <div class="vision-card-header">
                    <h3><?php _e('Your Generated Articles', $this->config['text_domain']); ?></h3>
                    <div class="vision-controls">
                        <select class="vision-select" id="status-filter">
                            <option value="all"><?php _e('All Statuses', $this->config['text_domain']); ?></option>
                            <option value="draft"><?php _e('Draft', $this->config['text_domain']); ?></option>
                            <option value="published"><?php _e('Published', $this->config['text_domain']); ?></option>
                        </select>
                        <button type="button" class="vision-button vision-button-primary" 
                                onclick="VisionFramework.switchTab('article_builder')">
                            <?php _e('Generate New Article', $this->config['text_domain']); ?>
                        </button>
                    </div>
                </div>
                
                <?php if (!empty($articles)): ?>
                <div class="vision-articles-grid">
                    <?php foreach ($articles as $article): ?>
                    <div class="vision-article-card">
                        <div class="vision-article-header">
                            <h4><?php echo esc_html($article['title']); ?></h4>
                            <span class="vision-status-badge status-<?php echo esc_attr($article['status']); ?>">
                                <?php echo esc_html(ucfirst($article['status'])); ?>
                            </span>
                        </div>
                        
                        <div class="vision-article-meta">
                            <div class="meta-item">
                                <strong><?php _e('Words:', $this->config['text_domain']); ?></strong> 
                                <?php echo number_format($article['word_count']); ?>
                            </div>
                            <div class="meta-item">
                                <strong><?php _e('Model:', $this->config['text_domain']); ?></strong> 
                                <?php echo esc_html($article['ai_model_used']); ?>
                            </div>
                            <div class="meta-item">
                                <strong><?php _e('Generated:', $this->config['text_domain']); ?></strong> 
                                <?php echo human_time_diff(strtotime($article['generation_date'])); ?> <?php _e('ago', $this->config['text_domain']); ?>
                            </div>
                        </div>
                        
                        <div class="vision-article-actions">
                            <?php if ($article['post_id']): ?>
                            <a href="<?php echo admin_url('post.php?post=' . $article['post_id'] . '&action=edit'); ?>" 
                               class="vision-button vision-button-small">
                                <?php _e('Edit Post', $this->config['text_domain']); ?>
                            </a>
                            <a href="<?php echo get_permalink($article['post_id']); ?>" target="_blank"
                               class="vision-button vision-button-small vision-button-secondary">
                                <?php _e('View', $this->config['text_domain']); ?>
                            </a>
                            <?php endif; ?>
                            <button type="button" class="vision-button vision-button-small vision-button-secondary"
                                    data-action="view-metadata" data-article-id="<?php echo esc_attr($article['id']); ?>">
                                <?php _e('Details', $this->config['text_domain']); ?>
                            </button>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="vision-empty-state">
                    <p><?php _e('No articles generated yet. Start creating your first AI-powered blog post!', $this->config['text_domain']); ?></p>
                    <button type="button" class="vision-button vision-button-primary" 
                            onclick="VisionFramework.switchTab('article_builder')">
                        <?php _e('Generate Your First Article', $this->config['text_domain']); ?>
                    </button>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <style>
        .vision-articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: var(--spacing-lg);
            padding: var(--spacing-lg);
        }
        
        .vision-article-card {
            background: var(--color-bg-secondary);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            transition: transform 0.2s ease;
        }
        
        .vision-article-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .vision-article-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-md);
        }
        
        .vision-article-header h4 {
            margin: 0;
            color: var(--color-text-primary);
            flex: 1;
            margin-right: var(--spacing-sm);
        }
        
        .vision-article-meta {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
            margin-bottom: var(--spacing-md);
        }
        
        .meta-item {
            font-size: 12px;
            color: var(--color-text-secondary);
        }
        
        .meta-item strong {
            color: var(--color-text-primary);
        }
        
        .vision-article-actions {
            display: flex;
            gap: var(--spacing-xs);
            flex-wrap: wrap;
        }

        /* Draft Comparison Styles */
        .comparison-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-md);
            background: var(--color-bg-secondary);
            border-radius: var(--radius-md);
        }

        .view-controls {
            display: flex;
            gap: var(--spacing-xs);
        }

        .comparison-metrics {
            display: flex;
            gap: var(--spacing-md);
        }

        .metric-toggle label {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 0.9rem;
            cursor: pointer;
        }

        .comparison-loading {
            text-align: center;
            padding: var(--spacing-xl);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--color-border);
            border-top: 4px solid var(--color-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto var(--spacing-md);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .generation-progress {
            max-width: 400px;
            margin: 0 auto;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--color-border);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: var(--spacing-xs);
        }

        .progress-fill {
            height: 100%;
            background: var(--color-primary);
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 0.9rem;
            color: var(--color-text-secondary);
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .draft-card {
            border: 2px solid var(--color-border);
            border-radius: var(--radius-lg);
            background: var(--color-bg);
            transition: all 0.3s ease;
            position: relative;
        }

        .draft-card.selected {
            border-color: var(--color-primary);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .draft-card.hidden {
            opacity: 0.3;
            transform: scale(0.95);
        }

        .draft-header {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--color-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .draft-model-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .model-name {
            font-weight: 600;
            color: var(--color-text-primary);
        }

        .model-badge {
            padding: 2px 8px;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .model-badge.recommended {
            background: var(--color-success-light);
            color: var(--color-success);
        }

        .model-badge.fast {
            background: var(--color-warning-light);
            color: var(--color-warning);
        }

        .model-badge.quality {
            background: var(--color-primary-light);
            color: var(--color-primary);
        }

        .draft-actions {
            display: flex;
            gap: var(--spacing-xs);
        }

        .draft-metrics {
            padding: var(--spacing-sm) var(--spacing-md);
            background: var(--color-bg-secondary);
            display: flex;
            justify-content: space-between;
            font-size: 0.85rem;
            color: var(--color-text-secondary);
        }

        .draft-content {
            padding: var(--spacing-md);
            max-height: 400px;
            overflow-y: auto;
        }

        .draft-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
            color: var(--color-text-primary);
        }

        .draft-excerpt {
            color: var(--color-text-secondary);
            line-height: 1.6;
            margin-bottom: var(--spacing-md);
        }

        .draft-preview {
            font-size: 0.9rem;
            line-height: 1.6;
        }

        .draft-preview h2 {
            font-size: 1rem;
            margin: var(--spacing-sm) 0;
            color: var(--color-text-primary);
        }

        .draft-preview h3 {
            font-size: 0.95rem;
            margin: var(--spacing-xs) 0;
            color: var(--color-text-primary);
        }

        .draft-footer {
            padding: var(--spacing-md);
            border-top: 1px solid var(--color-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .draft-rating {
            display: flex;
            gap: var(--spacing-xs);
        }

        .rating-star {
            cursor: pointer;
            color: var(--color-border);
            transition: color 0.2s ease;
        }

        .rating-star.active,
        .rating-star:hover {
            color: var(--color-warning);
        }

        .draft-select-btn {
            padding: var(--spacing-xs) var(--spacing-md);
            border: 2px solid var(--color-primary);
            background: transparent;
            color: var(--color-primary);
            border-radius: var(--radius-md);
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .draft-select-btn:hover {
            background: var(--color-primary);
            color: white;
        }

        .draft-select-btn.selected {
            background: var(--color-primary);
            color: white;
        }

        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }

            .comparison-controls {
                flex-direction: column;
                gap: var(--spacing-md);
            }

            .comparison-metrics {
                flex-wrap: wrap;
                gap: var(--spacing-sm);
            }
        }
        </style>
        <?php
        echo ob_get_clean();
    }
    
    /**
     * Render Settings tab content
     */
    public function renderSettingsTab()
    {
        // Get current settings
        $assigned_user_id = $this->databaseHandler->getBlogSetting('assigned_user_id', 0);
        $active_mou = $this->databaseHandler->getActiveMoU();
        $enabled_providers = $this->databaseHandler->getEnabledProviders('text');
        $image_providers = $this->databaseHandler->getEnabledProviders('image');
        $user_setup = $this->userManager->getUserSetupStatus();
        $global_image_style = $this->databaseHandler->getBlogSetting('global_image_style', 'professional and modern');
        
        ob_start();
        ?>
        <div class="vision-settings">
            <div class="vision-page-header">
                <h1><?php _e('Settings & Configuration', $this->config['text_domain']); ?></h1>
                <p class="vision-subtitle"><?php _e('Complete setup and configuration for AI-powered blog generation. This is the first step in your workflow.', $this->config['text_domain']); ?></p>
            </div>
            
            <!-- Setup Progress Indicator -->
            <div class="vision-setup-progress">
                <div class="setup-step <?php echo !empty($assigned_user_id) ? 'completed' : 'active'; ?>">
                    <span class="step-icon">👤</span>
                    <span class="step-title"><?php _e('Author Setup', $this->config['text_domain']); ?></span>
                </div>
                <div class="setup-step <?php echo !empty($active_mou) ? 'completed' : (!empty($assigned_user_id) ? 'active' : 'pending'); ?>">
                    <span class="step-icon">📋</span>
                    <span class="step-title"><?php _e('Site MoU', $this->config['text_domain']); ?></span>
                </div>
                <div class="setup-step <?php echo !empty($enabled_providers) ? 'completed' : 'pending'; ?>">
                    <span class="step-icon">🔑</span>
                    <span class="step-title"><?php _e('API Keys', $this->config['text_domain']); ?></span>
                </div>
                <div class="setup-step pending">
                    <span class="step-icon">🎯</span>
                    <span class="step-title"><?php _e('Ready to Generate', $this->config['text_domain']); ?></span>
                </div>
            </div>
            <form id="settings-form" class="vision-settings-form">
                <!-- Author Designation Section -->
                <div class="vision-card">
                    <div class="vision-card-header">
                        <h3><?php _e('👤 Author Designation', $this->config['text_domain']); ?></h3>
                        <p class="vision-card-subtitle"><?php _e('Assign a WordPress user to own all generated articles. This is required for the plugin to function.', $this->config['text_domain']); ?></p>
                    </div>
                    <div class="vision-card-content">
                        <?php if ($user_setup['assigned_user']): ?>
                        <div class="assigned-user-display">
                            <div class="user-info">
                                <img src="<?php echo esc_url($user_setup['assigned_user']['avatar_url']); ?>" alt="Avatar" class="user-avatar">
                                <div class="user-details">
                                    <h4><?php echo esc_html($user_setup['assigned_user']['display_name']); ?></h4>
                                    <p><?php echo esc_html($user_setup['assigned_user']['username']); ?> • <?php echo esc_html($user_setup['assigned_user']['role']); ?></p>
                                    <small><?php printf(__('%d articles generated', $this->config['text_domain']), $user_setup['assigned_user']['article_count']); ?></small>
                                </div>
                            </div>
                            <div class="user-actions">
                                <a href="<?php echo esc_url($user_setup['assigned_user']['edit_url']); ?>" class="vision-button vision-button-secondary">
                                    <?php _e('Edit User in WordPress', $this->config['text_domain']); ?>
                                </a>
                                <button type="button" class="vision-button vision-button-primary" data-action="change_assigned_user">
                                    <?php _e('Change Author', $this->config['text_domain']); ?>
                                </button>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="author-setup-needed">
                            <div class="setup-message">
                                <span class="setup-icon">⚠️</span>
                                <div class="setup-text">
                                    <h4><?php _e('Author Assignment Required', $this->config['text_domain']); ?></h4>
                                    <p><?php _e('Blog Writer requires a dedicated WordPress user to own generated articles. You can create a new user or assign an existing one.', $this->config['text_domain']); ?></p>
                                </div>
                            </div>
                            <div class="author-setup-actions">
                                <button type="button" class="vision-button vision-button-primary" data-action="create_new_user">
                                    <?php _e('Create New Author User', $this->config['text_domain']); ?>
                                </button>
                                <button type="button" class="vision-button vision-button-secondary" data-action="assign_existing_user">
                                    <?php _e('Assign Existing User', $this->config['text_domain']); ?>
                                </button>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Site Memorandum of Understanding -->
                <div class="vision-card">
                    <div class="vision-card-header">
                        <h3><?php _e('📋 Site Memorandum of Understanding (MoU)', $this->config['text_domain']); ?></h3>
                        <p class="vision-card-subtitle"><?php _e('Describe your business, services, location, and market. This context is included in all AI prompts for relevant content generation. Keep it under 200 words.', $this->config['text_domain']); ?></p>
                    </div>
                    <div class="vision-card-content">
                        <div class="mou-editor">
                            <textarea id="site-mou" name="site_mou" class="vision-textarea mou-textarea" rows="6" maxlength="1200" placeholder="<?php esc_attr_e('Example: Local plumbing service in Vancouver, Canada, focusing on domestic and commercial repairs, drain cleaning, and emergency services. We serve homeowners and businesses throughout the Greater Vancouver area with 24/7 availability and licensed, insured technicians.', $this->config['text_domain']); ?>"><?php echo esc_textarea($active_mou ?: ''); ?></textarea>
                            <div class="mou-meta">
                                <span class="word-count">0/200 words</span>
                                <div class="mou-actions">
                                    <button type="button" class="vision-button vision-button-secondary" data-action="generate_mou">
                                        <?php _e('AI Generate MoU', $this->config['text_domain']); ?>
                                    </button>
                                    <button type="button" class="vision-button vision-button-primary" data-action="save_mou">
                                        <?php _e('Save MoU', $this->config['text_domain']); ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="mou-help">
                            <h4><?php _e('💡 Tips for a Good MoU:', $this->config['text_domain']); ?></h4>
                            <ul>
                                <li><?php _e('Include your business type, services, and specializations', $this->config['text_domain']); ?></li>
                                <li><?php _e('Mention your target market (residential, commercial, etc.)', $this->config['text_domain']); ?></li>
                                <li><?php _e('Add your location/service area for local SEO', $this->config['text_domain']); ?></li>
                                <li><?php _e('Describe what makes your business unique', $this->config['text_domain']); ?></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- Multi-Provider API Configuration -->
                <div class="vision-card">
                    <div class="vision-card-header">
                        <h3><?php _e('🔑 AI Provider Configuration', $this->config['text_domain']); ?></h3>
                        <p class="vision-card-subtitle"><?php _e('Configure API keys for text and image AI providers. Multiple providers allow you to compare outputs and choose the best models for different tasks.', $this->config['text_domain']); ?></p>
                    </div>
                    <div class="vision-card-content">
                        <div class="provider-tabs">
                            <button type="button" class="provider-tab active" data-tab="text-providers"><?php _e('Text Generation', $this->config['text_domain']); ?></button>
                            <button type="button" class="provider-tab" data-tab="image-providers"><?php _e('Image Generation', $this->config['text_domain']); ?></button>
                        </div>
                        
                        <div class="api-providers-grid" id="text-providers">
                            <!-- OpenRouter -->
                            <div class="api-provider-section" data-provider="openrouter">
                                <div class="provider-header">
                                    <div class="provider-info">
                                        <h4 class="provider-name">
                                            <span class="provider-logo">🌐</span>
                                            <?php _e('OpenRouter', $this->config['text_domain']); ?>
                                            <span class="provider-badge"><?php _e('Recommended', $this->config['text_domain']); ?></span>
                                        </h4>
                                        <p class="provider-description"><?php _e('Access to 100+ models from multiple providers', $this->config['text_domain']); ?></p>
                                    </div>
                                    <div class="provider-status" id="openrouter-status">
                                        <span class="status-indicator"></span>
                                    </div>
                                </div>
                                <div class="provider-config">
                                    <div class="vision-input-group">
                                        <input type="text" id="openrouter-api-key" name="openrouter_api_key"
                                               class="vision-input api-key-input" 
                                               value="<?php echo esc_attr($this->databaseHandler->getBlogSetting('openrouter_api_key', '')); ?>"
                                               placeholder="<?php esc_attr_e('sk-or-v1-...', $this->config['text_domain']); ?>"
                                               style="font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace; font-size: 13px; letter-spacing: 0.5px;"
                                               autocomplete="off">
                                        <button type="button" class="vision-button vision-button-secondary test-api-btn" 
                                                data-provider="openrouter">
                                            <span class="btn-text"><?php _e('Test', $this->config['text_domain']); ?></span>
                                            <span class="btn-loading" style="display: none;">🔄</span>
                                        </button>
                                    </div>
                                    <div class="provider-help">
                                        <a href="https://openrouter.ai/keys" target="_blank"><?php _e('Get API Key', $this->config['text_domain']); ?></a> |
                                        <span class="models-info"><?php _e('GPT-4, Claude, Gemini, Llama & more', $this->config['text_domain']); ?></span>
                                    </div>
                                    <div class="api-status-container" id="openrouter-status-container"></div>
                                </div>
                            </div>

                            <!-- OpenAI -->
                            <div class="api-provider-section" data-provider="openai">
                                <div class="provider-header">
                                    <div class="provider-info">
                                        <h4 class="provider-name">
                                            <span class="provider-logo">🤖</span>
                                            <?php _e('OpenAI', $this->config['text_domain']); ?>
                                        </h4>
                                        <p class="provider-description"><?php _e('Direct access to GPT models', $this->config['text_domain']); ?></p>
                                    </div>
                                    <div class="provider-status" id="openai-status">
                                        <span class="status-indicator"></span>
                                    </div>
                                </div>
                                <div class="provider-config">
                                    <div class="vision-input-group">
                                        <input type="text" id="openai-api-key" name="openai_api_key"
                                               class="vision-input api-key-input" 
                                               value="<?php echo esc_attr($this->databaseHandler->getBlogSetting('openai_api_key', '')); ?>"
                                               placeholder="<?php esc_attr_e('sk-...', $this->config['text_domain']); ?>"
                                               style="font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace; font-size: 13px; letter-spacing: 0.5px;"
                                               autocomplete="off">
                                        <button type="button" class="vision-button vision-button-secondary test-api-btn" 
                                                data-provider="openai">
                                            <span class="btn-text"><?php _e('Test', $this->config['text_domain']); ?></span>
                                            <span class="btn-loading" style="display: none;">🔄</span>
                                        </button>
                                    </div>
                                    <div class="provider-help">
                                        <a href="https://platform.openai.com/api-keys" target="_blank"><?php _e('Get API Key', $this->config['text_domain']); ?></a> |
                                        <span class="models-info"><?php _e('GPT-4, GPT-3.5', $this->config['text_domain']); ?></span>
                                    </div>
                                    <div class="api-status-container" id="openai-status-container"></div>
                                </div>
                            </div>

                            <!-- Anthropic -->
                            <div class="api-provider-section" data-provider="anthropic">
                                <div class="provider-header">
                                    <div class="provider-info">
                                        <h4 class="provider-name">
                                            <span class="provider-logo">🧠</span>
                                            <?php _e('Anthropic', $this->config['text_domain']); ?>
                                        </h4>
                                        <p class="provider-description"><?php _e('Claude models for advanced reasoning', $this->config['text_domain']); ?></p>
                                    </div>
                                    <div class="provider-status" id="anthropic-status">
                                        <span class="status-indicator"></span>
                                    </div>
                                </div>
                                <div class="provider-config">
                                    <div class="vision-input-group">
                                        <input type="text" id="anthropic-api-key" name="anthropic_api_key"
                                               class="vision-input api-key-input" 
                                               value="<?php echo esc_attr($this->databaseHandler->getBlogSetting('anthropic_api_key', '')); ?>"
                                               placeholder="<?php esc_attr_e('sk-ant-...', $this->config['text_domain']); ?>"
                                               style="font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace; font-size: 13px; letter-spacing: 0.5px;"
                                               autocomplete="off">
                                        <button type="button" class="vision-button vision-button-secondary test-api-btn" 
                                                data-provider="anthropic">
                                            <span class="btn-text"><?php _e('Test', $this->config['text_domain']); ?></span>
                                            <span class="btn-loading" style="display: none;">🔄</span>
                                        </button>
                                    </div>
                                    <div class="provider-help">
                                        <a href="https://console.anthropic.com/account/keys" target="_blank"><?php _e('Get API Key', $this->config['text_domain']); ?></a> |
                                        <span class="models-info"><?php _e('Claude 3.5 Sonnet, Claude 3 Opus', $this->config['text_domain']); ?></span>
                                    </div>
                                    <div class="api-status-container" id="anthropic-status-container"></div>
                                </div>
                            </div>

                            <!-- Grok -->
                            <div class="api-provider-section" data-provider="grok">
                                <div class="provider-header">
                                    <div class="provider-info">
                                        <h4 class="provider-name">
                                            <span class="provider-logo">X</span>
                                            <?php _e('Grok', $this->config['text_domain']); ?>
                                        </h4>
                                        <p class="provider-description"><?php _e('X.AI\'s powerful reasoning AI', $this->config['text_domain']); ?></p>
                                    </div>
                                    <div class="provider-status" id="grok-status">
                                        <span class="status-indicator"></span>
                                    </div>
                                </div>
                                <div class="provider-config">
                                    <div class="vision-input-group">
                                        <input type="text" id="grok-api-key" name="grok_api_key"
                                               class="vision-input api-key-input" 
                                               value="<?php echo esc_attr($this->databaseHandler->getBlogSetting('grok_api_key', '')); ?>"
                                               placeholder="<?php esc_attr_e('xai-...', $this->config['text_domain']); ?>"
                                               style="font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace; font-size: 13px; letter-spacing: 0.5px;"
                                               autocomplete="off">
                                        <button type="button" class="vision-button vision-button-secondary test-api-btn" 
                                                data-provider="grok">
                                            <span class="btn-text"><?php _e('Test', $this->config['text_domain']); ?></span>
                                            <span class="btn-loading" style="display: none;">🔄</span>
                                        </button>
                                    </div>
                                    <div class="provider-help">
                                        <a href="https://x.ai" target="_blank"><?php _e('Get API Key', $this->config['text_domain']); ?></a> |
                                        <span class="models-info"><?php _e('Grok Beta', $this->config['text_domain']); ?></span>
                                    </div>
                                    <div class="api-status-container" id="grok-status-container"></div>
                                </div>
                            </div>

                            <!-- Groq -->
                            <div class="api-provider-section" data-provider="groq">
                                <div class="provider-header">
                                    <div class="provider-info">
                                        <h4 class="provider-name">
                                            <span class="provider-logo">⚡</span>
                                            <?php _e('Groq', $this->config['text_domain']); ?>
                                        </h4>
                                        <p class="provider-description"><?php _e('Ultra-fast inference speeds', $this->config['text_domain']); ?></p>
                                    </div>
                                    <div class="provider-status" id="groq-status">
                                        <span class="status-indicator"></span>
                                    </div>
                                </div>
                                <div class="provider-config">
                                    <div class="vision-input-group">
                                        <input type="text" id="groq-api-key" name="groq_api_key"
                                               class="vision-input api-key-input" 
                                               value="<?php echo esc_attr($this->databaseHandler->getBlogSetting('groq_api_key', '')); ?>"
                                               placeholder="<?php esc_attr_e('gsk_...', $this->config['text_domain']); ?>"
                                               style="font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace; font-size: 13px; letter-spacing: 0.5px;"
                                               autocomplete="off">
                                        <button type="button" class="vision-button vision-button-secondary test-api-btn" 
                                                data-provider="groq">
                                            <span class="btn-text"><?php _e('Test', $this->config['text_domain']); ?></span>
                                            <span class="btn-loading" style="display: none;">🔄</span>
                                        </button>
                                    </div>
                                    <div class="provider-help">
                                        <a href="https://console.groq.com/keys" target="_blank"><?php _e('Get API Key', $this->config['text_domain']); ?></a> |
                                        <span class="models-info"><?php _e('Llama 3, Mixtral, Gemma', $this->config['text_domain']); ?></span>
                                    </div>
                                    <div class="api-status-container" id="groq-status-container"></div>
                                </div>
                            </div>

                            <!-- Google Gemini -->
                            <div class="api-provider-section" data-provider="google">
                                <div class="provider-header">
                                    <div class="provider-info">
                                        <h4 class="provider-name">
                                            <span class="provider-logo">💎</span>
                                            <?php _e('Google Gemini', $this->config['text_domain']); ?>
                                        </h4>
                                        <p class="provider-description"><?php _e('Google\'s powerful multimodal AI', $this->config['text_domain']); ?></p>
                                    </div>
                                    <div class="provider-status" id="google-status">
                                        <span class="status-indicator"></span>
                                    </div>
                                </div>
                                <div class="provider-config">
                                    <div class="vision-input-group">
                                        <input type="text" id="google-api-key" name="google_api_key"
                                               class="vision-input api-key-input" 
                                               value="<?php echo esc_attr($this->databaseHandler->getBlogSetting('google_api_key', '')); ?>"
                                               placeholder="<?php esc_attr_e('AIza...', $this->config['text_domain']); ?>"
                                               style="font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace; font-size: 13px; letter-spacing: 0.5px;"
                                               autocomplete="off">
                                        <button type="button" class="vision-button vision-button-secondary test-api-btn" 
                                                data-provider="google">
                                            <span class="btn-text"><?php _e('Test', $this->config['text_domain']); ?></span>
                                            <span class="btn-loading" style="display: none;">🔄</span>
                                        </button>
                                    </div>
                                    <div class="provider-help">
                                        <a href="https://aistudio.google.com/app/apikey" target="_blank"><?php _e('Get API Key', $this->config['text_domain']); ?></a> |
                                        <span class="models-info"><?php _e('Gemini Pro, Gemini Flash', $this->config['text_domain']); ?></span>
                                    </div>
                                    <div class="api-status-container" id="google-status-container"></div>
                                </div>
                            </div>
                        </div>

                        <div class="api-configuration-actions">
                            <button type="button" class="vision-button vision-button-primary test-all-apis" data-action="test_all_apis">
                                <span class="btn-text"><?php _e('Test All Configured APIs', $this->config['text_domain']); ?></span>
                                <span class="btn-loading" style="display: none;">🔄</span>
                            </button>
                            <button type="button" class="vision-button vision-button-secondary refresh-models" data-action="refresh_models">
                                <?php _e('Refresh Available Models', $this->config['text_domain']); ?>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- AI Model Settings -->
                <div class="vision-card">
                    <div class="vision-card-header">
                        <h3><?php _e('🤖 AI Model Settings', $this->config['text_domain']); ?></h3>
                    </div>
                    <div class="vision-card-content">
                        <div class="vision-form-row">
                            <div class="vision-form-group">
                                <label for="default-ai-model"><?php _e('Default AI Model:', $this->config['text_domain']); ?></label>
                                <select id="default-ai-model" name="default_ai_model" class="vision-select">
                                    <?php foreach ($available_models as $model_id => $model_info): ?>
                                    <option value="<?php echo esc_attr($model_id); ?>"
                                            <?php selected($default_model, $model_id); ?>>
                                        <?php echo esc_html($model_info['name']) . ' ($' . $model_info['cost_per_1k'] . '/1K)'; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="vision-form-group">
                                <label for="default-word-count"><?php _e('Default Word Count:', $this->config['text_domain']); ?></label>
                                <input type="number" id="default-word-count" name="default_word_count"
                                       class="vision-input" value="<?php echo esc_attr($default_word_count); ?>"
                                       min="250" max="5000" step="50">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Content Settings -->
                <div class="vision-card">
                    <div class="vision-card-header">
                        <h3><?php _e('📝 Content Settings', $this->config['text_domain']); ?></h3>
                    </div>
                    <div class="vision-card-content">
                        <div class="vision-form-group">
                            <label for="main-content-prompt"><?php _e('Main Content Prompt:', $this->config['text_domain']); ?></label>
                            <textarea id="main-content-prompt" name="main_content_prompt" class="vision-textarea" rows="4"><?php 
                                echo esc_textarea($this->databaseHandler->getBlogSetting('main_content_prompt', 
                                    'Write a comprehensive, SEO-optimized blog post about [TOPIC]. Include relevant headings, engaging content, and ensure it provides value to readers. Target word count: [WORD_COUNT] words.'
                                ));
                            ?></textarea>
                            <div class="vision-form-help">
                                <?php _e('Use [TOPIC] and [WORD_COUNT] as placeholders. This prompt guides how articles are generated.', $this->config['text_domain']); ?>
                            </div>
                        </div>
                        
                        <div class="vision-form-group">
                            <label for="seo-optimization-prompt"><?php _e('SEO Optimization Prompt:', $this->config['text_domain']); ?></label>
                            <textarea id="seo-optimization-prompt" name="seo_optimization_prompt" class="vision-textarea" rows="3"><?php 
                                echo esc_textarea($this->databaseHandler->getBlogSetting('seo_optimization_prompt', 
                                    'Focus on relevant keywords, proper heading structure, and content that provides genuine value to readers searching for information about [TOPIC].'
                                ));
                            ?></textarea>
                        </div>
                        
                        <div class="vision-form-group">
                            <label for="image-generation-prompt"><?php _e('Image Generation Prompt:', $this->config['text_domain']); ?></label>
                            <textarea id="image-generation-prompt" name="image_generation_prompt" class="vision-textarea" rows="3"><?php 
                                echo esc_textarea($this->databaseHandler->getBlogSetting('image_generation_prompt', 
                                    'Create a professional, relevant image for a blog post about [TOPIC]. Style should be modern and engaging.'
                                ));
                            ?></textarea>
                        </div>
                    </div>
                </div>
                
                <!-- Save Settings -->
                <div class="vision-settings-actions">
                    <button type="submit" class="vision-button vision-button-primary">
                        <?php _e('Save Settings', $this->config['text_domain']); ?>
                    </button>
                    <button type="button" class="vision-button vision-button-secondary" data-action="reset-defaults">
                        <?php _e('Reset to Defaults', $this->config['text_domain']); ?>
                    </button>
                </div>
            </form>
        </div>
        
        <style>
        .vision-settings-form {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }
        
        .vision-input-group {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .vision-input-group .vision-input {
            flex: 1;
        }
        
        .vision-settings-actions {
            display: flex;
            gap: var(--spacing-sm);
            justify-content: flex-start;
            padding: var(--spacing-lg);
            background: var(--color-bg-secondary);
            border-radius: var(--radius-lg);
        }
        
        /* Multi-Provider API Styles */
        .api-providers-grid {
            display: grid;
            gap: var(--spacing-lg);
        }
        
        .api-provider-section {
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            background: var(--color-bg-secondary);
            transition: all 0.2s ease;
        }
        
        .api-provider-section:hover {
            border-color: var(--color-primary);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .provider-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-md);
        }
        
        .provider-info h4.provider-name {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            margin: 0 0 var(--spacing-xs) 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--color-text-primary);
        }
        
        .provider-logo {
            font-size: 20px;
        }
        
        .provider-badge {
            background: var(--color-primary);
            color: white;
            font-size: 11px;
            font-weight: 500;
            padding: 2px 8px;
            border-radius: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .provider-description {
            color: var(--color-text-secondary);
            font-size: 13px;
            margin: 0;
        }
        
        .provider-status {
            display: flex;
            align-items: center;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--color-text-muted);
            transition: background-color 0.3s ease;
        }
        
        .status-indicator.connected {
            background: #28a745;
        }
        
        .status-indicator.error {
            background: #dc3545;
        }
        
        .status-indicator.testing {
            background: #007cba;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .provider-config {
            margin-top: var(--spacing-md);
        }
        
        .api-key-input {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important;
            font-size: 13px !important;
            letter-spacing: 0.5px;
        }
        
        .test-api-btn {
            min-width: 80px;
            position: relative;
        }
        
        .test-api-btn .btn-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: translate(-50%, -50%) rotate(0deg); }
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        .toggle-visibility {
            min-width: 40px;
            cursor: pointer;
        }
        
        .provider-help {
            margin-top: var(--spacing-sm);
            font-size: 12px;
            color: var(--color-text-secondary);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .provider-help a {
            color: var(--color-primary);
            text-decoration: none;
        }
        
        .provider-help a:hover {
            text-decoration: underline;
        }
        
        .models-info {
            color: var(--color-text-muted);
            font-style: italic;
        }
        
        .api-status-container {
            margin-top: var(--spacing-sm);
        }
        
        .api-status-message {
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            transition: all 0.3s ease;
        }
        
        .api-status-message.success {
            background: rgba(40, 167, 69, 0.1);
            border-left: 3px solid #28a745;
            color: #155724;
        }
        
        .api-status-message.error {
            background: rgba(220, 53, 69, 0.1);
            border-left: 3px solid #dc3545;
            color: #721c24;
        }
        
        .api-status-message.testing {
            background: rgba(0, 124, 186, 0.1);
            border-left: 3px solid #007cba;
            color: #004085;
        }
        
        .api-status-message small {
            opacity: 0.7;
            margin-left: auto;
        }
        
        .api-configuration-actions {
            margin-top: var(--spacing-lg);
            padding-top: var(--spacing-lg);
            border-top: 1px solid var(--color-border);
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
        }
        
        .test-all-apis {
            position: relative;
        }
        
        .test-all-apis .btn-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation: spin 1s linear infinite;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .api-provider-section {
            border-color: var(--color-border-dark);
            background: var(--color-bg-tertiary);
        }
        
        [data-theme="dark"] .api-provider-section:hover {
            border-color: var(--color-primary-light);
        }
        
        [data-theme="dark"] .api-status-message.success {
            background: rgba(40, 167, 69, 0.15);
            color: #d4edda;
        }
        
        [data-theme="dark"] .api-status-message.error {
            background: rgba(220, 53, 69, 0.15);
            color: #f8d7da;
        }
        
        [data-theme="dark"] .api-status-message.testing {
            background: rgba(0, 124, 186, 0.15);
            color: #cce7f0;
        }
        </style>
        <?php
        echo ob_get_clean();
    }
    
    /**
     * Render Help tab content
     */
    public function renderHelpTab()
    {
        ob_start();
        ?>
        <div class="vision-help">
            <div class="vision-page-header">
                <h1><?php _e('Help & Documentation', $this->config['text_domain']); ?></h1>
                <p class="vision-subtitle"><?php _e('Get started with Blog Writer and learn how to create amazing AI-powered content.', $this->config['text_domain']); ?></p>
            </div>
            
            <!-- Quick Start Guide -->
            <div class="vision-card">
                <div class="vision-card-header">
                    <h3><?php _e('🚀 Quick Start Guide', $this->config['text_domain']); ?></h3>
                </div>
                <div class="vision-card-content">
                    <div class="vision-help-steps">
                        <div class="help-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h4><?php _e('Setup API Access', $this->config['text_domain']); ?></h4>
                                <p><?php _e('Get your OpenRouter API key and configure it in the Settings tab.', $this->config['text_domain']); ?></p>
                                <a href="https://openrouter.ai" target="_blank" class="vision-button vision-button-small">
                                    <?php _e('Get API Key', $this->config['text_domain']); ?>
                                </a>
                            </div>
                        </div>
                        
                        <div class="help-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h4><?php _e('Create or Assign User', $this->config['text_domain']); ?></h4>
                                <p><?php _e('Set up a dedicated user account to own all generated articles.', $this->config['text_domain']); ?></p>
                                <button type="button" class="vision-button vision-button-small" 
                                        onclick="VisionFramework.switchTab('dashboard')">
                                    <?php _e('Go to Dashboard', $this->config['text_domain']); ?>
                                </button>
                            </div>
                        </div>
                        
                        <div class="help-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h4><?php _e('Extract Page Keywords', $this->config['text_domain']); ?></h4>
                                <p><?php _e('Scan your WordPress pages to extract SEO keywords for intelligent linking.', $this->config['text_domain']); ?></p>
                                <button type="button" class="vision-button vision-button-small" 
                                        onclick="VisionFramework.switchTab('page_keywords')">
                                    <?php _e('Extract Keywords', $this->config['text_domain']); ?>
                                </button>
                            </div>
                        </div>
                        
                        <div class="help-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h4><?php _e('Generate Your First Article', $this->config['text_domain']); ?></h4>
                                <p><?php _e('Use the Article Builder to create AI-powered content with internal linking.', $this->config['text_domain']); ?></p>
                                <button type="button" class="vision-button vision-button-small" 
                                        onclick="VisionFramework.switchTab('article_builder')">
                                    <?php _e('Start Building', $this->config['text_domain']); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Features Overview -->
            <div class="vision-help-grid">
                <div class="vision-card">
                    <div class="vision-card-header">
                        <h3><?php _e('🤖 AI Models', $this->config['text_domain']); ?></h3>
                    </div>
                    <div class="vision-card-content">
                        <ul class="vision-feature-list">
                            <li><strong>GPT-4:</strong> <?php _e('Best accuracy and creativity', $this->config['text_domain']); ?></li>
                            <li><strong>Claude:</strong> <?php _e('Excellent for analysis and safety', $this->config['text_domain']); ?></li>
                            <li><strong>Gemini:</strong> <?php _e('Cost-effective and factual', $this->config['text_domain']); ?></li>
                        </ul>
                    </div>
                </div>
                
                <div class="vision-card">
                    <div class="vision-card-header">
                        <h3><?php _e('🔗 Internal Linking', $this->config['text_domain']); ?></h3>
                    </div>
                    <div class="vision-card-content">
                        <ul class="vision-feature-list">
                            <li><?php _e('Automatic keyword matching', $this->config['text_domain']); ?></li>
                            <li><?php _e('Relevance scoring', $this->config['text_domain']); ?></li>
                            <li><?php _e('Smart link placement', $this->config['text_domain']); ?></li>
                            <li><?php _e('Link density control', $this->config['text_domain']); ?></li>
                        </ul>
                    </div>
                </div>
                
                <div class="vision-card">
                    <div class="vision-card-header">
                        <h3><?php _e('🎨 AI Images', $this->config['text_domain']); ?></h3>
                    </div>
                    <div class="vision-card-content">
                        <ul class="vision-feature-list">
                            <li><?php _e('DALL-E 3 integration', $this->config['text_domain']); ?></li>
                            <li><?php _e('Custom prompts', $this->config['text_domain']); ?></li>
                            <li><?php _e('WordPress media library', $this->config['text_domain']); ?></li>
                            <li><?php _e('SEO-optimized alt text', $this->config['text_domain']); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Troubleshooting -->
            <div class="vision-card">
                <div class="vision-card-header">
                    <h3><?php _e('⚙️ Troubleshooting', $this->config['text_domain']); ?></h3>
                </div>
                <div class="vision-card-content">
                    <div class="vision-faq">
                        <div class="faq-item">
                            <h4><?php _e('API key not working?', $this->config['text_domain']); ?></h4>
                            <p><?php _e('Make sure you have credits in your OpenRouter account and the key is entered correctly.', $this->config['text_domain']); ?></p>
                        </div>
                        
                        <div class="faq-item">
                            <h4><?php _e('Keywords not extracting?', $this->config['text_domain']); ?></h4>
                            <p><?php _e('Ensure your pages are published and have sufficient content for keyword analysis.', $this->config['text_domain']); ?></p>
                        </div>
                        
                        <div class="faq-item">
                            <h4><?php _e('Need to reset everything?', $this->config['text_domain']); ?></h4>
                            <p><?php _e('Use the "Repair Database" button in the Dashboard to recreate all tables.', $this->config['text_domain']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .vision-help-steps {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }
        
        .help-step {
            display: flex;
            gap: var(--spacing-md);
            align-items: flex-start;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--color-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            flex-shrink: 0;
        }
        
        .step-content h4 {
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--color-text-primary);
        }
        
        .step-content p {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--color-text-secondary);
        }
        
        .vision-help-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }
        
        .vision-feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .vision-feature-list li {
            padding: var(--spacing-xs) 0;
            border-bottom: 1px solid var(--color-border);
            color: var(--color-text-secondary);
        }
        
        .vision-feature-list li:last-child {
            border-bottom: none;
        }
        
        .vision-faq {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }
        
        .faq-item h4 {
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--color-primary);
        }
        
        .faq-item p {
            margin: 0;
            color: var(--color-text-secondary);
        }
        </style>
        <?php
        echo ob_get_clean();
    }
    
    /**
     * Handle keyword extraction AJAX request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleKeywordExtraction($data)
    {
        try {
            // Validate input
            if (!isset($data['page_id']) || !is_numeric($data['page_id'])) {
                return [
                    'success' => false,
                    'data' => ['error' => 'Invalid page ID provided']
                ];
            }
            
            $page_id = absint($data['page_id']);
            $model = sanitize_text_field($data['model'] ?? 'gpt-3.5-turbo');
            
            // Check if page exists
            $page = get_post($page_id);
            if (!$page || $page->post_status !== 'publish') {
                return [
                    'success' => false,
                    'data' => ['error' => 'Page not found or not published']
                ];
            }
            
            // Extract keywords
            $result = $this->keywordExtractor->extractFromPage($page_id, $model);
            
            if ($result['success']) {
                // Save to database
                $this->databaseHandler->savePageData($page_id, [
                    'title' => $page->post_title,
                    'url' => get_permalink($page_id),
                    'status' => $page->post_status,
                    'keywords' => implode(', ', $result['keywords']),
                    'scan_model' => $model
                ]);
                
                $this->databaseHandler->log('info', 'Keywords extracted successfully', [
                    'page_id' => $page_id,
                    'keyword_count' => count($result['keywords']),
                    'model' => $model
                ]);
                
                return [
                    'success' => true,
                    'data' => [
                        'message' => sprintf('Extracted %d keywords from "%s"', count($result['keywords']), $page->post_title),
                        'keywords' => $result['keywords'],
                        'page_id' => $page_id
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'data' => ['error' => $result['error'] ?? 'Keyword extraction failed']
                ];
            }
        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Keyword extraction error', [
                'error' => $e->getMessage(),
                'page_id' => $data['page_id'] ?? 'unknown'
            ]);
            
            return [
                'success' => false,
                'data' => ['error' => 'An unexpected error occurred during keyword extraction']
            ];
        }
    }
    
    /**
     * Handle article generation AJAX request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleArticleGeneration($data)
    {
        try {
            // Validate input
            $required_fields = ['topic', 'models', 'word_count'];
            foreach ($required_fields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    return [
                        'success' => false,
                        'data' => ['error' => "Missing required field: {$field}"]
                    ];
                }
            }
            
            $topic = sanitize_text_field($data['topic']);
            $models = array_map('sanitize_text_field', (array) $data['models']);
            $word_count = absint($data['word_count']);
            $internal_linking = !empty($data['internal_linking']);
            $generate_image = !empty($data['generate_image']);
            $custom_prompt = sanitize_textarea_field($data['custom_prompt'] ?? '');
            
            // Validate word count
            if ($word_count < 100 || $word_count > 5000) {
                return [
                    'success' => false,
                    'data' => ['error' => 'Word count must be between 100 and 5000']
                ];
            }
            
            // Get assigned user
            $assigned_user_id = $this->databaseHandler->getBlogSetting('assigned_user_id', 0);
            if (!$assigned_user_id) {
                return [
                    'success' => false,
                    'data' => ['error' => 'No user assigned for article ownership. Please configure in Settings.']
                ];
            }
            
            $start_time = microtime(true);
            
            // Generate article with selected model (use first model for now)
            $model = $models[0];
            $options = [
                'word_count' => $word_count,
                'custom_prompt' => $custom_prompt
            ];
            $result = $this->articleGenerator->generateFullArticle($topic, $model, $options);

            if (is_wp_error($result)) {
                return [
                    'success' => false,
                    'data' => ['error' => $result->get_error_message()]
                ];
            }

            $article_content = $result['content'];
            $generation_time = round(microtime(true) - $start_time, 2);
            
            // Add internal links if requested
            $internal_links = [];
            if ($internal_linking) {
                $linking_result = $this->internalLinker->addLinksToContent($article_content, $topic);
                if ($linking_result['success']) {
                    $article_content = $linking_result['content'];
                    $internal_links = $linking_result['links'];
                }
            }
            
            // Generate image if requested
            $image_data = null;
            if ($generate_image) {
                $image_result = $this->imageGenerator->generateForArticle($topic, $result['title'] ?? $topic);
                if ($image_result['success']) {
                    $image_data = $image_result['image_data'];
                }
            }
            
            // Save article data
            $article_id = $this->databaseHandler->saveArticleData([
                'user_id' => $assigned_user_id,
                'title' => $result['title'] ?? $topic,
                'prompt_data' => [
                    'topic' => $topic,
                    'word_count' => $word_count,
                    'custom_prompt' => $custom_prompt
                ],
                'generation_models' => $models,
                'internal_links' => $internal_links,
                'image_data' => $image_data,
                'word_count' => str_word_count(strip_tags($article_content)),
                'status' => 'draft',
                'ai_model_used' => $model,
                'generation_time_seconds' => $generation_time
            ]);

            // Save image placeholders if any were detected
            $image_placeholders = [];
            if (!empty($result['image_placeholders'])) {
                foreach ($result['image_placeholders'] as $placeholder) {
                    $placeholder_id = $this->imageGenerator->saveImagePlaceholder(
                        $article_id,
                        $placeholder['full_match'],
                        $placeholder['suggested_prompt']
                    );

                    if ($placeholder_id) {
                        $image_placeholders[] = array_merge($placeholder, ['id' => $placeholder_id]);
                    }
                }
            }
            
            $this->databaseHandler->log('info', 'Article generated successfully', [
                'topic' => $topic,
                'model' => $model,
                'word_count' => $word_count,
                'generation_time' => $generation_time,
                'article_id' => $article_id
            ]);
            
            return [
                'success' => true,
                'data' => [
                    'message' => 'Article generated successfully!',
                    'id' => $article_id,
                    'title' => $result['title'] ?? $topic,
                    'content' => $article_content,
                    'model' => $model,
                    'word_count' => str_word_count(strip_tags($article_content)),
                    'generation_time' => $generation_time,
                    'internal_links' => $internal_links,
                    'image_data' => $image_data,
                    'image_placeholders' => $image_placeholders
                ]
            ];
        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Article generation error', [
                'error' => $e->getMessage(),
                'topic' => $data['topic'] ?? 'unknown'
            ]);
            
            return [
                'success' => false,
                'data' => ['error' => 'An unexpected error occurred during article generation']
            ];
        }
    }
    
    /**
     * Handle image generation AJAX request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleImageGeneration($data)
    {
        try {
            $topic = sanitize_text_field($data['topic'] ?? '');
            $title = sanitize_text_field($data['title'] ?? $topic);
            $custom_prompt = sanitize_textarea_field($data['custom_prompt'] ?? '');
            
            if (empty($topic)) {
                return [
                    'success' => false,
                    'data' => ['error' => 'Topic is required for image generation']
                ];
            }
            
            $result = $this->imageGenerator->generateForArticle($topic, $title, $custom_prompt);
            
            if ($result['success']) {
                $this->databaseHandler->log('info', 'Image generated successfully', [
                    'topic' => $topic,
                    'image_id' => $result['image_data']['attachment_id'] ?? null
                ]);
                
                return [
                    'success' => true,
                    'data' => [
                        'message' => 'Image generated and uploaded successfully!',
                        'image_data' => $result['image_data']
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'data' => ['error' => $result['error'] ?? 'Image generation failed']
                ];
            }
        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Image generation error', [
                'error' => $e->getMessage(),
                'topic' => $data['topic'] ?? 'unknown'
            ]);
            
            return [
                'success' => false,
                'data' => ['error' => 'An unexpected error occurred during image generation']
            ];
        }
    }
    
    /**
     * Handle user creation AJAX request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleUserCreation($data)
    {
        try {
            // If no data provided, generate sensible defaults
            $site_name = get_bloginfo('name');
            $site_domain = parse_url(home_url(), PHP_URL_HOST);
            
            $username = sanitize_user($data['username'] ?? 'blogwriter_' . sanitize_title($site_name));
            $email = sanitize_email($data['email'] ?? 'blogwriter@' . $site_domain);
            $display_name = sanitize_text_field($data['display_name'] ?? 'Blog Writer');
            $role = sanitize_text_field($data['role'] ?? 'author');
            
            // If auto-generated username exists, add numbers
            $original_username = $username;
            $counter = 1;
            while (username_exists($username)) {
                $username = $original_username . '_' . $counter;
                $counter++;
            }
            
            // If auto-generated email exists, modify it
            $original_email = $email;
            $counter = 1;
            while (email_exists($email)) {
                $email_parts = explode('@', $original_email);
                $email = $email_parts[0] . '_' . $counter . '@' . $email_parts[1];
                $counter++;
            }
            
            $result = $this->userManager->createDedicatedUser([
                'username' => $username,
                'email' => $email,
                'display_name' => $display_name,
                'role' => $role
            ]);
            
            if (is_array($result) && isset($result['user_id'])) {
                // Set as assigned user
                $this->databaseHandler->setBlogSetting('assigned_user_id', $result['user_id'], 'integer');
                
                $this->databaseHandler->log('info', 'User created successfully', [
                    'user_id' => $result['user_id'],
                    'username' => $username,
                    'role' => $role
                ]);
                
                return [
                    'success' => true,
                    'data' => [
                        'message' => 'User created and assigned successfully!',
                        'user_id' => $result['user_id'],
                        'username' => $username,
                        'email' => $email,
                        'display_name' => $display_name,
                        'password' => $result['password'], // Include generated password
                        'edit_url' => admin_url('user-edit.php?user_id=' . $result['user_id']),
                        'reload_page' => true // Signal to reload the dashboard
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'data' => ['error' => is_wp_error($result) ? $result->get_error_message() : 'User creation failed']
                ];
            }
        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'User creation error', [
                'error' => $e->getMessage(),
                'username' => $data['username'] ?? 'unknown'
            ]);
            
            return [
                'success' => false,
                'data' => ['error' => 'An unexpected error occurred during user creation']
            ];
        }
    }
    
    /**
     * Handle change user AJAX request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleChangeUser($data)
    {
        try {
            // Check permissions
            if (!current_user_can('manage_options')) {
                return [
                    'success' => false,
                    'data' => ['error' => 'Insufficient permissions']
                ];
            }
            
            // Get all WordPress users who can publish posts
            $users = get_users([
                'capability' => 'publish_posts',
                'orderby' => 'display_name',
                'fields' => ['ID', 'display_name', 'user_email', 'user_login']
            ]);
            
            $available_users = [];
            foreach ($users as $user) {
                $available_users[] = [
                    'id' => $user->ID,
                    'display_name' => $user->display_name,
                    'username' => $user->user_login,
                    'email' => $user->user_email,
                    'avatar_url' => get_avatar_url($user->ID),
                    'edit_url' => admin_url('user-edit.php?user_id=' . $user->ID)
                ];
            }
            
            // Get current assigned user
            $current_user_id = $this->databaseHandler->getBlogSetting('assigned_user_id', 0);
            
            return [
                'success' => true,
                'data' => [
                    'available_users' => $available_users,
                    'current_user_id' => $current_user_id,
                    'show_modal' => true
                ]
            ];
        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Change user error', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'data' => ['error' => 'An unexpected error occurred']
            ];
        }
    }
    
    /**
     * Handle assign user AJAX request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleAssignUser($data)
    {
        try {
            // Check permissions
            if (!current_user_can('manage_options')) {
                return [
                    'success' => false,
                    'data' => ['error' => 'Insufficient permissions']
                ];
            }
            
            $user_id = absint($data['user_id'] ?? 0);
            
            if (empty($user_id)) {
                return [
                    'success' => false,
                    'data' => ['error' => 'User ID is required']
                ];
            }
            
            // Use UserManager to set the assigned user
            $result = $this->userManager->setAssignedUser($user_id);
            
            if ($result === true) {
                $user = get_user_by('id', $user_id);
                
                $this->databaseHandler->log('info', 'User assigned successfully', [
                    'user_id' => $user_id,
                    'username' => $user->user_login
                ]);
                
                return [
                    'success' => true,
                    'data' => [
                        'message' => 'User assigned successfully!',
                        'user_id' => $user_id,
                        'username' => $user->user_login,
                        'display_name' => $user->display_name,
                        'reload_page' => true
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'data' => ['error' => is_wp_error($result) ? $result->get_error_message() : 'User assignment failed']
                ];
            }
        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Assign user error', [
                'error' => $e->getMessage(),
                'user_id' => $data['user_id'] ?? 'unknown'
            ]);
            
            return [
                'success' => false,
                'data' => ['error' => 'An unexpected error occurred during user assignment']
            ];
        }
    }
    
    /**
     * Handle settings save AJAX request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleSettingsSave($data)
    {
        try {
            $settings_map = [
                'openrouter_api_key' => ['type' => 'string', 'group' => 'api', 'encrypt' => true],
                'default_ai_model' => ['type' => 'string', 'group' => 'ai'],
                'default_word_count' => ['type' => 'integer', 'group' => 'content'],
                'assigned_user_id' => ['type' => 'integer', 'group' => 'general'],
                'main_content_prompt' => ['type' => 'text', 'group' => 'prompts'],
                'seo_optimization_prompt' => ['type' => 'text', 'group' => 'prompts'],
                'image_generation_prompt' => ['type' => 'text', 'group' => 'prompts'],
                'auto_extract_keywords' => ['type' => 'boolean', 'group' => 'seo'],
                'max_internal_links' => ['type' => 'integer', 'group' => 'seo'],
                'keyword_extraction_model' => ['type' => 'string', 'group' => 'ai']
            ];
            
            $saved_count = 0;
            $errors = [];
            
            foreach ($settings_map as $key => $config) {
                if (isset($data[$key])) {
                    $value = $data[$key];
                    
                    // Sanitize based on type
                    switch ($config['type']) {
                        case 'integer':
                            $value = absint($value);
                            break;
                        case 'boolean':
                            $value = !empty($value) && $value !== '0';
                            break;
                        case 'text':
                            $value = sanitize_textarea_field($value);
                            break;
                        default:
                            $value = sanitize_text_field($value);
                    }
                    
                    $success = $this->databaseHandler->setBlogSetting(
                        $key,
                        $value,
                        $config['type'],
                        $config['group'],
                        $config['encrypt'] ?? false
                    );
                    
                    if ($success) {
                        $saved_count++;
                    } else {
                        $errors[] = "Failed to save {$key}";
                    }
                }
            }
            
            if ($saved_count > 0) {
                $this->databaseHandler->log('info', 'Settings saved successfully', [
                    'settings_count' => $saved_count
                ]);
                
                return [
                    'success' => true,
                    'data' => [
                        'message' => sprintf('%d settings saved successfully!', $saved_count),
                        'settings' => array_keys($data)
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'data' => ['error' => 'No settings were saved. ' . implode(', ', $errors)]
                ];
            }
        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Settings save error', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'data' => ['error' => 'An unexpected error occurred while saving settings']
            ];
        }
    }
    
    /**
     * Handle database repair AJAX request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleDatabaseRepair($data)
    {
        try {
            // Check permissions
            if (!current_user_can('manage_options')) {
                return [
                    'success' => false,
                    'data' => ['error' => 'Insufficient permissions']
                ];
            }
            
            // Force recreate all database tables
            $this->databaseHandler->forceRecreate();
            
            $this->databaseHandler->log('info', 'Database repair completed', [
                'user_id' => get_current_user_id()
            ]);
            
            return [
                'success' => true,
                'data' => [
                    'message' => 'Database repair completed successfully! All tables have been recreated.',
                    'reload_page' => true // Signal to reload the page
                ]
            ];
        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Database repair error', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'data' => ['error' => 'Database repair failed: ' . $e->getMessage()]
            ];
        }
    }
    
    /**
     * Handle multi-provider API test AJAX request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleProviderApiTest($data)
    {
        try {
            $provider = sanitize_text_field($data['provider'] ?? '');
            $api_key = sanitize_text_field($data['api_key'] ?? '');
            
            $this->databaseHandler->log('info', 'Multi-Provider API Test Started', [
                'provider' => $provider,
                'key_length' => strlen($api_key),
                'user_id' => get_current_user_id()
            ]);
            
            if (empty($provider)) {
                return [
                    'success' => false,
                    'data' => ['error' => 'Provider is required for testing']
                ];
            }
            
            if (empty($api_key)) {
                return [
                    'success' => false,
                    'data' => ['error' => 'API key is required for testing']
                ];
            }
            
            $start_time = microtime(true);
            $test_result = $this->testProviderConnection($provider, $api_key);
            $response_time = round(microtime(true) - $start_time, 3);
            
            if ($test_result['success']) {
                // Save the working API key
                $this->databaseHandler->setBlogSetting($provider . '_api_key', $api_key);
                
                $this->databaseHandler->log('info', 'API test successful', [
                    'provider' => $provider,
                    'response_time' => $response_time,
                    'models_available' => $test_result['models_available'] ?? 0
                ]);
                
                return [
                    'success' => true,
                    'data' => [
                        'message' => sprintf('✅ %s connection successful!', ucfirst($provider)),
                        'provider' => $provider,
                        'models_available' => $test_result['models_available'] ?? 0,
                        'response_time' => $response_time,
                        'details' => $test_result['details'] ?? ''
                    ]
                ];
            } else {
                $this->databaseHandler->log('warning', 'API test failed', [
                    'provider' => $provider,
                    'error' => $test_result['error'],
                    'response_time' => $response_time
                ]);
                
                return [
                    'success' => false,
                    'data' => [
                        'error' => sprintf('❌ %s test failed: %s', ucfirst($provider), $test_result['error']),
                        'provider' => $provider
                    ]
                ];
            }
        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'API test error', [
                'provider' => $provider ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'data' => ['error' => 'Unexpected error during API testing: ' . $e->getMessage()]
            ];
        }
    }
    
    /**
     * Handle test all APIs request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleTestAllApis($data)
    {
        try {
            $providers = ['openrouter', 'openai', 'anthropic', 'groq', 'google'];
            $results = [];
            $successful = 0;
            $total_tested = 0;
            
            foreach ($providers as $provider) {
                $api_key = $this->databaseHandler->getBlogSetting($provider . '_api_key', '');
                
                if (!empty($api_key)) {
                    $total_tested++;
                    $test_result = $this->testProviderConnection($provider, $api_key);
                    
                    if ($test_result['success']) {
                        $successful++;
                    }
                    
                    $results[$provider] = [
                        'success' => $test_result['success'],
                        'message' => $test_result['success'] ? 'Connected' : $test_result['error'],
                        'models' => $test_result['models_available'] ?? 0
                    ];
                }
            }
            
            $this->databaseHandler->log('info', 'Bulk API test completed', [
                'total_tested' => $total_tested,
                'successful' => $successful,
                'results' => $results
            ]);
            
            return [
                'success' => true,
                'data' => [
                    'message' => sprintf('Tested %d providers: %d successful, %d failed', 
                                        $total_tested, $successful, $total_tested - $successful),
                    'results' => $results,
                    'summary' => [
                        'total' => $total_tested,
                        'successful' => $successful,
                        'failed' => $total_tested - $successful
                    ]
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'data' => ['error' => 'Bulk test failed: ' . $e->getMessage()]
            ];
        }
    }
    
    /**
     * Handle API key save
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleApiKeySave($data)
    {
        try {
            $provider = sanitize_text_field($data['provider'] ?? '');
            $api_key = sanitize_text_field($data['api_key'] ?? '');
            
            $this->databaseHandler->log('info', 'API Key Save Request', [
                'provider' => $provider,
                'key_length' => strlen($api_key),
                'user_id' => get_current_user_id(),
                'data_received' => array_keys($data)
            ]);
            
            if (empty($provider)) {
                $this->databaseHandler->log('warning', 'API Key Save Failed - No Provider', ['data' => $data]);
                return [
                    'success' => false,
                    'data' => ['error' => 'Provider is required']
                ];
            }
            
            if (empty($api_key)) {
                $this->databaseHandler->log('info', 'API Key Save - Empty Key', ['provider' => $provider]);
                // Allow saving empty keys to clear them
            }
            
            // Save the API key
            $setting_key = $provider . '_api_key';
            $save_result = $this->databaseHandler->setBlogSetting($setting_key, $api_key);
            
            $this->databaseHandler->log('info', 'API Key Save Result', [
                'provider' => $provider,
                'setting_key' => $setting_key,
                'save_result' => $save_result,
                'saved_length' => strlen($api_key)
            ]);
            
            // Verify the save by reading it back
            $verification = $this->databaseHandler->getBlogSetting($setting_key, 'NOT_FOUND');
            $verification_match = ($verification === $api_key);
            
            $this->databaseHandler->log('info', 'API Key Save Verification', [
                'provider' => $provider,
                'verification_length' => strlen($verification),
                'matches_input' => $verification_match,
                'verification_preview' => $verification ? substr($verification, 0, 8) . '...' : 'EMPTY'
            ]);
            
            return [
                'success' => true,
                'data' => [
                    'message' => sprintf('%s API key saved successfully', ucfirst($provider)),
                    'provider' => $provider,
                    'verified' => $verification_match
                ]
            ];
        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'API Key Save Exception', [
                'provider' => $provider ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'data' => ['error' => 'Failed to save API key: ' . $e->getMessage()]
            ];
        }
    }
    
    /**
     * Test connection for specific provider
     * 
     * @param string $provider Provider name
     * @param string $api_key API key to test
     * @return array
     */
    private function testProviderConnection($provider, $api_key)
    {
        switch ($provider) {
            case 'openrouter':
                return $this->testOpenRouterConnection($api_key);
            case 'openai':
                return $this->testOpenAIConnection($api_key);
            case 'anthropic':
                return $this->testAnthropicConnection($api_key);
            case 'groq':
                return $this->testGroqConnection($api_key);
            case 'google':
                return $this->testGoogleConnection($api_key);
            default:
                return [
                    'success' => false,
                    'error' => 'Unknown provider: ' . $provider
                ];
        }
    }
    
    /**
     * Test OpenRouter API connection
     * 
     * @param string $api_key
     * @return array
     */
    private function testOpenRouterConnection($api_key)
    {
        $response = wp_remote_get('https://openrouter.ai/api/v1/models', [
            'headers' => [
                'Authorization' => 'Bearer ' . $api_key,
                'HTTP-Referer' => home_url()
            ],
            'timeout' => 15
        ]);
        
        if (is_wp_error($response)) {
            return [
                'success' => false,
                'error' => $response->get_error_message()
            ];
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code === 200) {
            $body = json_decode(wp_remote_retrieve_body($response), true);
            $models_count = isset($body['data']) ? count($body['data']) : 0;
            
            return [
                'success' => true,
                'models_available' => $models_count,
                'details' => "Access to {$models_count} models"
            ];
        } else {
            return [
                'success' => false,
                'error' => "HTTP {$response_code}: Authentication failed"
            ];
        }
    }
    
    /**
     * Test OpenAI API connection
     * 
     * @param string $api_key
     * @return array
     */
    private function testOpenAIConnection($api_key)
    {
        $response = wp_remote_get('https://api.openai.com/v1/models', [
            'headers' => [
                'Authorization' => 'Bearer ' . $api_key
            ],
            'timeout' => 15
        ]);
        
        if (is_wp_error($response)) {
            return [
                'success' => false,
                'error' => $response->get_error_message()
            ];
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code === 200) {
            $body = json_decode(wp_remote_retrieve_body($response), true);
            $models_count = isset($body['data']) ? count($body['data']) : 0;
            
            return [
                'success' => true,
                'models_available' => $models_count,
                'details' => "GPT models available"
            ];
        } else {
            return [
                'success' => false,
                'error' => "HTTP {$response_code}: Authentication failed"
            ];
        }
    }
    
    /**
     * Test Anthropic API connection
     * 
     * @param string $api_key
     * @return array
     */
    private function testAnthropicConnection($api_key)
    {
        // Anthropic doesn't have a models endpoint, so we test with a minimal request
        $response = wp_remote_post('https://api.anthropic.com/v1/messages', [
            'headers' => [
                'x-api-key' => $api_key,
                'anthropic-version' => '2023-06-01',
                'Content-Type' => 'application/json'
            ],
            'body' => wp_json_encode([
                'model' => 'claude-3-haiku-20240307',
                'max_tokens' => 1,
                'messages' => [[
                    'role' => 'user',
                    'content' => 'Hi'
                ]]
            ]),
            'timeout' => 15
        ]);
        
        if (is_wp_error($response)) {
            return [
                'success' => false,
                'error' => $response->get_error_message()
            ];
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code === 200) {
            return [
                'success' => true,
                'models_available' => 3, // Claude 3 variants
                'details' => 'Claude models available'
            ];
        } else {
            $body = wp_remote_retrieve_body($response);
            $error_data = json_decode($body, true);
            $error_msg = $error_data['error']['message'] ?? "HTTP {$response_code}: Authentication failed";
            
            return [
                'success' => false,
                'error' => $error_msg
            ];
        }
    }
    
    /**
     * Test Groq API connection
     * 
     * @param string $api_key
     * @return array
     */
    private function testGroqConnection($api_key)
    {
        $response = wp_remote_get('https://api.groq.com/openai/v1/models', [
            'headers' => [
                'Authorization' => 'Bearer ' . $api_key
            ],
            'timeout' => 15
        ]);
        
        if (is_wp_error($response)) {
            return [
                'success' => false,
                'error' => $response->get_error_message()
            ];
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code === 200) {
            $body = json_decode(wp_remote_retrieve_body($response), true);
            $models_count = isset($body['data']) ? count($body['data']) : 0;
            
            return [
                'success' => true,
                'models_available' => $models_count,
                'details' => 'Ultra-fast inference available'
            ];
        } else {
            return [
                'success' => false,
                'error' => "HTTP {$response_code}: Authentication failed"
            ];
        }
    }
    
    /**
     * Test Google Gemini API connection
     * 
     * @param string $api_key
     * @return array
     */
    private function testGoogleConnection($api_key)
    {
        $response = wp_remote_get("https://generativelanguage.googleapis.com/v1/models?key={$api_key}", [
            'timeout' => 15
        ]);
        
        if (is_wp_error($response)) {
            return [
                'success' => false,
                'error' => $response->get_error_message()
            ];
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code === 200) {
            $body = json_decode(wp_remote_retrieve_body($response), true);
            $models_count = isset($body['models']) ? count($body['models']) : 0;
            
            return [
                'success' => true,
                'models_available' => $models_count,
                'details' => 'Gemini models available'
            ];
        } else {
            return [
                'success' => false,
                'error' => "HTTP {$response_code}: Authentication failed"
            ];
        }
    }
    
    /**
     * Handle get pages AJAX request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleGetPages($data)
    {
        try {
            $status = sanitize_text_field($data['status'] ?? '');
            $limit = absint($data['limit'] ?? 50);
            $offset = absint($data['offset'] ?? 0);
            
            $args = [
                'limit' => $limit,
                'offset' => $offset
            ];
            
            if (!empty($status)) {
                $args['status'] = $status;
            }
            
            $pages = $this->databaseHandler->getAllPages($args);
            
            return [
                'success' => true,
                'data' => [
                    'pages' => $pages,
                    'total' => count($pages)
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'data' => ['error' => 'Failed to retrieve pages: ' . $e->getMessage()]
            ];
        }
    }
    
    /**
     * Handle get articles AJAX request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleGetArticles($data)
    {
        try {
            $status = sanitize_text_field($data['status'] ?? '');
            $user_id = absint($data['user_id'] ?? 0);
            $limit = absint($data['limit'] ?? 50);
            $offset = absint($data['offset'] ?? 0);
            
            $args = [
                'limit' => $limit,
                'offset' => $offset
            ];
            
            if (!empty($status)) {
                $args['status'] = $status;
            }
            
            if ($user_id > 0) {
                $args['user_id'] = $user_id;
            }
            
            $articles = $this->databaseHandler->getArticles($args);
            
            return [
                'success' => true,
                'data' => [
                    'articles' => $articles,
                    'total' => count($articles)
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'data' => ['error' => 'Failed to retrieve articles: ' . $e->getMessage()]
            ];
        }
    }
    
    /**
     * Handle link preview AJAX request
     * 
     * @param array $data Request data
     * @return array
     */
    public function handleLinkPreview($data)
    {
        try {
            $content = wp_kses_post($data['content'] ?? '');
            $topic = sanitize_text_field($data['topic'] ?? '');
            
            if (empty($content)) {
                return [
                    'success' => false,
                    'data' => ['error' => 'Content is required for link preview']
                ];
            }
            
            $result = $this->internalLinker->previewLinksForContent($content, $topic);
            
            if ($result['success']) {
                return [
                    'success' => true,
                    'data' => [
                        'preview_content' => $result['content'],
                        'suggested_links' => $result['links'],
                        'link_count' => count($result['links'])
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'data' => ['error' => $result['error'] ?? 'Link preview failed']
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'data' => ['error' => 'An unexpected error occurred during link preview']
            ];
        }
    }
    
    /**
     * Handle form success events
     */
    public function handleFormSuccess($form, $data)
    {
        if ($this->databaseHandler && $this->databaseHandler->tableExists('logs')) {
            $this->databaseHandler->log('info', 'Form submitted successfully', [
                'form_class' => $form ? $form->attr('class') : 'unknown',
                'data_keys' => array_keys($data)
            ]);
        }
    }
    
    /**
     * Handle tab change events
     */
    public function handleTabChange($tab_id)
    {
        if ($this->databaseHandler && $this->databaseHandler->tableExists('logs')) {
            $this->databaseHandler->log('debug', 'Tab changed', [
                'tab_id' => $tab_id,
                'user_id' => get_current_user_id()
            ]);
        }
    }
    
    /**
     * Handle get image placeholders AJAX request
     *
     * @param array $data Request data
     * @return array
     */
    public function handleGetImagePlaceholders($data)
    {
        try {
            $article_id = absint($data['article_id'] ?? 0);

            if (empty($article_id)) {
                return [
                    'success' => false,
                    'data' => ['error' => __('Article ID is required.', $this->config['text_domain'])]
                ];
            }

            $placeholders = $this->imageGenerator->getArticlePlaceholders($article_id);

            return [
                'success' => true,
                'data' => [
                    'placeholders' => $placeholders,
                    'count' => count($placeholders)
                ]
            ];

        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Get image placeholders failed', [
                'error' => $e->getMessage(),
                'article_id' => $data['article_id'] ?? null
            ]);

            return [
                'success' => false,
                'data' => ['error' => __('Failed to retrieve image placeholders.', $this->config['text_domain'])]
            ];
        }
    }

    /**
     * Handle generate placeholder image AJAX request
     *
     * @param array $data Request data
     * @return array
     */
    public function handleGeneratePlaceholderImage($data)
    {
        try {
            $placeholder_id = absint($data['placeholder_id'] ?? 0);
            $service = sanitize_text_field($data['service'] ?? 'dalle');
            $style = sanitize_text_field($data['style'] ?? 'natural');
            $size = sanitize_text_field($data['size'] ?? '1024x1024');

            if (empty($placeholder_id)) {
                return [
                    'success' => false,
                    'data' => ['error' => __('Placeholder ID is required.', $this->config['text_domain'])]
                ];
            }

            $options = [
                'service' => $service,
                'style' => $style,
                'size' => $size
            ];

            $result = $this->imageGenerator->generatePlaceholderImage($placeholder_id, $options);

            if (is_wp_error($result)) {
                return [
                    'success' => false,
                    'data' => ['error' => $result->get_error_message()]
                ];
            }

            return [
                'success' => true,
                'data' => [
                    'image' => $result,
                    'placeholder_id' => $placeholder_id
                ]
            ];

        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Generate placeholder image failed', [
                'error' => $e->getMessage(),
                'placeholder_id' => $data['placeholder_id'] ?? null
            ]);

            return [
                'success' => false,
                'data' => ['error' => __('Failed to generate placeholder image.', $this->config['text_domain'])]
            ];
        }
    }

    /**
     * Handle update placeholder selection AJAX request
     *
     * @param array $data Request data
     * @return array
     */
    public function handleUpdatePlaceholderSelection($data)
    {
        try {
            $placeholder_id = absint($data['placeholder_id'] ?? 0);
            $image_url = esc_url_raw($data['image_url'] ?? '');
            $provider = sanitize_text_field($data['provider'] ?? '');
            $attachment_id = absint($data['attachment_id'] ?? 0);

            if (empty($placeholder_id) || empty($image_url)) {
                return [
                    'success' => false,
                    'data' => ['error' => __('Placeholder ID and image URL are required.', $this->config['text_domain'])]
                ];
            }

            $result = $this->imageGenerator->updatePlaceholderSelection($placeholder_id, $image_url, $provider, $attachment_id);

            if (!$result) {
                return [
                    'success' => false,
                    'data' => ['error' => __('Failed to update placeholder selection.', $this->config['text_domain'])]
                ];
            }

            return [
                'success' => true,
                'data' => [
                    'placeholder_id' => $placeholder_id,
                    'selected_image_url' => $image_url
                ]
            ];

        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Update placeholder selection failed', [
                'error' => $e->getMessage(),
                'placeholder_id' => $data['placeholder_id'] ?? null
            ]);

            return [
                'success' => false,
                'data' => ['error' => __('Failed to update placeholder selection.', $this->config['text_domain'])]
            ];
        }
    }

    /**
     * Handle generate multiple drafts AJAX request
     *
     * @param array $data Request data
     * @return array
     */
    public function handleGenerateMultipleDrafts($data)
    {
        try {
            $topic = sanitize_text_field($data['topic'] ?? '');
            $models = array_map('sanitize_text_field', (array) ($data['models'] ?? []));
            $word_count = absint($data['word_count'] ?? 1000);
            $tone = sanitize_text_field($data['tone'] ?? 'professional');
            $audience = sanitize_text_field($data['audience'] ?? 'general');
            $custom_prompt = sanitize_textarea_field($data['custom_prompt'] ?? '');

            if (empty($topic) || empty($models)) {
                return [
                    'success' => false,
                    'data' => ['error' => __('Topic and at least one AI model are required.', $this->config['text_domain'])]
                ];
            }

            $drafts = [];
            $errors = [];

            foreach ($models as $model) {
                try {
                    $options = [
                        'word_count' => $word_count,
                        'tone' => $tone,
                        'audience' => $audience,
                        'custom_prompt' => $custom_prompt
                    ];

                    $result = $this->articleGenerator->generateFullArticle($topic, $model, $options);

                    if (is_wp_error($result)) {
                        $errors[] = [
                            'model' => $model,
                            'error' => $result->get_error_message()
                        ];
                        continue;
                    }

                    // Calculate additional metrics for comparison
                    $content_text = strip_tags($result['content']);
                    $readability_score = $this->calculateReadabilityScore($content_text);
                    $seo_score = $this->calculateSEOScore($result);

                    $drafts[] = [
                        'model' => $model,
                        'model_info' => $this->articleGenerator->getAvailableModels()[$model] ?? [],
                        'title' => $result['title'],
                        'content' => $result['content'],
                        'excerpt' => $result['excerpt'],
                        'word_count' => $result['word_count'],
                        'headings' => $result['headings'],
                        'image_placeholders' => $result['image_placeholders'] ?? [],
                        'generation_time' => $result['generation_time'],
                        'readability_score' => $readability_score,
                        'seo_score' => $seo_score,
                        'internal_links_count' => count($result['internal_links'] ?? []),
                        'meta_data' => $result['meta_data']
                    ];

                    // Add small delay between requests to avoid rate limiting
                    sleep(1);

                } catch (Exception $e) {
                    $errors[] = [
                        'model' => $model,
                        'error' => $e->getMessage()
                    ];
                }
            }

            if (empty($drafts)) {
                return [
                    'success' => false,
                    'data' => [
                        'error' => __('Failed to generate any drafts.', $this->config['text_domain']),
                        'errors' => $errors
                    ]
                ];
            }

            return [
                'success' => true,
                'data' => [
                    'drafts' => $drafts,
                    'errors' => $errors,
                    'total_requested' => count($models),
                    'successful_generations' => count($drafts),
                    'failed_generations' => count($errors)
                ]
            ];

        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Multiple drafts generation failed', [
                'error' => $e->getMessage(),
                'topic' => $data['topic'] ?? null
            ]);

            return [
                'success' => false,
                'data' => ['error' => __('Failed to generate multiple drafts.', $this->config['text_domain'])]
            ];
        }
    }

    /**
     * Handle select draft AJAX request
     *
     * @param array $data Request data
     * @return array
     */
    public function handleSelectDraft($data)
    {
        try {
            $draft_index = absint($data['draft_index'] ?? 0);
            $drafts = $data['drafts'] ?? [];

            if (!isset($drafts[$draft_index])) {
                return [
                    'success' => false,
                    'data' => ['error' => __('Invalid draft selection.', $this->config['text_domain'])]
                ];
            }

            $selected_draft = $drafts[$draft_index];

            // Save the selected draft as an article
            $assigned_user_id = $this->databaseHandler->getBlogSetting('assigned_user_id', 0);

            if (!$assigned_user_id) {
                return [
                    'success' => false,
                    'data' => ['error' => __('No user assigned for article ownership.', $this->config['text_domain'])]
                ];
            }

            $article_id = $this->databaseHandler->saveArticleData([
                'user_id' => $assigned_user_id,
                'title' => $selected_draft['title'],
                'prompt_data' => [
                    'topic' => $data['topic'] ?? '',
                    'word_count' => $selected_draft['word_count'],
                    'model' => $selected_draft['model']
                ],
                'generation_models' => [$selected_draft['model']],
                'internal_links' => [],
                'image_data' => null,
                'word_count' => $selected_draft['word_count'],
                'status' => 'draft',
                'ai_model_used' => $selected_draft['model'],
                'generation_time_seconds' => $selected_draft['generation_time']
            ]);

            // Save image placeholders if any
            $image_placeholders = [];
            if (!empty($selected_draft['image_placeholders'])) {
                foreach ($selected_draft['image_placeholders'] as $placeholder) {
                    $placeholder_id = $this->imageGenerator->saveImagePlaceholder(
                        $article_id,
                        $placeholder['full_match'],
                        $placeholder['suggested_prompt']
                    );

                    if ($placeholder_id) {
                        $image_placeholders[] = array_merge($placeholder, ['id' => $placeholder_id]);
                    }
                }
            }

            return [
                'success' => true,
                'data' => [
                    'article_id' => $article_id,
                    'selected_draft' => $selected_draft,
                    'image_placeholders' => $image_placeholders
                ]
            ];

        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Draft selection failed', [
                'error' => $e->getMessage(),
                'draft_index' => $data['draft_index'] ?? null
            ]);

            return [
                'success' => false,
                'data' => ['error' => __('Failed to select draft.', $this->config['text_domain'])]
            ];
        }
    }

    /**
     * Calculate readability score for content
     *
     * @param string $content
     * @return int
     */
    protected function calculateReadabilityScore($content)
    {
        // Simple readability calculation based on sentence and word length
        $sentences = preg_split('/[.!?]+/', $content);
        $words = str_word_count($content);
        $sentences_count = count(array_filter($sentences));

        if ($sentences_count === 0) return 0;

        $avg_sentence_length = $words / $sentences_count;

        // Simple scoring: shorter sentences = higher readability
        if ($avg_sentence_length <= 15) return 90;
        if ($avg_sentence_length <= 20) return 75;
        if ($avg_sentence_length <= 25) return 60;
        return 45;
    }

    /**
     * Calculate SEO score for article
     *
     * @param array $article_data
     * @return int
     */
    protected function calculateSEOScore($article_data)
    {
        $score = 0;

        // Title length (optimal: 30-60 characters)
        $title_length = strlen($article_data['title']);
        if ($title_length >= 30 && $title_length <= 60) {
            $score += 20;
        } elseif ($title_length >= 20 && $title_length <= 70) {
            $score += 10;
        }

        // Word count (optimal: 800-2000 words)
        $word_count = $article_data['word_count'];
        if ($word_count >= 800 && $word_count <= 2000) {
            $score += 25;
        } elseif ($word_count >= 500) {
            $score += 15;
        }

        // Headings structure
        $headings_count = count($article_data['headings'] ?? []);
        if ($headings_count >= 3) {
            $score += 20;
        } elseif ($headings_count >= 1) {
            $score += 10;
        }

        // Content structure
        if (!empty($article_data['excerpt'])) {
            $score += 15;
        }

        // Image placeholders
        if (!empty($article_data['image_placeholders'])) {
            $score += 20;
        }

        return min($score, 100);
    }

    /**
     * Handle refresh page balance AJAX request
     *
     * @param array $data Request data
     * @return array
     */
    public function handleRefreshPageBalance($data)
    {
        try {
            $updated_count = $this->databaseHandler->refreshAllPageBalance();

            $this->databaseHandler->log('info', 'Page balance refreshed', [
                'updated_pages' => $updated_count
            ]);

            return [
                'success' => true,
                'data' => [
                    'message' => sprintf(__('Updated balance data for %d pages.', $this->config['text_domain']), $updated_count),
                    'updated_count' => $updated_count,
                    'balance_summary' => $this->databaseHandler->getPageBalanceSummary(),
                    'balance_insights' => $this->databaseHandler->getPageBalanceInsights(),
                    'pages_needing_attention' => $this->databaseHandler->getPagesNeedingAttention(5)
                ]
            ];

        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Page balance refresh failed', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'data' => ['error' => __('Failed to refresh page balance.', $this->config['text_domain'])]
            ];
        }
    }

    /**
     * Handle generate for page AJAX request
     *
     * @param array $data Request data
     * @return array
     */
    public function handleGenerateForPage($data)
    {
        try {
            $page_id = absint($data['page_id'] ?? 0);
            $page_title = sanitize_text_field($data['page_title'] ?? '');

            if (empty($page_id)) {
                return [
                    'success' => false,
                    'data' => ['error' => __('Page ID is required.', $this->config['text_domain'])]
                ];
            }

            // Get page data for context
            $page_data = $this->databaseHandler->getPageData($page_id);
            $page = get_post($page_id);

            if (!$page) {
                return [
                    'success' => false,
                    'data' => ['error' => __('Page not found.', $this->config['text_domain'])]
                ];
            }

            // Generate topic based on page content and keywords
            $topic = $this->generateTopicForPage($page, $page_data);

            // Use default model for quick generation
            $model = 'gpt-4';
            $options = [
                'word_count' => 1000,
                'tone' => 'professional',
                'audience' => 'general',
                'target_page_id' => $page_id
            ];

            $result = $this->articleGenerator->generateFullArticle($topic, $model, $options);

            if (is_wp_error($result)) {
                return [
                    'success' => false,
                    'data' => ['error' => $result->get_error_message()]
                ];
            }

            // Save the article
            $assigned_user_id = $this->databaseHandler->getBlogSetting('assigned_user_id', 0);

            if (!$assigned_user_id) {
                return [
                    'success' => false,
                    'data' => ['error' => __('No user assigned for article ownership.', $this->config['text_domain'])]
                ];
            }

            $article_id = $this->databaseHandler->saveArticleData([
                'user_id' => $assigned_user_id,
                'title' => $result['title'],
                'prompt_data' => [
                    'topic' => $topic,
                    'target_page_id' => $page_id,
                    'word_count' => $options['word_count']
                ],
                'generation_models' => [$model],
                'internal_links' => [['page_id' => $page_id, 'page_title' => $page_title]],
                'image_data' => null,
                'word_count' => $result['word_count'],
                'status' => 'draft',
                'ai_model_used' => $model,
                'generation_time_seconds' => $result['generation_time']
            ]);

            // Update page balance
            $this->databaseHandler->updatePageBalance($page_id);

            return [
                'success' => true,
                'data' => [
                    'message' => __('Article generated successfully for the selected page!', $this->config['text_domain']),
                    'article_id' => $article_id,
                    'title' => $result['title'],
                    'topic' => $topic,
                    'page_title' => $page_title
                ]
            ];

        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Generate for page failed', [
                'error' => $e->getMessage(),
                'page_id' => $data['page_id'] ?? null
            ]);

            return [
                'success' => false,
                'data' => ['error' => __('Failed to generate article for page.', $this->config['text_domain'])]
            ];
        }
    }

    /**
     * Generate topic for a specific page
     *
     * @param WP_Post $page WordPress page object
     * @param array|null $page_data Page data with keywords
     * @return string
     */
    protected function generateTopicForPage($page, $page_data)
    {
        $page_title = $page->post_title;
        $keywords = $page_data['keywords'] ?? '';

        // Extract key terms from page title and keywords
        $terms = [];

        // Add page title words
        $title_words = preg_split('/\s+/', strtolower($page_title));
        $terms = array_merge($terms, array_filter($title_words, function($word) {
            return strlen($word) > 3 && !in_array($word, ['the', 'and', 'for', 'with', 'your']);
        }));

        // Add keywords if available
        if (!empty($keywords)) {
            $keyword_list = array_map('trim', explode(',', $keywords));
            $terms = array_merge($terms, array_slice($keyword_list, 0, 3));
        }

        // Create topic based on service category
        $category = $this->databaseHandler->detectServiceCategory($page_title);

        $topic_templates = [
            'commercial' => 'Commercial %s Solutions for Businesses',
            'emergency' => 'Emergency %s Services: What You Need to Know',
            'maintenance' => 'Essential %s Maintenance Tips and Best Practices',
            'installation' => 'Professional %s Installation Guide',
            'general' => 'Complete Guide to %s Services'
        ];

        $template = $topic_templates[$category] ?? $topic_templates['general'];
        $main_term = !empty($terms) ? ucfirst($terms[0]) : 'Service';

        return sprintf($template, $main_term);
    }

    /**
     * Get database handler
     *
     * @return DatabaseHandler
     */
    public function getDatabaseHandler()
    {
        return $this->databaseHandler;
    }
}