{"name": "blog-writer", "version": "1.0.0", "description": "AI-powered blog post generation with intelligent internal linking and SEO optimization for WordPress", "main": "assets/js/admin.js", "scripts": {"build": "echo 'Build process - minify CSS/JS for production'", "dev": "echo 'Development mode - watch for changes'", "lint": "echo 'Lint CSS and JavaScript files'", "test": "echo 'Run plugin tests'"}, "keywords": ["wordpress", "plugin", "ai", "blog", "content-generation", "seo", "openrouter", "gpt", "claude", "gemini", "internal-linking", "keyword-extraction", "content-marketing", "automation"], "author": "LVL3 Marketing <<EMAIL>>", "license": "GPL-2.0-or-later", "homepage": "https://lvl3marketing.com/blog-writer", "repository": {"type": "git", "url": "https://github.com/lvl3marketing/blog-writer"}, "bugs": {"url": "https://github.com/lvl3marketing/blog-writer/issues"}, "devDependencies": {"sass": "^1.0.0", "autoprefixer": "^10.0.0", "postcss": "^8.0.0"}, "browserslist": ["defaults", "not ie 11", "not op_mini all"], "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "wordpress": {"requires": "5.0", "tested": "6.3", "requires_php": "7.4", "text_domain": "blog-writer", "domain_path": "/languages"}}