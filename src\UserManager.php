<?php
/**
 * User Manager
 * 
 * Handles dedicated user creation and article ownership management
 */

namespace BlogWriter;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * UserManager Class
 */
class UserManager
{
    /**
     * Database handler instance
     * 
     * @var BlogWriterDatabaseHandler
     */
    protected $databaseHandler;
    
    /**
     * Plugin configuration
     * 
     * @var array
     */
    protected $config;
    
    /**
     * Constructor
     * 
     * @param BlogWriterDatabaseHandler $databaseHandler
     * @param array $config
     */
    public function __construct($databaseHandler, $config)
    {
        $this->databaseHandler = $databaseHandler;
        $this->config = $config;
    }
    
    /**
     * Create a dedicated user for blog articles
     * 
     * @param array $user_data User creation data
     * @return array|WP_Error
     */
    public function createDedicatedUser($user_data)
    {
        $username = sanitize_user($user_data['username']);
        $email = sanitize_email($user_data['email']);
        $display_name = sanitize_text_field($user_data['display_name'] ?? $username);
        $role = $user_data['role'] ?? 'author';
        $bio = sanitize_textarea_field($user_data['bio'] ?? '');
        
        // Validate inputs
        $validation_result = $this->validateUserData($username, $email, $role);
        if (is_wp_error($validation_result)) {
            return $validation_result;
        }
        
        // Generate secure password
        $password = $this->generateSecurePassword();
        
        try {
            // Create the user
            $user_id = wp_create_user($username, $password, $email);
            
            if (is_wp_error($user_id)) {
                return $user_id;
            }
            
            // Set user role
            $user = new \WP_User($user_id);
            $user->set_role($role);
            
            // Update user meta
            wp_update_user([
                'ID' => $user_id,
                'display_name' => $display_name,
                'description' => $bio
            ]);
            
            // Add custom meta for blog writer users
            update_user_meta($user_id, '_blog_writer_dedicated_user', true);
            update_user_meta($user_id, '_blog_writer_created_date', current_time('mysql'));
            update_user_meta($user_id, '_blog_writer_created_by', get_current_user_id());
            
            // Set as assigned user
            $this->databaseHandler->setBlogSetting('assigned_user_id', $user_id, 'integer');
            
            // Log the user creation
            $this->databaseHandler->log('info', 'Dedicated user created', [
                'user_id' => $user_id,
                'username' => $username,
                'email' => $email,
                'role' => $role,
                'created_by' => get_current_user_id()
            ]);
            
            return [
                'user_id' => $user_id,
                'username' => $username,
                'email' => $email,
                'display_name' => $display_name,
                'role' => $role,
                'password' => $password, // Return password for initial setup
                'login_url' => wp_login_url(),
                'edit_url' => admin_url('user-edit.php?user_id=' . $user_id)
            ];
            
        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'User creation failed', [
                'username' => $username,
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            
            return new \WP_Error('creation_failed', 
                sprintf(__('User creation failed: %s', $this->config['text_domain']), $e->getMessage())
            );
        }
    }
    
    /**
     * Get suggested usernames based on site info
     * 
     * @return array
     */
    public function getSuggestedUsernames()
    {
        $site_name = get_bloginfo('name');
        $site_name_clean = sanitize_title($site_name);
        
        $suggestions = [
            'webmaster',
            'blogwriter',
            'contentmanager',
            'editor',
            'author'
        ];
        
        // Add site-specific suggestions
        if (!empty($site_name_clean)) {
            $suggestions[] = $site_name_clean . '-writer';
            $suggestions[] = $site_name_clean . '-blog';
            $suggestions[] = $site_name_clean . '-content';
        }
        
        // Filter out existing usernames
        $available_suggestions = [];
        foreach ($suggestions as $suggestion) {
            if (!username_exists($suggestion)) {
                $available_suggestions[] = $suggestion;
            }
        }
        
        return array_slice($available_suggestions, 0, 5);
    }
    
    /**
     * Validate user creation data
     * 
     * @param string $username
     * @param string $email
     * @param string $role
     * @return bool|WP_Error
     */
    protected function validateUserData($username, $email, $role)
    {
        // Validate username
        if (empty($username)) {
            return new \WP_Error('empty_username', __('Username cannot be empty.', $this->config['text_domain']));
        }
        
        if (!validate_username($username)) {
            return new \WP_Error('invalid_username', __('Username contains invalid characters.', $this->config['text_domain']));
        }
        
        if (username_exists($username)) {
            return new \WP_Error('username_exists', __('Username already exists.', $this->config['text_domain']));
        }
        
        // Validate email
        if (empty($email) || !is_email($email)) {
            return new \WP_Error('invalid_email', __('Valid email address is required.', $this->config['text_domain']));
        }
        
        if (email_exists($email)) {
            return new \WP_Error('email_exists', __('Email address already exists.', $this->config['text_domain']));
        }
        
        // Validate role
        $allowed_roles = ['author', 'editor', 'contributor'];
        if (!in_array($role, $allowed_roles)) {
            return new \WP_Error('invalid_role', __('Invalid user role specified.', $this->config['text_domain']));
        }
        
        return true;
    }
    
    /**
     * Generate secure password
     * 
     * @param int $length
     * @return string
     */
    protected function generateSecurePassword($length = 16)
    {
        return wp_generate_password($length, true, false);
    }
    
    /**
     * Get current assigned user
     * 
     * @return array|null
     */
    public function getAssignedUser()
    {
        $user_id = $this->databaseHandler->getBlogSetting('assigned_user_id', 0);
        
        if (empty($user_id)) {
            return null;
        }
        
        $user = get_user_by('id', $user_id);
        
        if (!$user) {
            return null;
        }
        
        $is_dedicated = get_user_meta($user_id, '_blog_writer_dedicated_user', true);
        $created_date = get_user_meta($user_id, '_blog_writer_created_date', true);
        $article_count = $this->getUserArticleCount($user_id);
        
        return [
            'user_id' => $user->ID,
            'username' => $user->user_login,
            'email' => $user->user_email,
            'display_name' => $user->display_name,
            'role' => $user->roles[0] ?? '',
            'is_dedicated' => (bool) $is_dedicated,
            'created_date' => $created_date,
            'article_count' => $article_count,
            'avatar_url' => get_avatar_url($user->ID),
            'edit_url' => admin_url('user-edit.php?user_id=' . $user->ID),
            'posts_url' => admin_url('edit.php?author=' . $user->ID)
        ];
    }
    
    /**
     * Set assigned user
     * 
     * @param int $user_id
     * @return bool|WP_Error
     */
    public function setAssignedUser($user_id)
    {
        $user = get_user_by('id', $user_id);
        
        if (!$user) {
            return new \WP_Error('invalid_user', __('User not found.', $this->config['text_domain']));
        }
        
        // Check if user has appropriate capabilities
        if (!user_can($user_id, 'publish_posts')) {
            return new \WP_Error('insufficient_caps', __('User does not have permission to publish posts.', $this->config['text_domain']));
        }
        
        $result = $this->databaseHandler->setBlogSetting('assigned_user_id', $user_id, 'integer');
        
        if ($result) {
            $this->databaseHandler->log('info', 'Assigned user updated', [
                'user_id' => $user_id,
                'username' => $user->user_login,
                'updated_by' => get_current_user_id()
            ]);
            
            return true;
        }
        
        return new \WP_Error('update_failed', __('Failed to update assigned user.', $this->config['text_domain']));
    }
    
    /**
     * Get article count for user
     * 
     * @param int $user_id
     * @return int
     */
    public function getUserArticleCount($user_id)
    {
        $articles = $this->databaseHandler->getArticles(['user_id' => $user_id, 'limit' => 0]);
        return count($articles);
    }
    
    /**
     * Get all eligible users for assignment
     * 
     * @return array
     */
    public function getEligibleUsers()
    {
        $users = get_users([
            'capability' => 'publish_posts',
            'fields' => ['ID', 'user_login', 'display_name', 'user_email']
        ]);
        
        $eligible_users = [];
        
        foreach ($users as $user) {
            $is_dedicated = get_user_meta($user->ID, '_blog_writer_dedicated_user', true);
            $article_count = $this->getUserArticleCount($user->ID);
            
            $eligible_users[] = [
                'user_id' => $user->ID,
                'username' => $user->user_login,
                'display_name' => $user->display_name,
                'email' => $user->user_email,
                'is_dedicated' => (bool) $is_dedicated,
                'article_count' => $article_count,
                'avatar_url' => get_avatar_url($user->ID)
            ];
        }
        
        // Sort by article count (dedicated users first, then by article count)
        usort($eligible_users, function($a, $b) {
            if ($a['is_dedicated'] && !$b['is_dedicated']) return -1;
            if (!$a['is_dedicated'] && $b['is_dedicated']) return 1;
            return $b['article_count'] <=> $a['article_count'];
        });
        
        return $eligible_users;
    }
    
    /**
     * Transfer article ownership
     * 
     * @param array $article_ids
     * @param int $new_user_id
     * @return array|WP_Error
     */
    public function transferArticleOwnership($article_ids, $new_user_id)
    {
        $user = get_user_by('id', $new_user_id);
        
        if (!$user) {
            return new \WP_Error('invalid_user', __('Target user not found.', $this->config['text_domain']));
        }
        
        if (!user_can($new_user_id, 'publish_posts')) {
            return new \WP_Error('insufficient_caps', __('Target user does not have permission to publish posts.', $this->config['text_domain']));
        }
        
        $transferred = 0;
        $errors = [];
        
        foreach ($article_ids as $article_id) {
            $article = $this->databaseHandler->getArticles(['limit' => 1]);
            
            if (empty($article)) {
                $errors[] = "Article ID {$article_id} not found";
                continue;
            }
            
            $article = $article[0];
            
            // Update WordPress post author
            if (!empty($article['post_id'])) {
                $result = wp_update_post([
                    'ID' => $article['post_id'],
                    'post_author' => $new_user_id
                ]);
                
                if (is_wp_error($result)) {
                    $errors[] = "Failed to update post {$article['post_id']}: " . $result->get_error_message();
                    continue;
                }
            }
            
            // Update article record in database
            global $wpdb;
            $table_name = $wpdb->prefix . str_replace('-', '_', $this->config['text_domain']) . '_articles';
            
            $update_result = $wpdb->update(
                $table_name,
                ['user_id' => $new_user_id],
                ['id' => $article_id],
                ['%d'],
                ['%d']
            );
            
            if ($update_result !== false) {
                $transferred++;
            } else {
                $errors[] = "Failed to update article record {$article_id}";
            }
        }
        
        // Log the transfer
        $this->databaseHandler->log('info', 'Article ownership transferred', [
            'article_ids' => $article_ids,
            'new_user_id' => $new_user_id,
            'transferred_count' => $transferred,
            'error_count' => count($errors),
            'transferred_by' => get_current_user_id()
        ]);
        
        return [
            'transferred_count' => $transferred,
            'error_count' => count($errors),
            'errors' => $errors,
            'new_owner' => [
                'user_id' => $user->ID,
                'username' => $user->user_login,
                'display_name' => $user->display_name
            ]
        ];
    }
    
    /**
     * Get user setup status
     * 
     * @return array
     */
    public function getUserSetupStatus()
    {
        $assigned_user = $this->getAssignedUser();
        $setup_complete = !empty($assigned_user);
        
        $status = [
            'setup_complete' => $setup_complete,
            'assigned_user' => $assigned_user,
            'suggestions' => $this->getSuggestedUsernames(),
            'eligible_users' => $this->getEligibleUsers(),
            'next_steps' => []
        ];
        
        if (!$setup_complete) {
            $status['next_steps'][] = __('Create a dedicated user for article ownership', $this->config['text_domain']);
            $status['next_steps'][] = __('Configure user permissions and profile', $this->config['text_domain']);
        } else {
            $status['next_steps'][] = __('Start generating articles', $this->config['text_domain']);
            $status['next_steps'][] = __('Configure AI models and prompts', $this->config['text_domain']);
        }
        
        return $status;
    }
    
    /**
     * Get user management statistics
     * 
     * @return array
     */
    public function getUserStats()
    {
        $assigned_user = $this->getAssignedUser();
        $total_users = count($this->getEligibleUsers());
        $dedicated_users = $this->getDedicatedUserCount();
        
        return [
            'has_assigned_user' => !empty($assigned_user),
            'assigned_user_id' => $assigned_user['user_id'] ?? 0,
            'total_eligible_users' => $total_users,
            'dedicated_users_count' => $dedicated_users,
            'assigned_user_articles' => $assigned_user['article_count'] ?? 0,
            'setup_date' => $assigned_user['created_date'] ?? null
        ];
    }
    
    /**
     * Get count of dedicated users created by plugin
     * 
     * @return int
     */
    protected function getDedicatedUserCount()
    {
        global $wpdb;
        
        $count = $wpdb->get_var("
            SELECT COUNT(*) 
            FROM {$wpdb->usermeta} 
            WHERE meta_key = '_blog_writer_dedicated_user' 
            AND meta_value = '1'
        ");
        
        return (int) $count;
    }
    
    /**
     * Delete dedicated user (with safety checks)
     * 
     * @param int $user_id
     * @param int $reassign_to User ID to reassign posts to
     * @return bool|WP_Error
     */
    public function deleteDedicatedUser($user_id, $reassign_to = null)
    {
        $user = get_user_by('id', $user_id);
        
        if (!$user) {
            return new \WP_Error('invalid_user', __('User not found.', $this->config['text_domain']));
        }
        
        $is_dedicated = get_user_meta($user_id, '_blog_writer_dedicated_user', true);
        
        if (!$is_dedicated) {
            return new \WP_Error('not_dedicated', __('User is not a Blog Writer dedicated user.', $this->config['text_domain']));
        }
        
        // Check if user has articles
        $article_count = $this->getUserArticleCount($user_id);
        
        if ($article_count > 0 && empty($reassign_to)) {
            return new \WP_Error('has_articles', __('User has articles. Please specify a user to reassign articles to.', $this->config['text_domain']));
        }
        
        if (!empty($reassign_to)) {
            $reassign_user = get_user_by('id', $reassign_to);
            if (!$reassign_user) {
                return new \WP_Error('invalid_reassign_user', __('Reassignment user not found.', $this->config['text_domain']));
            }
        }
        
        // If this is the assigned user, clear the assignment
        $assigned_user_id = $this->databaseHandler->getBlogSetting('assigned_user_id', 0);
        if ($assigned_user_id == $user_id) {
            $this->databaseHandler->setBlogSetting('assigned_user_id', 0, 'integer');
        }
        
        // Delete the user
        $result = wp_delete_user($user_id, $reassign_to);
        
        if ($result) {
            $this->databaseHandler->log('info', 'Dedicated user deleted', [
                'user_id' => $user_id,
                'username' => $user->user_login,
                'reassigned_to' => $reassign_to,
                'article_count' => $article_count,
                'deleted_by' => get_current_user_id()
            ]);
            
            return true;
        }
        
        return new \WP_Error('deletion_failed', __('Failed to delete user.', $this->config['text_domain']));
    }
    
    /**
     * Get user creation workflow steps
     * 
     * @return array
     */
    public function getWorkflowSteps()
    {
        $assigned_user = $this->getAssignedUser();
        
        return [
            [
                'step' => 1,
                'title' => __('Create or Select User', $this->config['text_domain']),
                'description' => __('Create a dedicated user account or select an existing user to own generated articles.', $this->config['text_domain']),
                'completed' => !empty($assigned_user),
                'action' => 'create_user'
            ],
            [
                'step' => 2,
                'title' => __('Configure Permissions', $this->config['text_domain']),
                'description' => __('Ensure the user has appropriate permissions to create and publish posts.', $this->config['text_domain']),
                'completed' => !empty($assigned_user) && user_can($assigned_user['user_id'], 'publish_posts'),
                'action' => 'check_permissions'
            ],
            [
                'step' => 3,
                'title' => __('Set Profile Information', $this->config['text_domain']),
                'description' => __('Configure user profile with bio, avatar, and other relevant information.', $this->config['text_domain']),
                'completed' => !empty($assigned_user) && !empty($assigned_user['display_name']),
                'action' => 'configure_profile'
            ],
            [
                'step' => 4,
                'title' => __('Start Creating Content', $this->config['text_domain']),
                'description' => __('Begin generating AI-powered articles with your configured user.', $this->config['text_domain']),
                'completed' => !empty($assigned_user) && $assigned_user['article_count'] > 0,
                'action' => 'create_content'
            ]
        ];
    }
}