<?php
/**
 * Quick API Test - Load this in browser to test API endpoints directly
 * Place this in your WordPress root directory and access via browser
 */

// Load WordPress
require_once 'wp-load.php';

// Test if we can reach the BlogWriter class
if (!class_exists('BlogWriter\\BlogWriter')) {
    die('❌ BlogWriter class not found - plugin may not be activated');
}

// Get the plugin instance
$plugin = BlogWriter\BlogWriter::instance();

echo "<h2>🧪 Blog Writer API Test</h2>";

// Test 1: Check if AJAX action is registered
echo "<h3>Test 1: WordPress AJAX Action</h3>";
$action_name = 'blog-writer_ajax';
echo "Action name: <code>wp_ajax_{$action_name}</code><br>";

// Check if action exists
$wp_filter = $GLOBALS['wp_filter'];
if (isset($wp_filter["wp_ajax_{$action_name}"])) {
    echo "✅ AJAX action is registered<br>";
} else {
    echo "❌ AJAX action NOT registered<br>";
}

// Test 2: Check visionFramework localization
echo "<h3>Test 2: Ad<PERSON></h3>";
if (is_admin()) {
    echo "✅ We're in admin context<br>";
} else {
    echo "❌ Not in admin context<br>";
}

// Test 3: Simulate AJAX call
echo "<h3>Test 3: Direct API Handler Test</h3>";
if (method_exists($plugin, 'handleProviderApiTest')) {
    echo "✅ handleProviderApiTest method exists<br>";
    
    // Simulate the data that would come from AJAX
    $test_data = [
        'provider' => 'openrouter',
        'api_key' => 'test-key-123'
    ];
    
    try {
        $result = $plugin->handleProviderApiTest($test_data);
        echo "📊 Test result: <pre>" . print_r($result, true) . "</pre>";
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ handleProviderApiTest method NOT found<br>";
}

// Test 4: Check database
echo "<h3>Test 4: Database Connection</h3>";
$db_handler = $plugin->databaseHandler ?? null;
if ($db_handler) {
    echo "✅ Database handler exists<br>";
    if (method_exists($db_handler, 'getBlogSetting')) {
        $test_setting = $db_handler->getBlogSetting('test_key', 'default_value');
        echo "✅ Database read test successful<br>";
    }
} else {
    echo "❌ Database handler NOT found<br>";
}

// Test 5: Check current user capabilities
echo "<h3>Test 5: User Permissions</h3>";
if (current_user_can('manage_options')) {
    echo "✅ Current user has manage_options capability<br>";
} else {
    echo "❌ Current user lacks manage_options capability<br>";
}

echo "<h3>🔧 Debug Information</h3>";
echo "WordPress AJAX URL: " . admin_url('admin-ajax.php') . "<br>";
echo "Plugin URL: " . (defined('BLOG_WRITER_PLUGIN_URL') ? BLOG_WRITER_PLUGIN_URL : 'NOT DEFINED') . "<br>";
echo "Text Domain: " . (defined('BLOG_WRITER_TEXT_DOMAIN') ? BLOG_WRITER_TEXT_DOMAIN : 'NOT DEFINED') . "<br>";
echo "Current User ID: " . get_current_user_id() . "<br>";
echo "Is Admin: " . (is_admin() ? 'Yes' : 'No') . "<br>";

?>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
console.log('🧪 Test page loaded');
console.log('Available globals:', Object.keys(window).filter(k => k.toLowerCase().includes('vision')));

// Test jQuery AJAX to the actual endpoint
function testRealAjax() {
    console.log('🔥 Testing real AJAX call...');
    
    $.ajax({
        url: '<?php echo admin_url('admin-ajax.php'); ?>',
        type: 'POST',
        data: {
            action: 'blog-writer_ajax',
            endpoint: 'test_api_provider',
            provider: 'openrouter',
            api_key: 'test-key',
            nonce: '<?php echo wp_create_nonce('blog-writer_nonce'); ?>'
        },
        dataType: 'json'
    })
    .done(function(response) {
        console.log('✅ AJAX Success:', response);
        $('#ajax-result').html('<div style="color: green;">SUCCESS: ' + JSON.stringify(response, null, 2) + '</div>');
    })
    .fail(function(xhr, status, error) {
        console.error('❌ AJAX Failed:', {xhr, status, error});
        $('#ajax-result').html('<div style="color: red;">ERROR: ' + status + ' - ' + error + '<br>Response: ' + xhr.responseText + '</div>');
    });
}
</script>

<h3>Test 6: Live AJAX Test</h3>
<button onclick="testRealAjax()">🧪 Test Real AJAX Call</button>
<div id="ajax-result" style="margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 5px;"></div>

<p><strong>Instructions:</strong> Check the browser console for detailed logs and click the AJAX test button.</p>