# Blog Writer WordPress Plugin Design Document

## Overview

**Blog Writer** is a comprehensive WordPress plugin developed by LVL3 Marketing that enables administrators to generate AI-powered blog posts with intelligent internal linking and SEO optimization. The plugin leverages multiple AI models to create high-quality content while automatically linking to relevant existing pages based on extracted SEO keywords.

### Key Features
- AI-powered blog post generation using multiple models (<PERSON><PERSON>, <PERSON>, Gemini)
- Automatic SEO keyword extraction from existing WordPress pages
- Intelligent internal linking based on keyword analysis
- AI-generated image integration
- Customizable prompt management system
- Dedicated user management for content ownership
- Professional admin interface using Vision Framework

### Technical Specifications
- **Framework**: Built on Vision Framework for standardized UI/UX
- **WordPress Version**: 5.0+ (Tested up to 6.3)
- **PHP Version**: 7.4+
- **Author**: LVL3 Marketing (https://lvl3marketing.com)
- **License**: GPL v2 or later

## Architecture

### Plugin Structure
```
blogwriter/
├── blogwriter.php (Main plugin file)
├── framework/ (Vision Framework core)
├── src/
│   ├── BlogWriter.php (Main plugin class)
│   ├── Installer.php (Installation/upgrade handler)
│   ├── KeywordExtractor.php (SEO keyword analysis)
│   ├── ArticleGenerator.php (AI content generation)
│   ├── InternalLinker.php (Intelligent linking system)
│   ├── ImageGenerator.php (AI image generation)
│   └── UserManager.php (Dedicated user management)
├── assets/
│   ├── css/admin.css (Admin styling)
│   └── js/admin.js (Admin functionality)
└── templates/
    └── admin/ (Admin interface templates)
```

### Core Components

#### 1. Main Plugin Class (BlogWriter.php)
Extends Vision Framework Plugin base class and manages overall plugin functionality.

#### 2. Keyword Extractor (KeywordExtractor.php)
- Scans published WordPress pages
- Extracts SEO-relevant keywords using AI analysis
- Stores keyword mappings in database
- Provides mass update and individual page scanning

#### 3. Article Generator (ArticleGenerator.php)
- Manages AI model integrations (OpenRouter API)
- Processes custom prompts and parameters
- Generates article content with specified constraints
- Handles multiple AI model competition for topic suggestions

#### 4. Internal Linker (InternalLinker.php)
- Analyzes generated content for linking opportunities
- Matches content context with page keywords
- Inserts intelligent internal links
- Provides link summary and management interface

#### 5. Image Generator (ImageGenerator.php)
- Integrates with AI image generation services
- Manages custom image prompts
- Provides stock/AI image selection options
- Handles image insertion into articles

#### 6. User Manager (UserManager.php)
- Creates dedicated users for blog content ownership
- Manages user assignment and permissions
- Tracks article metadata and ownership
- Provides user workflow guidance

## Component Architecture

### Tab System (Admin Interface)

#### Dashboard Tab
- Welcome interface with setup workflow
- Plugin status overview
- User creation/assignment guidance
- Quick action buttons
- Recent activity summary

#### Page Keywords Tab
- Table of all WordPress pages (published/draft status)
- SEO keyword extraction interface
- Mass update and individual scan options
- Keyword management and editing
- Last scan date tracking
- API configuration for keyword extraction

#### Article Builder Tab
- Multi-model AI topic suggestions interface
- Custom prompt management system
- Article parameter configuration (word count, SEO settings)
- Internal linking preview and management
- Image generation and selection
- Article preview and editing interface

#### Generated Articles Tab
- List of all plugin-generated articles
- Article metadata and creation details
- Edit/republish functionality
- Internal link management
- Ownership tracking

#### Settings Tab
- API key management (OpenRouter, image services)
- AI model configuration and selection
- Default prompt templates
- Plugin preferences
- User assignment settings

#### Help Tab
- Setup documentation
- Feature explanations
- API configuration guides
- Troubleshooting information

### Database Schema

#### Table: blogwriter_pages
```sql
CREATE TABLE blogwriter_pages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_id INT NOT NULL,
    page_title VARCHAR(255) NOT NULL,
    page_url VARCHAR(500) NOT NULL,
    page_status VARCHAR(20) NOT NULL,
    keywords LONGTEXT,
    last_scan_date DATETIME,
    scan_model VARCHAR(50),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_page_id (page_id),
    INDEX idx_status (page_status)
);
```

#### Table: blogwriter_articles
```sql
CREATE TABLE blogwriter_articles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    post_id INT,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    prompt_data LONGTEXT,
    generation_models JSON,
    internal_links JSON,
    image_data JSON,
    word_count INT,
    generation_date DATETIME,
    status VARCHAR(20) DEFAULT 'draft',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_post_id (post_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);
```

#### Table: blogwriter_settings
```sql
CREATE TABLE blogwriter_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value LONGTEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### AI Integration Architecture

#### API Handler Structure
```mermaid
graph TD
    A[Article Builder Interface] --> B[AI Model Router]
    B --> C[OpenRouter API]
    B --> D[Direct Model APIs]
    C --> E[GPT Models]
    C --> F[Claude Models]
    C --> G[Gemini Models]
    D --> H[Image Generation APIs]
    
    I[Keyword Extractor] --> J[Content Analysis API]
    J --> K[SEO Keyword Extraction]
    
    L[Internal Linker] --> M[Context Matching Engine]
    M --> N[Page Database]
    M --> O[Link Insertion Logic]
```

#### AI Model Competition Flow
1. User selects topic parameters
2. System sends requests to multiple AI models simultaneously
3. Each model returns 5 article suggestions
4. Interface displays competing suggestions side-by-side
5. User selects preferred suggestion for full generation
6. Selected model generates complete article with internal linking

### User Management System

#### Workflow Components
- **Initial Setup**: Guided user creation on dashboard
- **User Suggestions**: "webmaster", company name, or custom
- **Assignment Interface**: Simple dropdown selection
- **Ownership Tracking**: All generated articles linked to assigned user
- **Migration Tools**: Ability to change article ownership later

#### User Creation Flow
```mermaid
graph LR
    A[Dashboard Access] --> B{User Assigned?}
    B -->|No| C[Setup Workflow]
    B -->|Yes| D[Normal Operations]
    C --> E[User Creation Form]
    E --> F[User Assignment]
    F --> D
```

## Article Generation Process

### Multi-Stage Generation Pipeline

#### Stage 1: Topic Generation
- Multiple AI models suggest 5 topics each
- Side-by-side comparison interface
- User selection of preferred topic
- Topic refinement options

#### Stage 2: Content Generation
- Selected AI model generates full article
- Custom prompt integration
- Parameter adherence (word count, SEO requirements)
- Structured content with headings

#### Stage 3: Internal Linking
- Content analysis for linking opportunities
- Keyword matching against page database
- Intelligent link insertion
- Link relevance scoring

#### Stage 4: Image Integration
- AI-generated image creation
- Stock image integration options
- Custom prompt-based generation
- Image placement optimization

#### Stage 5: Final Assembly
- Article compilation with all components
- Internal link summary generation
- Draft post creation in WordPress
- Metadata storage for future editing

### Prompt Management System

#### Prompt Categories
- **Main Content Prompt**: Primary article generation instructions
- **SEO Optimization**: Search engine optimization guidelines
- **Linking Instructions**: Internal linking behavior specifications
- **Style Guidelines**: Brand voice and writing style requirements
- **Image Prompts**: AI image generation instructions

#### Prompt Customization Interface
- Modular prompt builder with cards/sections
- Template system for reusable prompts
- Variable substitution for dynamic content
- Preview system for prompt testing

## Internal Linking Intelligence

### Keyword-Based Matching Algorithm

#### Matching Process
1. **Content Analysis**: Parse generated article for key concepts
2. **Keyword Comparison**: Match concepts against page keyword database
3. **Relevance Scoring**: Calculate link relevance based on context
4. **Position Optimization**: Determine optimal link placement
5. **Link Insertion**: Insert links with appropriate anchor text

#### Link Quality Metrics
- Contextual relevance score
- Keyword match strength
- Content flow preservation
- SEO value assessment

### Link Management Interface

#### Visual Link Summary
- Table of all inserted links
- Link destination and anchor text
- Relevance scores
- Edit/remove capabilities
- Link preview functionality

#### Link Optimization Tools
- Automatic link density management
- Anchor text variation suggestions
- Link placement recommendations
- SEO impact analysis

## Image Generation System

### Multi-Provider Integration

#### Supported Services
- AI-generated images (DALL-E, Midjourney, Stable Diffusion)
- Stock image services integration
- Custom image upload options
- Image editing capabilities

#### Image Workflow
1. **Prompt Generation**: AI creates image prompts based on article content
2. **Provider Selection**: User chooses generation service
3. **Image Generation**: Multiple options generated
4. **Selection Interface**: Visual selection with preview
5. **Integration**: Automatic insertion into article with proper sizing

### Image Management Features
- Batch generation for multiple images
- Style consistency across images
- SEO-optimized alt text generation
- Responsive image sizing
- WordPress media library integration

## Settings Management

### API Configuration
- OpenRouter API key management
- Individual model API keys
- Rate limiting configuration
- Cost tracking and budgets
- Service availability monitoring

### Model Selection Interface
- Available model listing
- Performance characteristics
- Cost per request information
- Model capability comparison
- Default model assignment

### Plugin Preferences
- Default article parameters
- Automatic linking settings
- Image generation defaults
- User interface customizations
- Backup and export options

## Data Flow Architecture

### Article Creation Workflow
```mermaid
sequenceDiagram
    participant U as User
    participant AB as Article Builder
    participant AI as AI Models
    participant KE as Keyword Engine
    participant IL as Internal Linker
    participant IG as Image Generator
    participant WP as WordPress

    U->>AB: Configure article parameters
    AB->>AI: Request topic suggestions
    AI-->>AB: Return competing topics
    AB-->>U: Display topic options
    U->>AB: Select preferred topic
    AB->>AI: Generate full article
    AI-->>AB: Return article content
    AB->>KE: Get page keywords
    KE-->>AB: Return keyword mappings
    AB->>IL: Process internal links
    IL-->>AB: Return linked content
    AB->>IG: Generate images
    IG-->>AB: Return image content
    AB->>WP: Create draft post
    WP-->>AB: Return post ID
    AB-->>U: Display final article
```

### Keyword Extraction Workflow
```mermaid
sequenceDiagram
    participant U as User
    participant PKT as Page Keywords Tab
    participant KE as Keyword Extractor
    participant AI as AI Service
    participant DB as Database

    U->>PKT: Initiate page scan
    PKT->>KE: Start extraction process
    KE->>AI: Send page content for analysis
    AI-->>KE: Return extracted keywords
    KE->>DB: Store keyword mappings
    DB-->>KE: Confirm storage
    KE-->>PKT: Update interface
    PKT-->>U: Display results
```

## Testing Strategy

### Component Testing
- **Keyword Extractor**: Test AI integration and keyword quality
- **Article Generator**: Validate content generation and prompt adherence
- **Internal Linker**: Verify link accuracy and relevance
- **Image Generator**: Test image generation and integration
- **User Manager**: Validate user creation and assignment

### Integration Testing
- **Full Article Pipeline**: End-to-end article creation testing
- **Multi-Model Competition**: Test simultaneous AI model requests
- **Database Operations**: Verify data integrity and performance
- **WordPress Integration**: Test post creation and metadata storage

### User Interface Testing
- **Tab Navigation**: Test seamless tab switching
- **Form Validation**: Validate input sanitization and error handling
- **AJAX Operations**: Test asynchronous operations and loading states
- **Responsive Design**: Verify mobile and tablet compatibility