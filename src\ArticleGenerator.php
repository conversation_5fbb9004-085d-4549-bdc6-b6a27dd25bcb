<?php
/**
 * Article Generator
 * 
 * Handles AI-powered article generation with multiple model support
 */

namespace BlogWriter;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ArticleGenerator Class
 */
class ArticleGenerator
{
    /**
     * Database handler instance
     * 
     * @var BlogWriterDatabaseHandler
     */
    protected $databaseHandler;
    
    /**
     * Plugin configuration
     * 
     * @var array
     */
    protected $config;
    
    /**
     * Available AI models for generation
     * 
     * @var array
     */
    protected $availableModels = [
        'gpt-4' => [
            'name' => 'GPT-4',
            'provider' => 'openai',
            'cost_per_1k' => 0.03,
            'max_tokens' => 8192,
            'strengths' => ['accuracy', 'creativity', 'reasoning']
        ],
        'gpt-3.5-turbo' => [
            'name' => 'GPT-3.5 Turbo',
            'provider' => 'openai',
            'cost_per_1k' => 0.002,
            'max_tokens' => 4096,
            'strengths' => ['speed', 'cost-effective', 'reliable']
        ],
        'claude-3-opus' => [
            'name' => 'Claude 3 Opus',
            'provider' => 'anthropic',
            'cost_per_1k' => 0.015,
            'max_tokens' => 4096,
            'strengths' => ['analysis', 'safety', 'nuanced writing']
        ],
        'claude-3-sonnet' => [
            'name' => 'Claude 3 Sonnet',
            'provider' => 'anthropic',
            'cost_per_1k' => 0.003,
            'max_tokens' => 4096,
            'strengths' => ['balanced', 'efficient', 'versatile']
        ],
        'gemini-pro' => [
            'name' => 'Gemini Pro',
            'provider' => 'google',
            'cost_per_1k' => 0.0005,
            'max_tokens' => 2048,
            'strengths' => ['multimodal', 'factual', 'cost-effective']
        ]
    ];
    
    /**
     * Constructor
     * 
     * @param BlogWriterDatabaseHandler $databaseHandler
     * @param array $config
     */
    public function __construct($databaseHandler, $config)
    {
        $this->databaseHandler = $databaseHandler;
        $this->config = $config;
    }
    
    /**
     * Generate topic suggestions from multiple AI models
     * 
     * @param string $topic_theme General topic theme
     * @param array $options Generation options
     * @return array|WP_Error
     */
    public function generateTopicSuggestions($topic_theme, $options = [])
    {
        $api_key = $this->databaseHandler->getBlogSetting('openrouter_api_key', '');
        
        if (empty($api_key)) {
            return new \WP_Error('no_api_key', __('OpenRouter API key not configured.', $this->config['text_domain']));
        }
        
        $models_to_use = $options['models'] ?? ['gpt-4', 'claude-3-sonnet', 'gemini-pro'];
        $suggestions_per_model = $options['suggestions_per_model'] ?? 5;
        $industry = $options['industry'] ?? '';
        $tone = $options['tone'] ?? 'professional';
        
        $all_suggestions = [];
        $start_time = microtime(true);
        
        foreach ($models_to_use as $model) {
            if (!isset($this->availableModels[$model])) {
                continue;
            }
            
            try {
                $suggestions = $this->generateTopicsForModel($model, $topic_theme, $suggestions_per_model, [
                    'industry' => $industry,
                    'tone' => $tone,
                    'api_key' => $api_key
                ]);
                
                if (!is_wp_error($suggestions)) {
                    $all_suggestions[$model] = [
                        'model_name' => $this->availableModels[$model]['name'],
                        'suggestions' => $suggestions,
                        'provider' => $this->availableModels[$model]['provider'],
                        'strengths' => $this->availableModels[$model]['strengths']
                    ];
                }
                
                // Add delay between requests to avoid rate limiting
                sleep(1);
                
            } catch (Exception $e) {
                $this->databaseHandler->log('warning', 'Topic generation failed for model', [
                    'model' => $model,
                    'error' => $e->getMessage(),
                    'topic_theme' => $topic_theme
                ]);
            }
        }
        
        $generation_time = microtime(true) - $start_time;
        
        if (empty($all_suggestions)) {
            return new \WP_Error('no_suggestions', __('Failed to generate topic suggestions from any model.', $this->config['text_domain']));
        }
        
        // Log the topic generation
        $this->databaseHandler->log('info', 'Topic suggestions generated', [
            'topic_theme' => $topic_theme,
            'models_used' => array_keys($all_suggestions),
            'total_suggestions' => array_sum(array_map(function($s) { return count($s['suggestions']); }, $all_suggestions)),
            'generation_time' => round($generation_time, 2)
        ]);
        
        return [
            'suggestions' => $all_suggestions,
            'generation_time' => round($generation_time, 2),
            'models_used' => array_keys($all_suggestions),
            'topic_theme' => $topic_theme
        ];
    }
    
    /**
     * Generate full article from selected topic
     * 
     * @param string $topic Selected topic
     * @param string $model AI model to use
     * @param array $options Generation options
     * @return array|WP_Error
     */
    public function generateFullArticle($topic, $model, $options = [])
    {
        $api_key = $this->databaseHandler->getBlogSetting('openrouter_api_key', '');
        
        if (empty($api_key)) {
            return new \WP_Error('no_api_key', __('OpenRouter API key not configured.', $this->config['text_domain']));
        }
        
        if (!isset($this->availableModels[$model])) {
            return new \WP_Error('invalid_model', __('Invalid AI model specified.', $this->config['text_domain']));
        }
        
        $start_time = microtime(true);
        
        // Build comprehensive prompt
        $prompt_data = $this->buildArticlePrompt($topic, $options);
        
        try {
            // Generate the article
            $article_content = $this->performArticleGeneration($model, $prompt_data, $api_key);
            
            if (is_wp_error($article_content)) {
                return $article_content;
            }
            
            // Process and structure the content
            $structured_content = $this->structureArticleContent($article_content);

            // Detect image placeholders in the content
            $image_placeholders = $this->detectImagePlaceholders($structured_content['content']);

            $generation_time = microtime(true) - $start_time;

            // Calculate word count
            $word_count = str_word_count(strip_tags($structured_content['content']));

            $result = [
                'title' => $structured_content['title'],
                'content' => $structured_content['content'],
                'excerpt' => $structured_content['excerpt'],
                'headings' => $structured_content['headings'],
                'image_placeholders' => $image_placeholders,
                'word_count' => $word_count,
                'model_used' => $model,
                'generation_time' => round($generation_time, 2),
                'prompt_data' => $prompt_data,
                'seo_data' => $this->extractSEOData($structured_content),
                'meta_data' => [
                    'topic' => $topic,
                    'options' => $options,
                    'generated_at' => current_time('mysql')
                ]
            ];
            
            // Log the generation
            $this->databaseHandler->log('info', 'Full article generated', [
                'topic' => $topic,
                'model' => $model,
                'word_count' => $word_count,
                'generation_time' => round($generation_time, 2)
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Article generation failed', [
                'topic' => $topic,
                'model' => $model,
                'error' => $e->getMessage()
            ]);
            
            return new \WP_Error('generation_failed', 
                sprintf(__('Article generation failed: %s', $this->config['text_domain']), $e->getMessage())
            );
        }
    }
    
    /**
     * Generate topics for a specific model
     * 
     * @param string $model
     * @param string $topic_theme
     * @param int $count
     * @param array $options
     * @return array|WP_Error
     */
    protected function generateTopicsForModel($model, $topic_theme, $count, $options)
    {
        $prompt = $this->buildTopicPrompt($topic_theme, $count, $options);
        
        $api_url = 'https://openrouter.ai/api/v1/chat/completions';
        
        $headers = [
            'Authorization' => 'Bearer ' . $options['api_key'],
            'Content-Type' => 'application/json',
            'HTTP-Referer' => home_url(),
            'X-Title' => get_bloginfo('name') . ' - Blog Writer Plugin'
        ];
        
        $body = [
            'model' => $model,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are an expert content strategist who creates engaging blog post topics that drive traffic and engagement. Focus on topics that provide real value to readers.'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'temperature' => 0.8,
            'max_tokens' => 800
        ];
        
        $response = wp_remote_post($api_url, [
            'headers' => $headers,
            'body' => wp_json_encode($body),
            'timeout' => 30
        ]);
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($response_code !== 200) {
            $error_data = json_decode($response_body, true);
            $error_message = $error_data['error']['message'] ?? 'Unknown API error';
            
            return new \WP_Error('api_error', 
                sprintf(__('API request failed (%d): %s', $this->config['text_domain']), $response_code, $error_message)
            );
        }
        
        $data = json_decode($response_body, true);
        
        if (!isset($data['choices'][0]['message']['content'])) {
            return new \WP_Error('invalid_response', __('Invalid API response format.', $this->config['text_domain']));
        }
        
        // Parse topics from response
        $topics = $this->parseTopicsFromResponse($data['choices'][0]['message']['content']);
        
        return array_slice($topics, 0, $count);
    }
    
    /**
     * Build topic generation prompt
     * 
     * @param string $topic_theme
     * @param int $count
     * @param array $options
     * @return string
     */
    protected function buildTopicPrompt($topic_theme, $count, $options)
    {
        $prompt = "Generate {$count} engaging blog post topics related to: {$topic_theme}\n\n";
        
        if (!empty($options['industry'])) {
            $prompt .= "Industry context: {$options['industry']}\n";
        }
        
        $prompt .= "Tone: {$options['tone']}\n\n";
        
        $prompt .= "Requirements:\n";
        $prompt .= "1. Topics should be specific and actionable\n";
        $prompt .= "2. Appeal to the target audience's pain points and interests\n";
        $prompt .= "3. Be SEO-friendly with searchable terms\n";
        $prompt .= "4. Provide clear value proposition\n";
        $prompt .= "5. Be suitable for 800-1500 word articles\n\n";
        
        $prompt .= "Format: Return only the topic titles, one per line, without numbering or extra text.";
        
        return $prompt;
    }
    
    /**
     * Build comprehensive article generation prompt
     * 
     * @param string $topic
     * @param array $options
     * @return array
     */
    protected function buildArticlePrompt($topic, $options)
    {
        $word_count = $options['word_count'] ?? $this->databaseHandler->getBlogSetting('default_word_count', 1000);
        $tone = $options['tone'] ?? 'professional';
        $audience = $options['audience'] ?? 'general';
        $include_faq = $options['include_faq'] ?? true;
        $seo_focus = $options['seo_focus'] ?? true;
        
        // Get custom prompts from database
        $main_prompt = $this->databaseHandler->getBlogSetting('main_content_prompt', 
            'Write a comprehensive, SEO-optimized blog post about [TOPIC]. Include relevant headings, engaging content, and ensure it provides value to readers. Target word count: [WORD_COUNT] words.'
        );
        
        $seo_prompt = $this->databaseHandler->getBlogSetting('seo_optimization_prompt',
            'Focus on relevant keywords, proper heading structure, and content that provides genuine value to readers searching for information about [TOPIC].'
        );
        
        // Replace placeholders
        $main_prompt = str_replace(['[TOPIC]', '[WORD_COUNT]'], [$topic, $word_count], $main_prompt);
        $seo_prompt = str_replace('[TOPIC]', $topic, $seo_prompt);
        
        $prompt = $main_prompt . "\n\n";
        
        if ($seo_focus) {
            $prompt .= "SEO Guidelines:\n" . $seo_prompt . "\n\n";
        }
        
        $prompt .= "Article Specifications:\n";
        $prompt .= "- Topic: {$topic}\n";
        $prompt .= "- Target word count: {$word_count} words\n";
        $prompt .= "- Tone: {$tone}\n";
        $prompt .= "- Target audience: {$audience}\n\n";
        
        $prompt .= "Structure Requirements:\n";
        $prompt .= "1. Compelling title (H1)\n";
        $prompt .= "2. Engaging introduction (2-3 paragraphs)\n";
        $prompt .= "3. Main content with 3-5 H2 sections\n";
        $prompt .= "4. Use H3 subheadings within sections as needed\n";
        
        if ($include_faq) {
            $prompt .= "5. FAQ section with 3-5 relevant questions\n";
        }
        
        $prompt .= "6. Strong conclusion with call-to-action\n\n";
        
        $prompt .= "Content Guidelines:\n";
        $prompt .= "- Use short paragraphs (2-3 sentences max)\n";
        $prompt .= "- Include specific examples and actionable advice\n";
        $prompt .= "- Write in an engaging, conversational style\n";
        $prompt .= "- Use bullet points and numbered lists where appropriate\n";
        $prompt .= "- Ensure content is original and valuable\n\n";
        
        $prompt .= "Return the article in HTML format with proper heading tags (h1, h2, h3) and paragraph tags.";
        
        return [
            'full_prompt' => $prompt,
            'topic' => $topic,
            'word_count' => $word_count,
            'tone' => $tone,
            'audience' => $audience,
            'include_faq' => $include_faq,
            'seo_focus' => $seo_focus,
            'custom_prompts' => [
                'main' => $main_prompt,
                'seo' => $seo_prompt
            ]
        ];
    }
    
    /**
     * Perform article generation API call
     * 
     * @param string $model
     * @param array $prompt_data
     * @param string $api_key
     * @return string|WP_Error
     */
    protected function performArticleGeneration($model, $prompt_data, $api_key)
    {
        $api_url = 'https://openrouter.ai/api/v1/chat/completions';
        
        $headers = [
            'Authorization' => 'Bearer ' . $api_key,
            'Content-Type' => 'application/json',
            'HTTP-Referer' => home_url(),
            'X-Title' => get_bloginfo('name') . ' - Blog Writer Plugin'
        ];
        
        $max_tokens = min($this->availableModels[$model]['max_tokens'] - 100, $prompt_data['word_count'] * 2);
        
        $body = [
            'model' => $model,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are an expert content writer specializing in creating high-quality, SEO-optimized blog posts that engage readers and provide genuine value. Write in a clear, accessible style.'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt_data['full_prompt']
                ]
            ],
            'temperature' => 0.7,
            'max_tokens' => $max_tokens
        ];
        
        $response = wp_remote_post($api_url, [
            'headers' => $headers,
            'body' => wp_json_encode($body),
            'timeout' => 60
        ]);
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($response_code !== 200) {
            $error_data = json_decode($response_body, true);
            $error_message = $error_data['error']['message'] ?? 'Unknown API error';
            
            return new \WP_Error('api_error', 
                sprintf(__('API request failed (%d): %s', $this->config['text_domain']), $response_code, $error_message)
            );
        }
        
        $data = json_decode($response_body, true);
        
        if (!isset($data['choices'][0]['message']['content'])) {
            return new \WP_Error('invalid_response', __('Invalid API response format.', $this->config['text_domain']));
        }
        
        return $data['choices'][0]['message']['content'];
    }
    
    /**
     * Structure article content
     * 
     * @param string $content
     * @return array
     */
    protected function structureArticleContent($content)
    {
        // Clean up the content
        $content = trim($content);
        
        // Extract title (first H1)
        $title = '';
        if (preg_match('/<h1[^>]*>(.*?)<\/h1>/i', $content, $matches)) {
            $title = strip_tags($matches[1]);
            $content = preg_replace('/<h1[^>]*>.*?<\/h1>/i', '', $content, 1);
        }
        
        // If no H1 found, try to extract from first line or paragraph
        if (empty($title)) {
            $lines = explode("\n", strip_tags($content));
            $title = trim($lines[0]);
            if (strlen($title) > 100) {
                $title = substr($title, 0, 97) . '...';
            }
        }
        
        // Extract headings for structure analysis
        $headings = [];
        if (preg_match_all('/<h([2-6])[^>]*>(.*?)<\/h[2-6]>/i', $content, $matches)) {
            foreach ($matches[2] as $i => $heading) {
                $headings[] = [
                    'level' => (int)$matches[1][$i],
                    'text' => strip_tags($heading),
                    'id' => sanitize_title($heading)
                ];
            }
        }
        
        // Generate excerpt from first paragraph
        $excerpt = '';
        if (preg_match('/<p[^>]*>(.*?)<\/p>/i', $content, $matches)) {
            $excerpt = wp_trim_words(strip_tags($matches[1]), 25);
        }
        
        // Clean and format content
        $content = $this->cleanArticleContent($content);
        
        return [
            'title' => $title,
            'content' => $content,
            'excerpt' => $excerpt,
            'headings' => $headings
        ];
    }
    
    /**
     * Clean and format article content
     *
     * @param string $content
     * @return string
     */
    protected function cleanArticleContent($content)
    {
        // Remove any markdown artifacts
        $content = preg_replace('/```[a-zA-Z]*\n/', '', $content);
        $content = str_replace('```', '', $content);

        // Ensure proper paragraph spacing
        $content = preg_replace('/\n\s*\n/', '</p><p>', $content);

        // Wrap content in paragraphs if not already wrapped
        if (strpos($content, '<p>') === false) {
            $content = '<p>' . $content . '</p>';
        }

        // Clean up double paragraphs
        $content = preg_replace('/<\/p>\s*<p>/', '</p><p>', $content);

        // Add proper spacing around headings
        $content = preg_replace('/(<\/p>)(\s*)(<h[2-6])/i', '$1$2$3', $content);
        $content = preg_replace('/(<\/h[2-6]>)(\s*)(<p>)/i', '$1$2$3', $content);

        return trim($content);
    }

    /**
     * Detect image placeholders in generated content
     *
     * @param string $content Article content
     * @return array Array of detected placeholders
     */
    public function detectImagePlaceholders($content)
    {
        $placeholders = [];

        // Pattern 1: [IMAGE: description] or [Image: description]
        if (preg_match_all('/\[(?:IMAGE|Image|image):\s*([^\]]+)\]/i', $content, $matches)) {
            foreach ($matches[1] as $index => $description) {
                $placeholders[] = [
                    'type' => 'bracket',
                    'full_match' => $matches[0][$index],
                    'description' => trim($description),
                    'suggested_prompt' => $this->generateImagePrompt($description)
                ];
            }
        }

        // Pattern 2: {img: description} or {image: description}
        if (preg_match_all('/\{(?:img|image):\s*([^}]+)\}/i', $content, $matches)) {
            foreach ($matches[1] as $index => $description) {
                $placeholders[] = [
                    'type' => 'brace',
                    'full_match' => $matches[0][$index],
                    'description' => trim($description),
                    'suggested_prompt' => $this->generateImagePrompt($description)
                ];
            }
        }

        // Pattern 3: <!-- IMAGE: description --> or similar HTML comments
        if (preg_match_all('/<!--\s*(?:IMAGE|Image|image):\s*([^-]+)-->/i', $content, $matches)) {
            foreach ($matches[1] as $index => $description) {
                $placeholders[] = [
                    'type' => 'comment',
                    'full_match' => $matches[0][$index],
                    'description' => trim($description),
                    'suggested_prompt' => $this->generateImagePrompt($description)
                ];
            }
        }

        // Pattern 4: **[Image needed: description]** or similar markdown-style
        if (preg_match_all('/\*\*\[(?:Image needed|IMAGE NEEDED|image needed):\s*([^\]]+)\]\*\*/i', $content, $matches)) {
            foreach ($matches[1] as $index => $description) {
                $placeholders[] = [
                    'type' => 'markdown',
                    'full_match' => $matches[0][$index],
                    'description' => trim($description),
                    'suggested_prompt' => $this->generateImagePrompt($description)
                ];
            }
        }

        // Pattern 5: Simple text patterns like "Insert image of..." or "Add photo showing..."
        if (preg_match_all('/(?:Insert image of|Add photo showing|Include picture of|Show image of)\s+([^.!?]+)/i', $content, $matches)) {
            foreach ($matches[1] as $index => $description) {
                $placeholders[] = [
                    'type' => 'text',
                    'full_match' => $matches[0][$index],
                    'description' => trim($description),
                    'suggested_prompt' => $this->generateImagePrompt($description)
                ];
            }
        }

        return $placeholders;
    }

    /**
     * Generate image prompt from placeholder description
     *
     * @param string $description Placeholder description
     * @return string Generated prompt for image generation
     */
    protected function generateImagePrompt($description)
    {
        // Clean up the description
        $description = trim($description);
        $description = rtrim($description, '.,!?');

        // Build a professional image prompt
        $prompt = "Create a professional, high-quality image showing " . $description;
        $prompt .= ". The image should be suitable for a blog post, with good lighting and composition.";
        $prompt .= " Style should be modern and engaging. No text or watermarks in the image.";

        return $prompt;
    }
    
    /**
     * Extract SEO data from structured content
     * 
     * @param array $structured_content
     * @return array
     */
    protected function extractSEOData($structured_content)
    {
        $content_text = strip_tags($structured_content['content']);
        
        return [
            'word_count' => str_word_count($content_text),
            'character_count' => strlen($content_text),
            'paragraph_count' => substr_count($structured_content['content'], '<p>'),
            'heading_count' => count($structured_content['headings']),
            'readability_score' => $this->calculateReadabilityScore($content_text),
            'keyword_density' => $this->analyzeKeywordDensity($content_text, $structured_content['title'])
        ];
    }
    
    /**
     * Calculate basic readability score
     * 
     * @param string $text
     * @return float
     */
    protected function calculateReadabilityScore($text)
    {
        $sentences = preg_split('/[.!?]+/', $text);
        $words = str_word_count($text);
        $syllables = $this->countSyllables($text);
        
        if (count($sentences) == 0 || $words == 0) {
            return 0;
        }
        
        // Flesch Reading Ease Score
        $score = 206.835 - (1.015 * ($words / count($sentences))) - (84.6 * ($syllables / $words));
        
        return round($score, 1);
    }
    
    /**
     * Count syllables in text (simplified)
     * 
     * @param string $text
     * @return int
     */
    protected function countSyllables($text)
    {
        $text = strtolower($text);
        $text = preg_replace('/[^a-z ]/', '', $text);
        $words = explode(' ', $text);
        $syllables = 0;
        
        foreach ($words as $word) {
            $syllables += max(1, preg_match_all('/[aeiouy]+/', $word));
        }
        
        return $syllables;
    }
    
    /**
     * Analyze keyword density
     * 
     * @param string $text
     * @param string $title
     * @return array
     */
    protected function analyzeKeywordDensity($text, $title)
    {
        $words = str_word_count(strtolower($text), 1);
        $total_words = count($words);
        $word_count = array_count_values($words);
        
        // Remove common stop words
        $stop_words = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'];
        
        foreach ($stop_words as $stop_word) {
            unset($word_count[$stop_word]);
        }
        
        // Filter to words with at least 3 characters and more than 1 occurrence
        $filtered_words = array_filter($word_count, function($count, $word) {
            return strlen($word) >= 3 && $count > 1;
        }, ARRAY_FILTER_USE_BOTH);
        
        arsort($filtered_words);
        
        $top_keywords = array_slice($filtered_words, 0, 10, true);
        $density_data = [];
        
        foreach ($top_keywords as $word => $count) {
            $density_data[] = [
                'word' => $word,
                'count' => $count,
                'density' => round(($count / $total_words) * 100, 2)
            ];
        }
        
        return $density_data;
    }
    
    /**
     * Parse topics from AI response
     * 
     * @param string $response
     * @return array
     */
    protected function parseTopicsFromResponse($response)
    {
        $lines = explode("\n", trim($response));
        $topics = [];
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            if (empty($line)) {
                continue;
            }
            
            // Remove numbering and bullet points
            $line = preg_replace('/^[\d\.\-\*\+\>\s]+/', '', $line);
            $line = trim($line);
            
            // Remove quotes
            $line = trim($line, '"\'');
            
            if (!empty($line) && strlen($line) <= 200) {
                $topics[] = $line;
            }
        }
        
        return array_unique($topics);
    }
    
    /**
     * Get available models for selection
     * 
     * @return array
     */
    public function getAvailableModels()
    {
        return $this->availableModels;
    }
    
    /**
     * Estimate generation cost
     * 
     * @param string $model
     * @param int $word_count
     * @return float
     */
    public function estimateGenerationCost($model, $word_count)
    {
        if (!isset($this->availableModels[$model])) {
            return 0;
        }
        
        $tokens_estimated = $word_count * 1.3; // Rough estimate
        $cost_per_1k = $this->availableModels[$model]['cost_per_1k'];
        
        return round(($tokens_estimated / 1000) * $cost_per_1k, 4);
    }
    
    /**
     * Get generation statistics
     * 
     * @return array
     */
    public function getGenerationStats()
    {
        $articles = $this->databaseHandler->getArticles(['limit' => 0]);
        
        $stats = [
            'total_articles' => count($articles),
            'total_words' => 0,
            'models_used' => [],
            'average_generation_time' => 0,
            'last_generation' => null
        ];
        
        $total_time = 0;
        
        foreach ($articles as $article) {
            $stats['total_words'] += $article['word_count'];
            
            if (!empty($article['ai_model_used'])) {
                $stats['models_used'][] = $article['ai_model_used'];
            }
            
            if (!empty($article['generation_time_seconds'])) {
                $total_time += $article['generation_time_seconds'];
            }
            
            if (empty($stats['last_generation']) || $article['generation_date'] > $stats['last_generation']) {
                $stats['last_generation'] = $article['generation_date'];
            }
        }
        
        $stats['models_used'] = array_unique($stats['models_used']);
        $stats['average_generation_time'] = count($articles) > 0 ? round($total_time / count($articles), 1) : 0;
        
        return $stats;
    }
}