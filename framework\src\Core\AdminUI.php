<?php
/**
 * Admin UI Framework
 * 
 * Handles the unified admin interface for all plugins
 */

namespace VisionFramework\Core;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AdminUI Class
 */
class AdminUI
{
    /**
     * Plugin configuration
     * 
     * @var array
     */
    protected $config;
    
    /**
     * Current tab
     * 
     * @var string
     */
    protected $currentTab = 'dashboard';
    
    /**
     * Plugin page hook
     * 
     * @var string
     */
    protected $pageHook;
    
    /**
     * Available tabs
     * 
     * @var array
     */
    protected $tabs = [];
    
    /**
     * Constructor
     * 
     * @param array $config Plugin configuration
     */
    public function __construct($config)
    {
        $this->config = $config;
        $this->initTabs();
    }
    
    /**
     * Initialize the admin UI
     */
    public function init()
    {
        // Set current tab from URL parameter
        if (isset($_GET['tab'])) {
            $tab = sanitize_key($_GET['tab']);
            if (array_key_exists($tab, $this->tabs)) {
                $this->currentTab = $tab;
            }
        }
    }
    
    /**
     * Clear all tabs (used by plugins to override defaults)
     */
    public function clearTabs()
    {
        $this->tabs = [];
    }
    
    /**
     * Initialize default tabs
     */
    protected function initTabs()
    {
        $this->tabs = [
            'dashboard' => [
                'title' => __('Dashboard', $this->config['text_domain']),
                'icon' => 'dashboard',
                'callback' => [$this, 'renderDashboard']
            ],
            'logs' => [
                'title' => __('Logs', $this->config['text_domain']),
                'icon' => 'list-view',
                'callback' => [$this, 'renderLogs']
            ],
            'scan' => [
                'title' => __('Scan', $this->config['text_domain']),
                'icon' => 'search',
                'callback' => [$this, 'renderScan']
            ],
            'settings' => [
                'title' => __('Settings', $this->config['text_domain']),
                'icon' => 'admin-generic',
                'callback' => [$this, 'renderSettings']
            ],
            'help' => [
                'title' => __('Help', $this->config['text_domain']),
                'icon' => 'editor-help',
                'callback' => [$this, 'renderHelp']
            ],
            'about' => [
                'title' => __('About', $this->config['text_domain']),
                'icon' => 'info',
                'callback' => [$this, 'renderAbout']
            ]
        ];
        
        // Allow plugins to add custom tabs
        $this->tabs = apply_filters($this->config['text_domain'] . '_admin_tabs', $this->tabs);
    }
    
    /**
     * Add admin menu
     */
    public function addMenu()
    {
        $this->pageHook = add_menu_page(
            $this->config['plugin_name'],
            $this->config['menu_title'] ?? $this->config['plugin_name'],
            'manage_options',
            $this->config['menu_slug'],
            [$this, 'renderMainPage'],
            $this->config['menu_icon'] ?? 'dashicons-admin-generic',
            $this->config['menu_position'] ?? null
        );
    }
    
    /**
     * Check if current page is a plugin page
     * 
     * @param string $hook Current page hook
     * @return bool
     */
    public function isPluginPage($hook)
    {
        return $hook === $this->pageHook;
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueueAssets()
    {
        // Enqueue framework CSS
        wp_enqueue_style(
            $this->config['text_domain'] . '-admin',
            $this->config['plugin_url'] . 'assets/css/admin.css',
            [],
            $this->config['version']
        );
        
        // Enqueue framework JS
        wp_enqueue_script(
            $this->config['text_domain'] . '-admin',
            $this->config['plugin_url'] . 'assets/js/admin.js',
            ['jquery'],
            $this->config['version'],
            true
        );
        
        // Localize script for AJAX
        wp_localize_script(
            $this->config['text_domain'] . '-admin',
            'visionFramework',
            [
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce($this->config['text_domain'] . '_nonce'),
                'action' => $this->config['text_domain'] . '_ajax',
                'textDomain' => $this->config['text_domain'],
                'strings' => [
                    'loading' => __('Loading...', $this->config['text_domain']),
                    'error' => __('An error occurred. Please try again.', $this->config['text_domain']),
                    'success' => __('Operation completed successfully.', $this->config['text_domain'])
                ]
            ]
        );
        
        // Simple CSS class-based theme detection
        wp_add_inline_script(
            $this->config['text_domain'] . '-admin',
            'document.documentElement.className += " vision-admin-ready";'
        );
    }
    
    /**
     * Render main plugin page
     */
    public function renderMainPage()
    {
        // Security check
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }
        
        echo $this->getPageHTML();
    }
    
    /**
     * Get the complete page HTML
     * 
     * @return string
     */
    protected function getPageHTML()
    {
        ob_start();
        ?>
        <div class="vision-framework-wrapper" data-plugin="<?php echo esc_attr($this->config['text_domain']); ?>">
            <?php echo $this->getHeaderHTML(); ?>
            <?php echo $this->getNavigationHTML(); ?>
            <?php echo $this->getContentHTML(); ?>
            <?php echo $this->getFooterHTML(); ?>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get header HTML
     * 
     * @return string
     */
    protected function getHeaderHTML()
    {
        ob_start();
        ?>
        <div class="vision-header">
            <div class="vision-header-content">
                <div class="vision-header-logo">
                    <?php if (!empty($this->config['logo_url'])): ?>
                        <img src="<?php echo esc_url($this->config['logo_url']); ?>" 
                             alt="<?php echo esc_attr($this->config['plugin_name']); ?>" 
                             class="vision-logo">
                    <?php endif; ?>
                    <div class="vision-brand-info">
                        <h1 class="vision-plugin-title"><?php echo esc_html($this->config['plugin_name']); ?></h1>
                        <?php if (!empty($this->config['partner_name'])): ?>
                            <span class="vision-partner">by <?php echo esc_html($this->config['partner_name']); ?></span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="vision-header-controls">
                    <button type="button" class="vision-theme-toggle" id="vision-theme-toggle" title="<?php esc_attr_e('Toggle Light/Dark Mode', $this->config['text_domain']); ?>">
                        <span class="theme-icon theme-icon-sun">☀️</span>
                        <span class="theme-icon theme-icon-moon">🌙</span>
                    </button>
                    <div class="vision-header-version">
                        <span class="vision-version-badge">
                            v<?php echo esc_html($this->config['version']); ?>
                            <?php if (strpos($this->config['version'], 'dev') !== false): ?>
                                <span class="vision-dev-badge">DEV</span>
                            <?php endif; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get navigation HTML
     * 
     * @return string
     */
    protected function getNavigationHTML()
    {
        ob_start();
        ?>
        <div class="vision-navigation">
            <div class="vision-nav-tabs">
                <?php foreach ($this->tabs as $tabKey => $tab): ?>
                    <a href="#" 
                       class="vision-nav-tab <?php echo $tabKey === $this->currentTab ? 'active' : ''; ?>"
                       data-tab="<?php echo esc_attr($tabKey); ?>">
                        <span class="dashicons dashicons-<?php echo esc_attr($tab['icon']); ?>"></span>
                        <?php echo esc_html($tab['title']); ?>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get content HTML
     * 
     * @return string
     */
    protected function getContentHTML()
    {
        ob_start();
        ?>
        <div class="vision-content">
            <div class="vision-content-inner">
                <?php foreach ($this->tabs as $tabKey => $tab): ?>
                    <div class="vision-tab-content <?php echo $tabKey === $this->currentTab ? 'active' : ''; ?>"
                         id="vision-tab-<?php echo esc_attr($tabKey); ?>">
                        <?php
                        if (is_callable($tab['callback'])) {
                            call_user_func($tab['callback']);
                        } else {
                            echo '<p>' . __('Content not available.', $this->config['text_domain']) . '</p>';
                        }
                        ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get footer HTML
     * 
     * @return string
     */
    protected function getFooterHTML()
    {
        return '<div class="vision-footer"></div>';
    }
    
    /**
     * Render dashboard tab
     */
    public function renderDashboard()
    {
        echo apply_filters($this->config['text_domain'] . '_dashboard_content', $this->getDefaultDashboard());
    }
    
    /**
     * Render logs tab
     */
    public function renderLogs()
    {
        echo apply_filters($this->config['text_domain'] . '_logs_content', $this->getDefaultLogs());
    }
    
    /**
     * Render scan tab
     */
    public function renderScan()
    {
        echo apply_filters($this->config['text_domain'] . '_scan_content', $this->getDefaultScan());
    }
    
    /**
     * Render settings tab
     */
    public function renderSettings()
    {
        echo apply_filters($this->config['text_domain'] . '_settings_content', $this->getDefaultSettings());
    }
    
    /**
     * Render help tab
     */
    public function renderHelp()
    {
        echo apply_filters($this->config['text_domain'] . '_help_content', $this->getDefaultHelp());
    }
    
    /**
     * Render about tab
     */
    public function renderAbout()
    {
        echo apply_filters($this->config['text_domain'] . '_about_content', $this->getDefaultAbout());
    }
    
    /**
     * Get default dashboard content
     * 
     * @return string
     */
    protected function getDefaultDashboard()
    {
        ob_start();
        ?>
        <div class="vision-card">
            <h2><?php _e('Welcome to', $this->config['text_domain']); ?> <?php echo esc_html($this->config['plugin_name']); ?></h2>
            <p><?php _e('This is the main dashboard where you can view an overview of your plugin\'s status and recent activity.', $this->config['text_domain']); ?></p>
            
            <div class="vision-dashboard-widgets">
                <div class="vision-widget">
                    <h3><?php _e('Status Overview', $this->config['text_domain']); ?></h3>
                    <div class="vision-status-grid">
                        <div class="vision-status-item">
                            <span class="vision-status-label"><?php _e('Plugin Status', $this->config['text_domain']); ?></span>
                            <span class="vision-status-value vision-status-active"><?php _e('Active', $this->config['text_domain']); ?></span>
                        </div>
                        <div class="vision-status-item">
                            <span class="vision-status-label"><?php _e('Last Activity', $this->config['text_domain']); ?></span>
                            <span class="vision-status-value"><?php echo esc_html(current_time('mysql')); ?></span>
                        </div>
                    </div>
                </div>
                
                <div class="vision-widget">
                    <h3><?php _e('Quick Actions', $this->config['text_domain']); ?></h3>
                    <div class="vision-quick-actions">
                        <button type="button" class="vision-button vision-button-primary" data-action="refresh">
                            <?php _e('Refresh Data', $this->config['text_domain']); ?>
                        </button>
                        <button type="button" class="vision-button vision-button-secondary" data-action="clear-cache">
                            <?php _e('Clear Cache', $this->config['text_domain']); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get default logs content
     * 
     * @return string
     */
    protected function getDefaultLogs()
    {
        ob_start();
        ?>
        <div class="vision-card">
            <h2><?php _e('Activity Logs', $this->config['text_domain']); ?></h2>
            <p><?php _e('View recent plugin activity and system logs.', $this->config['text_domain']); ?></p>
            
            <div class="vision-logs-container">
                <div class="vision-logs-filters">
                    <select class="vision-log-level-filter">
                        <option value=""><?php _e('All Levels', $this->config['text_domain']); ?></option>
                        <option value="info"><?php _e('Info', $this->config['text_domain']); ?></option>
                        <option value="warning"><?php _e('Warning', $this->config['text_domain']); ?></option>
                        <option value="error"><?php _e('Error', $this->config['text_domain']); ?></option>
                    </select>
                    <button type="button" class="vision-button vision-button-secondary" data-action="clear-logs">
                        <?php _e('Clear Logs', $this->config['text_domain']); ?>
                    </button>
                </div>
                
                <div class="vision-logs-list">
                    <div class="vision-log-entry vision-log-info">
                        <span class="vision-log-time"><?php echo esc_html(current_time('H:i:s')); ?></span>
                        <span class="vision-log-level">INFO</span>
                        <span class="vision-log-message"><?php _e('Plugin initialized successfully', $this->config['text_domain']); ?></span>
                    </div>
                    <div class="vision-log-entry vision-log-info">
                        <span class="vision-log-time"><?php echo esc_html(date('H:i:s', strtotime('-5 minutes'))); ?></span>
                        <span class="vision-log-level">INFO</span>
                        <span class="vision-log-message"><?php _e('Configuration loaded', $this->config['text_domain']); ?></span>
                    </div>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get default scan content
     * 
     * @return string
     */
    protected function getDefaultScan()
    {
        ob_start();
        ?>
        <div class="vision-card">
            <h2><?php _e('System Scan', $this->config['text_domain']); ?></h2>
            <p><?php _e('Perform system scans and health checks.', $this->config['text_domain']); ?></p>
            
            <div class="vision-scan-container">
                <div class="vision-scan-controls">
                    <button type="button" class="vision-button vision-button-primary vision-button-large" data-action="start-scan">
                        <span class="dashicons dashicons-search"></span>
                        <?php _e('Start Scan', $this->config['text_domain']); ?>
                    </button>
                </div>
                
                <div class="vision-scan-progress" style="display: none;">
                    <div class="vision-progress-bar">
                        <div class="vision-progress-fill" style="width: 0%;"></div>
                    </div>
                    <p class="vision-scan-status"><?php _e('Preparing scan...', $this->config['text_domain']); ?></p>
                </div>
                
                <div class="vision-scan-results">
                    <h3><?php _e('Last Scan Results', $this->config['text_domain']); ?></h3>
                    <div class="vision-scan-summary">
                        <div class="vision-scan-item vision-scan-success">
                            <span class="dashicons dashicons-yes-alt"></span>
                            <?php _e('No issues found', $this->config['text_domain']); ?>
                        </div>
                        <div class="vision-scan-item vision-scan-info">
                            <span class="dashicons dashicons-info"></span>
                            <?php _e('Last scan: Never', $this->config['text_domain']); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get default settings content
     * 
     * @return string
     */
    protected function getDefaultSettings()
    {
        ob_start();
        ?>
        <div class="vision-card">
            <h2><?php _e('Plugin Settings', $this->config['text_domain']); ?></h2>
            <p><?php _e('Configure your plugin settings and preferences.', $this->config['text_domain']); ?></p>
            
            <form method="post" class="vision-settings-form">
                <?php wp_nonce_field($this->config['text_domain'] . '_settings', $this->config['text_domain'] . '_settings_nonce'); ?>
                
                <div class="vision-settings-section">
                    <h3><?php _e('General Settings', $this->config['text_domain']); ?></h3>
                    
                    <table class="vision-form-table">
                        <tr>
                            <th scope="row">
                                <label for="enable_feature"><?php _e('Enable Feature', $this->config['text_domain']); ?></label>
                            </th>
                            <td>
                                <input type="checkbox" id="enable_feature" name="enable_feature" value="1" checked>
                                <p class="description"><?php _e('Enable this feature to activate advanced functionality.', $this->config['text_domain']); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="api_key"><?php _e('API Key', $this->config['text_domain']); ?></label>
                            </th>
                            <td>
                                <input type="text" id="api_key" name="api_key" class="regular-text" placeholder="<?php esc_attr_e('Enter your API key', $this->config['text_domain']); ?>">
                                <p class="description"><?php _e('Optional API key for external integrations.', $this->config['text_domain']); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>
                
                <div class="vision-settings-section">
                    <h3><?php _e('Display Options', $this->config['text_domain']); ?></h3>
                    
                    <table class="vision-form-table">
                        <tr>
                            <th scope="row">
                                <label for="theme_mode"><?php _e('Theme Mode', $this->config['text_domain']); ?></label>
                            </th>
                            <td>
                                <select id="theme_mode" name="theme_mode">
                                    <option value="auto"><?php _e('Auto (Follow System)', $this->config['text_domain']); ?></option>
                                    <option value="light"><?php _e('Light Mode', $this->config['text_domain']); ?></option>
                                    <option value="dark"><?php _e('Dark Mode', $this->config['text_domain']); ?></option>
                                </select>
                            </td>
                        </tr>
                    </table>
                </div>
                
                <p class="submit">
                    <button type="submit" class="vision-button vision-button-primary">
                        <?php _e('Save Settings', $this->config['text_domain']); ?>
                    </button>
                </p>
            </form>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get default help content
     * 
     * @return string
     */
    protected function getDefaultHelp()
    {
        ob_start();
        ?>
        <div class="vision-card">
            <h2><?php _e('Help & Documentation', $this->config['text_domain']); ?></h2>
            <p><?php _e('Get help with using this plugin and find documentation.', $this->config['text_domain']); ?></p>
            
            <div class="vision-help-sections">
                <div class="vision-help-section">
                    <h3><?php _e('Getting Started', $this->config['text_domain']); ?></h3>
                    <ul>
                        <li><a href="#getting-started-setup"><?php _e('Initial Setup', $this->config['text_domain']); ?></a></li>
                        <li><a href="#getting-started-config"><?php _e('Configuration', $this->config['text_domain']); ?></a></li>
                        <li><a href="#getting-started-features"><?php _e('Key Features', $this->config['text_domain']); ?></a></li>
                    </ul>
                </div>
                
                <div class="vision-help-section">
                    <h3><?php _e('Frequently Asked Questions', $this->config['text_domain']); ?></h3>
                    <div class="vision-faq-item">
                        <h4><?php _e('How do I reset the plugin settings?', $this->config['text_domain']); ?></h4>
                        <p><?php _e('You can reset all plugin settings by deactivating and reactivating the plugin, or by using the reset option in the Settings tab.', $this->config['text_domain']); ?></p>
                    </div>
                    <div class="vision-faq-item">
                        <h4><?php _e('Is this plugin compatible with my theme?', $this->config['text_domain']); ?></h4>
                        <p><?php _e('This plugin is designed to work with any properly coded WordPress theme. If you experience issues, please contact support.', $this->config['text_domain']); ?></p>
                    </div>
                </div>
                
                <div class="vision-help-section">
                    <h3><?php _e('Support', $this->config['text_domain']); ?></h3>
                    <p><?php _e('Need additional help? Contact our support team:', $this->config['text_domain']); ?></p>
                    <ul>
                        <li><strong><?php _e('Email:', $this->config['text_domain']); ?></strong> <EMAIL></li>
                        <li><strong><?php _e('Documentation:', $this->config['text_domain']); ?></strong> <a href="#" target="_blank"><?php _e('View Online Docs', $this->config['text_domain']); ?></a></li>
                        <li><strong><?php _e('Support Forum:', $this->config['text_domain']); ?></strong> <a href="#" target="_blank"><?php _e('Visit Forum', $this->config['text_domain']); ?></a></li>
                    </ul>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get default about content
     * 
     * @return string
     */
    public function getDefaultAbout()
    {
        ob_start();
        ?>
        <div class="vision-card">
            <h2><?php _e('About', $this->config['text_domain']); ?> <?php echo esc_html($this->config['plugin_name']); ?></h2>
            
            <div class="vision-about-content">
                <div class="vision-about-section">
                    <h3><?php _e('Plugin Information', $this->config['text_domain']); ?></h3>
                    <table class="vision-info-table">
                        <tr>
                            <td><strong><?php _e('Version:', $this->config['text_domain']); ?></strong></td>
                            <td><?php echo esc_html($this->config['version']); ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php _e('Author:', $this->config['text_domain']); ?></strong></td>
                            <td><?php echo esc_html($this->config['author'] ?? 'Your Brand'); ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php _e('Website:', $this->config['text_domain']); ?></strong></td>
                            <td><a href="<?php echo esc_url($this->config['website'] ?? '#'); ?>" target="_blank"><?php echo esc_html($this->config['website'] ?? 'https://example.com'); ?></a></td>
                        </tr>
                        <tr>
                            <td><strong><?php _e('License:', $this->config['text_domain']); ?></strong></td>
                            <td><?php echo esc_html($this->config['license'] ?? 'GPL v2 or later'); ?></td>
                        </tr>
                    </table>
                </div>
                
                <div class="vision-about-section">
                    <h3><?php _e('Credits', $this->config['text_domain']); ?></h3>
                    <p><?php _e('This plugin was built using the Vision Framework, a standardized UI/UX and architecture framework for WordPress plugins.', $this->config['text_domain']); ?></p>
                    
                    <h4><?php _e('Framework Features:', $this->config['text_domain']); ?></h4>
                    <ul>
                        <li><?php _e('Unified visual identity with light/dark theme support', $this->config['text_domain']); ?></li>
                        <li><?php _e('Responsive design with mobile-first approach', $this->config['text_domain']); ?></li>
                        <li><?php _e('AJAX-enhanced navigation with no page reloads', $this->config['text_domain']); ?></li>
                        <li><?php _e('Modular architecture with security best practices', $this->config['text_domain']); ?></li>
                        <li><?php _e('Extensible tab system for custom functionality', $this->config['text_domain']); ?></li>
                    </ul>
                </div>
                
                <div class="vision-about-section">
                    <h3><?php _e('Changelog', $this->config['text_domain']); ?></h3>
                    <div class="vision-changelog">
                        <div class="vision-changelog-item">
                            <h4>v<?php echo esc_html($this->config['version']); ?></h4>
                            <ul>
                                <li><?php _e('Initial release with Vision Framework', $this->config['text_domain']); ?></li>
                                <li><?php _e('Implemented standardized UI/UX design system', $this->config['text_domain']); ?></li>
                                <li><?php _e('Added responsive layout with theme support', $this->config['text_domain']); ?></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Add a custom tab
     * 
     * @param string $key Tab key
     * @param array $tab Tab configuration
     */
    public function addTab($key, $tab)
    {
        $this->tabs[$key] = $tab;
    }
    
    /**
     * Remove a tab
     * 
     * @param string $key Tab key
     */
    public function removeTab($key)
    {
        unset($this->tabs[$key]);
    }
    
    /**
     * Get current tab
     * 
     * @return string
     */
    public function getCurrentTab()
    {
        return $this->currentTab;
    }
}