<?php
/**
 * Keyword Extractor
 * 
 * Handles AI-powered SEO keyword extraction from WordPress pages
 */

namespace BlogWriter;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * KeywordExtractor Class
 */
class KeywordExtractor
{
    /**
     * Database handler instance
     * 
     * @var BlogWriterDatabaseHandler
     */
    protected $databaseHandler;
    
    /**
     * Plugin configuration
     * 
     * @var array
     */
    protected $config;
    
    /**
     * Constructor
     * 
     * @param BlogWriterDatabaseHandler $databaseHandler
     * @param array $config
     */
    public function __construct($databaseHandler, $config)
    {
        $this->databaseHandler = $databaseHandler;
        $this->config = $config;
    }
    
    /**
     * Extract keywords from a specific page
     * 
     * @param int $page_id WordPress page ID
     * @param array $options Extraction options
     * @return array|WP_Error
     */
    public function extractPageKeywords($page_id, $options = [])
    {
        $page = get_post($page_id);
        
        if (!$page || $page->post_type !== 'page') {
            return new \WP_Error('invalid_page', __('Invalid page ID provided.', $this->config['text_domain']));
        }
        
        // Get page content
        $content = $this->preparePageContent($page);
        
        // Get AI model settings
        $model = $options['model'] ?? $this->databaseHandler->getBlogSetting('keyword_extraction_model', 'gpt-3.5-turbo');
        $api_key = $this->databaseHandler->getBlogSetting('openrouter_api_key', '');
        
        if (empty($api_key)) {
            return new \WP_Error('no_api_key', __('OpenRouter API key not configured.', $this->config['text_domain']));
        }
        
        try {
            // Extract keywords using AI
            $keywords = $this->performAIExtraction($content, $model, $api_key, $options);
            
            if (is_wp_error($keywords)) {
                return $keywords;
            }
            
            // Save to database
            $page_data = [
                'title' => $page->post_title,
                'url' => get_permalink($page->ID),
                'status' => $page->post_status,
                'keywords' => implode(', ', $keywords),
                'scan_model' => $model,
                'scan_settings' => $options
            ];
            
            $result = $this->databaseHandler->savePageData($page_id, $page_data);
            
            if ($result === false) {
                return new \WP_Error('save_failed', __('Failed to save keyword data to database.', $this->config['text_domain']));
            }
            
            // Log the extraction
            $this->databaseHandler->log('info', 'Keywords extracted for page', [
                'page_id' => $page_id,
                'page_title' => $page->post_title,
                'keywords_count' => count($keywords),
                'model' => $model
            ]);
            
            return [
                'keywords' => $keywords,
                'page_id' => $page_id,
                'page_title' => $page->post_title,
                'model_used' => $model,
                'extraction_time' => current_time('mysql')
            ];
            
        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'Keyword extraction failed', [
                'page_id' => $page_id,
                'error' => $e->getMessage(),
                'model' => $model
            ]);
            
            return new \WP_Error('extraction_failed', 
                sprintf(__('Keyword extraction failed: %s', $this->config['text_domain']), $e->getMessage())
            );
        }
    }
    
    /**
     * Extract keywords from multiple pages
     * 
     * @param array $page_ids Array of page IDs
     * @param array $options Extraction options
     * @return array
     */
    public function extractMultiplePages($page_ids, $options = [])
    {
        $results = [];
        $errors = [];
        
        foreach ($page_ids as $page_id) {
            $result = $this->extractPageKeywords($page_id, $options);
            
            if (is_wp_error($result)) {
                $errors[] = [
                    'page_id' => $page_id,
                    'error' => $result->get_error_message()
                ];
            } else {
                $results[] = $result;
            }
            
            // Add a small delay to avoid rate limiting
            if (count($page_ids) > 1) {
                sleep(1);
            }
        }
        
        return [
            'successful' => $results,
            'errors' => $errors,
            'total_processed' => count($page_ids),
            'success_count' => count($results),
            'error_count' => count($errors)
        ];
    }
    
    /**
     * Get all pages that need keyword extraction
     * 
     * @param array $args Query arguments
     * @return array
     */
    public function getPagesForExtraction($args = [])
    {
        $defaults = [
            'post_status' => ['publish', 'draft'],
            'post_type' => 'page',
            'posts_per_page' => -1,
            'meta_query' => []
        ];
        
        $args = wp_parse_args($args, $defaults);
        
        $pages = get_posts($args);
        $page_data = [];
        
        foreach ($pages as $page) {
            $existing_data = $this->databaseHandler->getPageData($page->ID);
            
            $page_data[] = [
                'id' => $page->ID,
                'title' => $page->post_title,
                'status' => $page->post_status,
                'url' => get_permalink($page->ID),
                'has_keywords' => !empty($existing_data['keywords']),
                'last_scan' => $existing_data['last_scan_date'] ?? null,
                'keywords' => $existing_data['keywords'] ?? '',
                'word_count' => str_word_count(strip_tags($page->post_content)),
                'modified' => $page->post_modified
            ];
        }
        
        return $page_data;
    }
    
    /**
     * Prepare page content for AI analysis
     * 
     * @param WP_Post $page
     * @return string
     */
    protected function preparePageContent($page)
    {
        // Get the full content including meta
        $content = $page->post_content;
        
        // Process shortcodes and blocks
        $content = do_shortcode($content);
        $content = apply_filters('the_content', $content);
        
        // Clean HTML tags but preserve structure
        $content = wp_strip_all_tags($content);
        
        // Add title and excerpt for context
        $full_content = $page->post_title . "\n\n";
        
        if (!empty($page->post_excerpt)) {
            $full_content .= $page->post_excerpt . "\n\n";
        }
        
        $full_content .= $content;
        
        // Add SEO meta if available (Yoast, RankMath, etc.)
        $seo_data = $this->getSEOMetaData($page->ID);
        if (!empty($seo_data)) {
            $full_content .= "\n\nSEO Data:\n" . implode("\n", $seo_data);
        }
        
        // Limit content length to avoid API limits
        return substr(trim($full_content), 0, 8000);
    }
    
    /**
     * Get SEO meta data from popular plugins
     * 
     * @param int $page_id
     * @return array
     */
    protected function getSEOMetaData($page_id)
    {
        $seo_data = [];
        
        // Yoast SEO
        if (class_exists('WPSEO_Meta')) {
            $yoast_title = get_post_meta($page_id, '_yoast_wpseo_title', true);
            $yoast_desc = get_post_meta($page_id, '_yoast_wpseo_metadesc', true);
            $yoast_keywords = get_post_meta($page_id, '_yoast_wpseo_focuskw', true);
            
            if ($yoast_title) $seo_data[] = "SEO Title: " . $yoast_title;
            if ($yoast_desc) $seo_data[] = "Meta Description: " . $yoast_desc;
            if ($yoast_keywords) $seo_data[] = "Focus Keywords: " . $yoast_keywords;
        }
        
        // RankMath
        if (class_exists('RankMath')) {
            $rm_title = get_post_meta($page_id, 'rank_math_title', true);
            $rm_desc = get_post_meta($page_id, 'rank_math_description', true);
            $rm_keywords = get_post_meta($page_id, 'rank_math_focus_keyword', true);
            
            if ($rm_title) $seo_data[] = "SEO Title: " . $rm_title;
            if ($rm_desc) $seo_data[] = "Meta Description: " . $rm_desc;
            if ($rm_keywords) $seo_data[] = "Focus Keywords: " . $rm_keywords;
        }
        
        // All in One SEO
        if (class_exists('AIOSEO\\Plugin\\AIOSEO')) {
            $aio_title = get_post_meta($page_id, '_aioseo_title', true);
            $aio_desc = get_post_meta($page_id, '_aioseo_description', true);
            $aio_keywords = get_post_meta($page_id, '_aioseo_keywords', true);
            
            if ($aio_title) $seo_data[] = "SEO Title: " . $aio_title;
            if ($aio_desc) $seo_data[] = "Meta Description: " . $aio_desc;
            if ($aio_keywords) $seo_data[] = "Keywords: " . $aio_keywords;
        }
        
        return $seo_data;
    }
    
    /**
     * Perform AI-powered keyword extraction
     * 
     * @param string $content Page content
     * @param string $model AI model to use
     * @param string $api_key OpenRouter API key
     * @param array $options Extraction options
     * @return array|WP_Error
     */
    protected function performAIExtraction($content, $model, $api_key, $options = [])
    {
        $max_keywords = $options['max_keywords'] ?? 15;
        $focus_type = $options['focus_type'] ?? 'seo'; // seo, semantic, industry
        
        // Build the prompt based on focus type
        $prompt = $this->buildExtractionPrompt($content, $max_keywords, $focus_type);
        
        $api_url = 'https://openrouter.ai/api/v1/chat/completions';
        
        $headers = [
            'Authorization' => 'Bearer ' . $api_key,
            'Content-Type' => 'application/json',
            'HTTP-Referer' => home_url(),
            'X-Title' => get_bloginfo('name') . ' - Blog Writer Plugin'
        ];
        
        $body = [
            'model' => $model,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are an expert SEO analyst specializing in keyword extraction for small to medium businesses. Extract relevant, searchable keywords that potential customers would use to find this content.'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'temperature' => 0.3,
            'max_tokens' => 500
        ];
        
        $response = wp_remote_post($api_url, [
            'headers' => $headers,
            'body' => wp_json_encode($body),
            'timeout' => 30
        ]);
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($response_code !== 200) {
            $error_data = json_decode($response_body, true);
            $error_message = $error_data['error']['message'] ?? 'Unknown API error';
            
            return new \WP_Error('api_error', 
                sprintf(__('API request failed (%d): %s', $this->config['text_domain']), $response_code, $error_message)
            );
        }
        
        $data = json_decode($response_body, true);
        
        if (!isset($data['choices'][0]['message']['content'])) {
            return new \WP_Error('invalid_response', __('Invalid API response format.', $this->config['text_domain']));
        }
        
        // Parse keywords from AI response
        $keywords = $this->parseKeywordsFromResponse($data['choices'][0]['message']['content']);
        
        if (empty($keywords)) {
            return new \WP_Error('no_keywords', __('No keywords extracted from content.', $this->config['text_domain']));
        }
        
        return array_slice($keywords, 0, $max_keywords);
    }
    
    /**
     * Build extraction prompt based on focus type
     * 
     * @param string $content
     * @param int $max_keywords
     * @param string $focus_type
     * @return string
     */
    protected function buildExtractionPrompt($content, $max_keywords, $focus_type)
    {
        $base_prompt = "Analyze the following webpage content and extract the most relevant SEO keywords.\n\n";
        $base_prompt .= "Content:\n" . $content . "\n\n";
        
        switch ($focus_type) {
            case 'semantic':
                $base_prompt .= "Focus on semantic keywords and related terms that capture the meaning and context of the content. ";
                break;
            case 'industry':
                $base_prompt .= "Focus on industry-specific terms and professional keywords that experts in this field would use. ";
                break;
            default: // seo
                $base_prompt .= "Focus on keywords that potential customers would search for when looking for this type of content or service. ";
        }
        
        $base_prompt .= "Extract up to {$max_keywords} keywords and phrases that are:\n";
        $base_prompt .= "1. Relevant to the content\n";
        $base_prompt .= "2. Likely to be searched by users\n";
        $base_prompt .= "3. Specific enough to be useful for SEO\n";
        $base_prompt .= "4. Include both short keywords (1-2 words) and long-tail phrases (3-5 words)\n\n";
        $base_prompt .= "Return only the keywords, one per line, without numbering or bullet points.";
        
        return $base_prompt;
    }
    
    /**
     * Parse keywords from AI response
     * 
     * @param string $response
     * @return array
     */
    protected function parseKeywordsFromResponse($response)
    {
        // Clean the response
        $response = trim($response);
        
        // Split by lines
        $lines = explode("\n", $response);
        $keywords = [];
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Skip empty lines
            if (empty($line)) {
                continue;
            }
            
            // Remove numbering and bullet points
            $line = preg_replace('/^[\d\.\-\*\+\>\s]+/', '', $line);
            $line = trim($line);
            
            // Remove quotes
            $line = trim($line, '"\'');
            
            // Skip if still empty or too long
            if (empty($line) || strlen($line) > 100) {
                continue;
            }
            
            // Convert to lowercase for consistency
            $keywords[] = strtolower($line);
        }
        
        // Remove duplicates and return
        return array_unique($keywords);
    }
    
    /**
     * Get keyword extraction statistics
     * 
     * @return array
     */
    public function getExtractionStats()
    {
        $total_pages = wp_count_posts('page');
        $extracted_pages = count($this->databaseHandler->getAllPages());
        
        return [
            'total_pages' => $total_pages->publish + $total_pages->draft,
            'pages_with_keywords' => $extracted_pages,
            'extraction_coverage' => $total_pages->publish > 0 ? 
                round(($extracted_pages / $total_pages->publish) * 100, 1) : 0,
            'last_extraction' => $this->getLastExtractionDate()
        ];
    }
    
    /**
     * Get last extraction date
     * 
     * @return string|null
     */
    protected function getLastExtractionDate()
    {
        global $wpdb;
        
        if (!$this->databaseHandler->tableExists('pages')) {
            return null;
        }
        
        $table_name = $wpdb->prefix . str_replace('-', '_', $this->config['text_domain']) . '_pages';
        
        $last_date = $wpdb->get_var(
            "SELECT MAX(last_scan_date) FROM {$table_name}"
        );
        
        return $last_date;
    }
    
    /**
     * Clean and validate keywords
     * 
     * @param string $keywords Comma-separated keywords
     * @return string
     */
    public function cleanKeywords($keywords)
    {
        if (empty($keywords)) {
            return '';
        }
        
        $keyword_array = explode(',', $keywords);
        $cleaned = [];
        
        foreach ($keyword_array as $keyword) {
            $keyword = trim($keyword);
            $keyword = strtolower($keyword);
            
            // Skip if too short or too long
            if (strlen($keyword) < 2 || strlen($keyword) > 100) {
                continue;
            }
            
            // Remove special characters except spaces and hyphens
            $keyword = preg_replace('/[^a-z0-9\s\-]/', '', $keyword);
            $keyword = trim($keyword);
            
            if (!empty($keyword)) {
                $cleaned[] = $keyword;
            }
        }
        
        return implode(', ', array_unique($cleaned));
    }
}