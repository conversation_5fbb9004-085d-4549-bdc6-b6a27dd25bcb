<?php
/**
 * Database Handler
 * 
 * Handles database operations with security and best practices
 */

namespace VisionFramework\Core;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * DatabaseHandler Class
 */
class DatabaseHandler
{
    /**
     * Plugin configuration
     * 
     * @var array
     */
    protected $config;
    
    /**
     * WordPress database instance
     * 
     * @var \wpdb
     */
    protected $wpdb;
    
    /**
     * Table names
     * 
     * @var array
     */
    protected $tables = [];
    
    /**
     * Constructor
     * 
     * @param array $config Plugin configuration
     */
    public function __construct($config)
    {
        global $wpdb;
        
        $this->config = $config;
        $this->wpdb = $wpdb;
        
        $this->initTables();
        
        // Add a filter to catch and redirect any queries to old table names
        add_filter('query', [$this, 'redirectOldTableQueries']);
    }
    
    /**
     * Redirect queries from old hyphenated table names to new underscore names
     * 
     * @param string $query The database query
     * @return string Modified query
     */
    public function redirectOldTableQueries($query)
    {
        // Only redirect queries that reference our old table names
        $text_domain = $this->config['text_domain'] ?? 'unknown';
        $old_prefix = $this->wpdb->prefix . str_replace('_', '-', $text_domain) . '_';
        $new_prefix = $this->wpdb->prefix . str_replace('-', '_', $text_domain) . '_';
        
        if (strpos($query, $old_prefix) !== false) {
            $query = str_replace($old_prefix, $new_prefix, $query);
        }
        
        return $query;
    }
    
    /**
     * Initialize table names
     */
    protected function initTables()
    {
        // Replace hyphens with underscores to ensure valid MySQL table names
        $prefix = str_replace('-', '_', $this->config['text_domain']);
        
        $this->tables = [
            'logs' => $this->wpdb->prefix . $prefix . '_logs',
            'settings' => $this->wpdb->prefix . $prefix . '_settings',
            'data' => $this->wpdb->prefix . $prefix . '_data'
        ];
        
        // Clear any cached table existence information
        $this->clearTableCache();
    }
    
    /**
     * Clear cached table information
     */
    protected function clearTableCache()
    {
        foreach ($this->tables as $table_name) {
            wp_cache_delete($table_name, 'table_exists');
        }
        
        // Also clear WordPress internal table cache
        wp_cache_flush_group('table_exists');
    }
    
    /**
     * Refresh table names and clear cache
     */
    public function refreshTables()
    {
        $this->initTables();
    }
    
    /**
     * Create plugin tables
     */
    public function createTables()
    {
        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        
        $charset_collate = $this->wpdb->get_charset_collate();
        
        // Create logs table
        $logs_sql = "CREATE TABLE {$this->tables['logs']} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            level varchar(10) NOT NULL DEFAULT 'info',
            message text NOT NULL,
            context longtext DEFAULT NULL,
            user_id bigint(20) unsigned DEFAULT NULL,
            ip_address varchar(45) DEFAULT NULL,
            user_agent varchar(500) DEFAULT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY level (level),
            KEY created_at (created_at),
            KEY user_id (user_id)
        ) $charset_collate;";
        
        dbDelta($logs_sql);
        
        // Create settings table for complex settings
        $settings_sql = "CREATE TABLE {$this->tables['settings']} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            setting_key varchar(100) NOT NULL,
            setting_value longtext NOT NULL,
            setting_type varchar(20) NOT NULL DEFAULT 'string',
            autoload varchar(10) NOT NULL DEFAULT 'yes',
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY setting_key (setting_key),
            KEY autoload (autoload)
        ) $charset_collate;";
        
        dbDelta($settings_sql);
        
        // Create data table for plugin-specific data
        $data_sql = "CREATE TABLE {$this->tables['data']} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            data_type varchar(50) NOT NULL,
            data_key varchar(255) NOT NULL,
            data_value longtext NOT NULL,
            meta_data longtext DEFAULT NULL,
            status varchar(20) NOT NULL DEFAULT 'active',
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY data_type_key (data_type, data_key),
            KEY data_type (data_type),
            KEY status (status),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        dbDelta($data_sql);
        
        // Update database version
        update_option($this->config['text_domain'] . '_db_version', $this->config['db_version']);
    }
    
    /**
     * Force recreation of tables (drops and recreates)
     */
    public function forceRecreate()
    {
        // Refresh table names to ensure we have the correct ones
        $this->refreshTables();
        
        // Drop existing tables
        $this->dropTables();
        
        // Also clean up any old hyphenated tables
        $this->cleanupOldTables();
        
        // Clear any WordPress cached table information
        $this->clearTableCache();
        
        // Recreate tables
        $this->createTables();
    }
    
    /**
     * Clean up old tables with invalid hyphen names
     */
    protected function cleanupOldTables()
    {
        // List of potential old table names with hyphens
        $text_domain = $this->config['text_domain'] ?? 'unknown';
        $old_tables = [
            $this->wpdb->prefix . str_replace('_', '-', $text_domain) . '_logs',
            $this->wpdb->prefix . str_replace('_', '-', $text_domain) . '_settings', 
            $this->wpdb->prefix . str_replace('_', '-', $text_domain) . '_data'
        ];
        
        foreach ($old_tables as $table) {
            // Use backticks to handle hyphenated table names in DROP statement
            $this->wpdb->query("DROP TABLE IF EXISTS `{$table}`");
        }
    }
    
    /**
     * Drop plugin tables
     */
    public function dropTables()
    {
        foreach ($this->tables as $table) {
            $this->wpdb->query("DROP TABLE IF EXISTS {$table}");
        }
    }
    
    /**
     * Log an entry
     * 
     * @param string $level Log level (info, warning, error, debug)
     * @param string $message Log message
     * @param array $context Additional context data
     * @return bool|int
     */
    public function log($level, $message, $context = [])
    {
        // First, ensure we have the correct table names
        $this->refreshTables();
        
        // Check if logs table exists first
        if (!$this->tableExists('logs')) {
            // If table doesn't exist, try to create it silently
            try {
                $this->createTables();
            } catch (Exception $e) {
                // If creation fails, just return false to avoid errors
                return false;
            }
            
            // Check again after creation attempt
            if (!$this->tableExists('logs')) {
                return false;
            }
        }
        
        // Sanitize inputs
        $level = sanitize_key($level);
        $message = sanitize_text_field($message);
        
        // Get current user info
        $user_id = get_current_user_id();
        $ip_address = $this->getUserIpAddress();
        $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? sanitize_text_field($_SERVER['HTTP_USER_AGENT']) : '';
        
        // Prepare context data
        $context_json = !empty($context) ? wp_json_encode($context) : null;
        
        return $this->wpdb->insert(
            $this->tables['logs'],
            [
                'level' => $level,
                'message' => $message,
                'context' => $context_json,
                'user_id' => $user_id ?: null,
                'ip_address' => $ip_address,
                'user_agent' => substr($user_agent, 0, 500),
                'created_at' => current_time('mysql')
            ],
            [
                '%s',
                '%s',
                '%s',
                '%d',
                '%s',
                '%s',
                '%s'
            ]
        );
    }
    
    /**
     * Get logs
     * 
     * @param array $args Query arguments
     * @return array
     */
    public function getLogs($args = [])
    {
        // Check if logs table exists first
        if (!$this->tableExists('logs')) {
            return [];
        }
        
        $defaults = [
            'level' => '',
            'limit' => 50,
            'offset' => 0,
            'order' => 'DESC',
            'start_date' => '',
            'end_date' => ''
        ];
        
        $args = wp_parse_args($args, $defaults);
        
        $sql = "SELECT * FROM {$this->tables['logs']} WHERE 1=1";
        $params = [];
        
        // Filter by level
        if (!empty($args['level'])) {
            $sql .= " AND level = %s";
            $params[] = $args['level'];
        }
        
        // Filter by date range
        if (!empty($args['start_date'])) {
            $sql .= " AND created_at >= %s";
            $params[] = $args['start_date'];
        }
        
        if (!empty($args['end_date'])) {
            $sql .= " AND created_at <= %s";
            $params[] = $args['end_date'];
        }
        
        // Order and limit
        $order = in_array(strtoupper($args['order']), ['ASC', 'DESC']) ? strtoupper($args['order']) : 'DESC';
        $sql .= " ORDER BY created_at {$order}";
        
        if ($args['limit'] > 0) {
            $sql .= " LIMIT %d";
            $params[] = $args['limit'];
            
            if ($args['offset'] > 0) {
                $sql .= " OFFSET %d";
                $params[] = $args['offset'];
            }
        }
        
        if (!empty($params)) {
            $sql = $this->wpdb->prepare($sql, $params);
        }
        
        return $this->wpdb->get_results($sql, ARRAY_A);
    }
    
    /**
     * Clear logs
     * 
     * @param array $args Clear arguments
     * @return bool|int
     */
    public function clearLogs($args = [])
    {
        // Check if logs table exists first
        if (!$this->tableExists('logs')) {
            return false;
        }
        
        $defaults = [
            'level' => '',
            'older_than_days' => 0
        ];
        
        $args = wp_parse_args($args, $defaults);
        
        $sql = "DELETE FROM {$this->tables['logs']} WHERE 1=1";
        $params = [];
        
        // Filter by level
        if (!empty($args['level'])) {
            $sql .= " AND level = %s";
            $params[] = $args['level'];
        }
        
        // Filter by age
        if ($args['older_than_days'] > 0) {
            $sql .= " AND created_at < DATE_SUB(NOW(), INTERVAL %d DAY)";
            $params[] = $args['older_than_days'];
        }
        
        if (!empty($params)) {
            $sql = $this->wpdb->prepare($sql, $params);
        }
        
        return $this->wpdb->query($sql);
    }
    
    /**
     * Get total log count
     * 
     * @param array $args Query arguments
     * @return int
     */
    public function getLogCount($args = [])
    {
        // Check if logs table exists first
        if (!$this->tableExists('logs')) {
            return 0;
        }
        
        $defaults = [
            'level' => '',
            'start_date' => '',
            'end_date' => ''
        ];
        
        $args = wp_parse_args($args, $defaults);
        
        $sql = "SELECT COUNT(*) FROM {$this->tables['logs']} WHERE 1=1";
        $params = [];
        
        // Filter by level
        if (!empty($args['level'])) {
            $sql .= " AND level = %s";
            $params[] = $args['level'];
        }
        
        // Filter by date range
        if (!empty($args['start_date'])) {
            $sql .= " AND created_at >= %s";
            $params[] = $args['start_date'];
        }
        
        if (!empty($args['end_date'])) {
            $sql .= " AND created_at <= %s";
            $params[] = $args['end_date'];
        }
        
        if (!empty($params)) {
            $sql = $this->wpdb->prepare($sql, $params);
        }
        
        return (int) $this->wpdb->get_var($sql);
    }
    
    /**
     * Save plugin setting
     * 
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @param string $type Value type (string, array, object)
     * @param bool $autoload Whether to autoload
     * @return bool|int
     */
    public function saveSetting($key, $value, $type = 'string', $autoload = true)
    {
        $key = sanitize_key($key);
        
        // Serialize complex data types
        if (in_array($type, ['array', 'object']) || is_array($value) || is_object($value)) {
            $value = maybe_serialize($value);
            $type = is_array($value) ? 'array' : 'object';
        }
        
        $autoload = $autoload ? 'yes' : 'no';
        
        // Check if setting exists
        $existing = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT id FROM {$this->tables['settings']} WHERE setting_key = %s",
            $key
        ));
        
        if ($existing) {
            // Update existing setting
            return $this->wpdb->update(
                $this->tables['settings'],
                [
                    'setting_value' => $value,
                    'setting_type' => $type,
                    'autoload' => $autoload,
                    'updated_at' => current_time('mysql')
                ],
                ['setting_key' => $key],
                ['%s', '%s', '%s', '%s'],
                ['%s']
            );
        } else {
            // Insert new setting
            return $this->wpdb->insert(
                $this->tables['settings'],
                [
                    'setting_key' => $key,
                    'setting_value' => $value,
                    'setting_type' => $type,
                    'autoload' => $autoload,
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                ],
                ['%s', '%s', '%s', '%s', '%s', '%s']
            );
        }
    }
    
    /**
     * Get plugin setting
     * 
     * @param string $key Setting key
     * @param mixed $default Default value
     * @return mixed
     */
    public function getSetting($key, $default = null)
    {
        $key = sanitize_key($key);
        
        $result = $this->wpdb->get_row($this->wpdb->prepare(
            "SELECT setting_value, setting_type FROM {$this->tables['settings']} WHERE setting_key = %s",
            $key
        ));
        
        if (!$result) {
            return $default;
        }
        
        $value = $result->setting_value;
        
        // Unserialize if needed
        if (in_array($result->setting_type, ['array', 'object'])) {
            $value = maybe_unserialize($value);
        }
        
        return $value;
    }
    
    /**
     * Delete plugin setting
     * 
     * @param string $key Setting key
     * @return bool|int
     */
    public function deleteSetting($key)
    {
        $key = sanitize_key($key);
        
        return $this->wpdb->delete(
            $this->tables['settings'],
            ['setting_key' => $key],
            ['%s']
        );
    }
    
    /**
     * Save data entry
     * 
     * @param string $type Data type
     * @param string $key Data key
     * @param mixed $value Data value
     * @param array $meta Meta data
     * @param string $status Entry status
     * @return bool|int
     */
    public function saveData($type, $key, $value, $meta = [], $status = 'active')
    {
        $type = sanitize_key($type);
        $key = sanitize_text_field($key);
        $status = sanitize_key($status);
        
        // Serialize value and meta
        $value = maybe_serialize($value);
        $meta = !empty($meta) ? wp_json_encode($meta) : null;
        
        // Check if data exists
        $existing = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT id FROM {$this->tables['data']} WHERE data_type = %s AND data_key = %s",
            $type,
            $key
        ));
        
        if ($existing) {
            // Update existing data
            return $this->wpdb->update(
                $this->tables['data'],
                [
                    'data_value' => $value,
                    'meta_data' => $meta,
                    'status' => $status,
                    'updated_at' => current_time('mysql')
                ],
                [
                    'data_type' => $type,
                    'data_key' => $key
                ],
                ['%s', '%s', '%s', '%s'],
                ['%s', '%s']
            );
        } else {
            // Insert new data
            return $this->wpdb->insert(
                $this->tables['data'],
                [
                    'data_type' => $type,
                    'data_key' => $key,
                    'data_value' => $value,
                    'meta_data' => $meta,
                    'status' => $status,
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                ],
                ['%s', '%s', '%s', '%s', '%s', '%s', '%s']
            );
        }
    }
    
    /**
     * Get data entries
     * 
     * @param string $type Data type
     * @param string $key Optional specific key
     * @param array $args Query arguments
     * @return array
     */
    public function getData($type, $key = '', $args = [])
    {
        $type = sanitize_key($type);
        
        $defaults = [
            'status' => 'active',
            'limit' => 0,
            'offset' => 0,
            'order' => 'DESC'
        ];
        
        $args = wp_parse_args($args, $defaults);
        
        $sql = "SELECT * FROM {$this->tables['data']} WHERE data_type = %s";
        $params = [$type];
        
        if (!empty($key)) {
            $sql .= " AND data_key = %s";
            $params[] = sanitize_text_field($key);
        }
        
        if (!empty($args['status'])) {
            $sql .= " AND status = %s";
            $params[] = $args['status'];
        }
        
        // Order and limit
        $order = in_array(strtoupper($args['order']), ['ASC', 'DESC']) ? strtoupper($args['order']) : 'DESC';
        $sql .= " ORDER BY updated_at {$order}";
        
        if ($args['limit'] > 0) {
            $sql .= " LIMIT %d";
            $params[] = $args['limit'];
            
            if ($args['offset'] > 0) {
                $sql .= " OFFSET %d";
                $params[] = $args['offset'];
            }
        }
        
        $sql = $this->wpdb->prepare($sql, $params);
        $results = $this->wpdb->get_results($sql, ARRAY_A);
        
        // Unserialize data values
        foreach ($results as &$result) {
            $result['data_value'] = maybe_unserialize($result['data_value']);
            if (!empty($result['meta_data'])) {
                $result['meta_data'] = json_decode($result['meta_data'], true);
            }
        }
        
        return !empty($key) && count($results) === 1 ? $results[0] : $results;
    }
    
    /**
     * Delete data entries
     * 
     * @param string $type Data type
     * @param string $key Optional specific key
     * @return bool|int
     */
    public function deleteData($type, $key = '')
    {
        $type = sanitize_key($type);
        
        $where = ['data_type' => $type];
        $where_format = ['%s'];
        
        if (!empty($key)) {
            $where['data_key'] = sanitize_text_field($key);
            $where_format[] = '%s';
        }
        
        return $this->wpdb->delete($this->tables['data'], $where, $where_format);
    }
    
    /**
     * Get user IP address
     * 
     * @return string
     */
    protected function getUserIpAddress()
    {
        // Check for various IP headers
        $ip_headers = [
            'HTTP_CF_CONNECTING_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];
        
        foreach ($ip_headers as $header) {
            if (isset($_SERVER[$header]) && !empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                
                // Handle comma-separated IPs
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip);
                    $ip = trim($ip[0]);
                }
                
                // Validate IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }
    
    /**
     * Get table name
     * 
     * @param string $table Table identifier
     * @return string|null
     */
    public function getTable($table)
    {
        return isset($this->tables[$table]) ? $this->tables[$table] : null;
    }
    
    /**
     * Check if a table exists
     * 
     * @param string $table Table identifier
     * @return bool
     */
    public function tableExists($table)
    {
        if (!isset($this->tables[$table])) {
            return false;
        }
        
        $table_name = $this->tables[$table];
        
        // Clear any existing cache first
        wp_cache_delete($table_name, 'table_exists');
        
        // Use a more reliable method to check table existence
        $result = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = %s AND table_name = %s",
            DB_NAME,
            $table_name
        ));
        
        $exists = (bool) $result;
        
        // Cache the result for a short time to avoid repeated queries
        wp_cache_set($table_name, $exists, 'table_exists', 300);
        
        return $exists;
    }
}