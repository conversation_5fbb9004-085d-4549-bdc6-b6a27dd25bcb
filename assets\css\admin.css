/*
 * Vision Framework Admin Styles
 * Standardized UI/UX for WordPress plugins
 */

:root {
  /* System font stack */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* Light theme colors */
  --color-primary: #0073aa;
  --color-primary-hover: #005a87;
  --color-secondary: #646970;
  --color-success: #00a32a;
  --color-warning: #dba617;
  --color-error: #d63638;
  
  /* Background colors */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f0f0f1;
  --color-bg-tertiary: #f0f0f1;
  
  /* Text colors */
  --color-text-primary: #1d2327;
  --color-text-secondary: #646970;
  --color-text-tertiary: #8c8f94;
  
  /* Border and shadow */
  --color-border: #c3c4c7;
  --color-border-dark: #000000;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* Border radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  
  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;
  
  /* Light theme specific colors */
  --color-warning-bg: #fff9e6;
  --color-warning-bg-secondary: #ffffff;
  --color-welcome-bg: linear-gradient(135deg, #dbeafe 0%, #ffffff 100%);
  --color-welcome-text: #1e40af;
  --color-success-bg: #f0fdf4;
  --color-error-bg: #fef2f2;
  --color-blog-writer-light: #dbeafe;
  --color-action-hover-bg: linear-gradient(135deg, #dbeafe 0%, #ffffff 100%);
}

/* WordPress Dark Mode Detection - Simple and reliable */

/* Default light mode colors are already set in :root above */

/* Dark mode overrides for WordPress admin color schemes */
body.admin-color-midnight .vision-framework-wrapper,
body.admin-color-ectoplasm .vision-framework-wrapper {
  --color-bg-primary: #1d2327;
  --color-bg-secondary: #2c3338;
  --color-bg-tertiary: #32373c;
  
  --color-text-primary: #f0f0f1;
  --color-text-secondary: #c3c4c7;
  --color-text-tertiary: #8c8f94;
  
  --color-border: #50575e;
  --color-border-dark: #000000;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.3);
  
  /* Dark mode Blog Writer brand colors */
  --color-blog-writer: #3b82f6;
  --color-blog-writer-hover: #2563eb;
  --color-blog-writer-light: #1e40af;
  
  /* Dark mode warning card */
  --color-warning-bg: #302606;
  --color-warning-bg-secondary: #0f1e2b;
  
  /* Dark mode welcome section */
  --color-welcome-bg: linear-gradient(135deg, #1e3a8a 0%, #1d2327 100%);
  --color-welcome-text: #dbeafe;
  
  /* Dark mode success and other components */
  --color-success-bg: #064e3b;
  --color-error-bg: #7f1d1d;
  --color-action-hover-bg: linear-gradient(135deg, #1e3a8a 0%, #1d2327 100%);
}

/* System dark mode preference */
@media (prefers-color-scheme: dark) {
  .vision-framework-wrapper {
    --color-bg-primary: #1d2327;
    --color-bg-secondary: #2c3338;
    --color-bg-tertiary: #32373c;
    
    --color-text-primary: #f0f0f1;
    --color-text-secondary: #c3c4c7;
    --color-text-tertiary: #8c8f94;
    
    --color-border: #50575e;
    --color-border-dark: #000000;
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.3);
    
    /* Dark mode Blog Writer brand colors */
    --color-blog-writer: #3b82f6;
    --color-blog-writer-hover: #2563eb;
    --color-blog-writer-light: #1e40af;
    
    /* Dark mode warning card */
    --color-warning-bg: #302606;
    --color-warning-bg-secondary: #0f1e2b;
    
    /* Dark mode welcome section */
    --color-welcome-bg: linear-gradient(135deg, #1e3a8a 0%, #1d2327 100%);
    --color-welcome-text: #dbeafe;
    
    /* Dark mode success and other components */
    --color-success-bg: #064e3b;
    --color-error-bg: #7f1d1d;
    --color-action-hover-bg: linear-gradient(135deg, #1e3a8a 0%, #1d2327 100%);
  }
}

/* Manual theme control via data-theme attribute */
[data-theme="dark"] .vision-framework-wrapper {
  --color-bg-primary: #1d2327;
  --color-bg-secondary: #2c3338;
  --color-bg-tertiary: #32373c;
  
  --color-text-primary: #f0f0f1;
  --color-text-secondary: #c3c4c7;
  --color-text-tertiary: #8c8f94;
  
  --color-border: #50575e;
  --color-border-dark: #000000;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.3);
  
  /* Dark mode Blog Writer brand colors */
  --color-blog-writer: #3b82f6;
  --color-blog-writer-hover: #2563eb;
  --color-blog-writer-light: #1e40af;
  
  /* Dark mode warning card */
  --color-warning-bg: #302606;
  --color-warning-bg-secondary: #0f1e2b;
  
  /* Dark mode welcome section */
  --color-welcome-bg: linear-gradient(135deg, #1e3a8a 0%, #1d2327 100%);
  --color-welcome-text: #dbeafe;
  
  /* Dark mode success and other components */
  --color-success-bg: #064e3b;
  --color-error-bg: #7f1d1d;
  --color-action-hover-bg: linear-gradient(135deg, #1e3a8a 0%, #1d2327 100%);
}

[data-theme="light"] .vision-framework-wrapper {
  /* Light mode colors (same as :root defaults) */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f0f0f1;
  --color-bg-tertiary: #f0f0f1;
  
  --color-text-primary: #1d2327;
  --color-text-secondary: #646970;
  --color-text-tertiary: #8c8f94;
  
  --color-border: #c3c4c7;
  --color-border-dark: #000000;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* Light mode specific colors */
  --color-warning-bg: #fff9e6;
  --color-warning-bg-secondary: #ffffff;
  --color-welcome-bg: linear-gradient(135deg, #dbeafe 0%, #ffffff 100%);
  --color-welcome-text: #1e40af;
  --color-success-bg: #f0fdf4;
  --color-error-bg: #fef2f2;
  --color-blog-writer-light: #dbeafe;
  --color-action-hover-bg: linear-gradient(135deg, #dbeafe 0%, #ffffff 100%);
}

/* Base styles */
.vision-framework-wrapper {
  font-family: var(--font-family);
  font-size: 14px;
  line-height: 1.5;
  color: var(--color-text-primary);
  background: var(--color-bg-secondary);
  min-height: 100vh;
  margin: 0 0 0 -20px;
  padding: 0;
  position: relative;
}

/* Bottom fade gradient for dark mode - ONLY within plugin content area */
.vision-framework-wrapper::after {
  content: '';
  display: none;
  width: 100%;
  height: 60px;
  background: linear-gradient(to bottom, 
    var(--color-bg-secondary) 0%, 
    rgba(240, 240, 241, 0.8) 70%, 
    rgba(240, 240, 241, 1) 100%);
  margin-top: var(--spacing-lg);
  pointer-events: none;
}

/* Show the fade ONLY in dark mode contexts - TEMPORARILY DISABLED */
/* 
body.admin-color-midnight .vision-framework-wrapper::after,
body.admin-color-ectoplasm .vision-framework-wrapper::after {
  display: block;
}

[data-theme="dark"] .vision-framework-wrapper::after {
  display: block;
}

@media (prefers-color-scheme: dark) {
  body:not([data-theme="light"]) .vision-framework-wrapper::after {
    display: block;
  }
}
*/

/* Header */
.vision-header {
  background: var(--color-bg-primary);
  border-bottom: 1px solid var(--color-border);
  padding: var(--spacing-sm) var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

.vision-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1500px;
  margin: 0 auto;
}

.vision-header-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.vision-header-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* Theme Toggle */
.vision-theme-toggle {
  background: transparent;
  border: 2px solid var(--color-border);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  transition: all var(--transition-fast);
  padding: 0;
  margin-right: var(--spacing-sm);
}

.vision-theme-toggle:hover {
  border-color: var(--color-primary);
  background: var(--color-bg-secondary);
  transform: scale(1.05);
}

.vision-theme-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.theme-icon {
  position: absolute;
  font-size: 18px;
  transition: all var(--transition-fast);
  opacity: 0;
  transform: scale(0.8);
}

/* Light mode - show sun, hide moon */
[data-theme="light"] .vision-theme-toggle .theme-icon-sun,
body:not([data-theme]) .vision-theme-toggle .theme-icon-sun {
  opacity: 1;
  transform: scale(1);
}

[data-theme="light"] .vision-theme-toggle .theme-icon-moon,
body:not([data-theme]) .vision-theme-toggle .theme-icon-moon {
  opacity: 0;
  transform: scale(0.8);
}

/* Dark mode - show moon, hide sun */
[data-theme="dark"] .vision-theme-toggle .theme-icon-sun {
  opacity: 0;
  transform: scale(0.8);
}

[data-theme="dark"] .vision-theme-toggle .theme-icon-moon {
  opacity: 1;
  transform: scale(1);
}

/* Auto-detect based on WordPress admin colors */
body.admin-color-midnight .vision-theme-toggle .theme-icon-sun,
body.admin-color-ectoplasm .vision-theme-toggle .theme-icon-sun {
  opacity: 0;
  transform: scale(0.8);
}

body.admin-color-midnight .vision-theme-toggle .theme-icon-moon,
body.admin-color-ectoplasm .vision-theme-toggle .theme-icon-moon {
  opacity: 1;
  transform: scale(1);
}

.vision-logo {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background: linear-gradient(135deg, var(--color-blog-writer) 0%, var(--color-blog-writer-hover) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: var(--shadow-sm);
}

.vision-logo svg {
  width: 24px;
  height: 24px;
  fill: white;
}

/* Blog Writer Logo SVG */
.vision-logo::after {
  content: '';
  position: absolute;
  width: 24px;
  height: 24px;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><polyline points="10,9 9,9 8,9"/><circle cx="19" cy="4" r="2" fill="%23fbbf24"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.vision-plugin-title {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: var(--color-text-primary);
  line-height: 1.2;
}

.vision-partner {
  font-size: 12px;
  color: var(--color-text-secondary);
  font-weight: 500;
  margin-top: 2px;
}

.vision-version-badge {
  background: var(--color-bg-tertiary);
  color: var(--color-text-secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 500;
}

.vision-dev-badge {
  background: var(--color-warning);
  color: white;
  margin-left: var(--spacing-xs);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
  font-size: 10px;
}

/* Navigation */
.vision-navigation {
  background: var(--color-bg-primary);
  border-bottom: 1px solid var(--color-border);
  padding: 0 var(--spacing-lg);
}

.vision-nav-tabs {
  display: flex;
  max-width: 1500px;
  margin: 0 auto;
  gap: var(--spacing-xs);
}

.vision-nav-tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--color-text-secondary);
  text-decoration: none;
  border-bottom: 2px solid transparent;
  transition: all var(--transition-fast);
  font-size: 13px;
  font-weight: 600;
}

.vision-nav-tab:hover {
  color: var(--color-primary);
  background: var(--color-bg-secondary);
}

.vision-nav-tab.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
}

/* Content */
.vision-content {
  padding: var(--spacing-lg);
}

.vision-content-inner {
  max-width: 1500px;
  margin: 0 auto;
}

.vision-tab-content {
  display: none;
  animation: fadeIn var(--transition-normal);
}

.vision-tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Cards */
.vision-card {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

.vision-card h2 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 18px;
  font-weight: 700;
  color: var(--color-text-primary);
  line-height: 1.3;
}

.vision-card h3 {
  margin: var(--spacing-md) 0 var(--spacing-sm) 0;
  font-size: 15px;
  font-weight: 600;
  color: var(--color-text-primary);
  line-height: 1.3;
}

/* Buttons */
.vision-button {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.vision-button-primary {
  background: var(--color-primary);
  color: white;
}

.vision-button-primary:hover {
  background: var(--color-primary-hover);
}

.vision-button-secondary {
  background: var(--color-bg-tertiary);
  color: var(--color-text-primary);
  border-color: var(--color-border);
}

.vision-button-secondary:hover {
  background: var(--color-border);
}

.vision-button-large {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 16px;
}

/* Forms */
.vision-form-table {
  width: 100%;
  border-collapse: collapse;
}

.vision-form-table th {
  text-align: left;
  padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 0;
  font-weight: 600;
  color: var(--color-text-primary);
  vertical-align: top;
  width: 200px;
}

.vision-form-table td {
  padding: var(--spacing-md) 0;
}

.vision-form-table input,
.vision-form-table select,
.vision-form-table textarea {
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-family: var(--font-family);
  background: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-size: 14px;
  transition: all var(--transition-fast);
  width: 100%;
  box-sizing: border-box;
}

.vision-form-table input:focus,
.vision-form-table select:focus,
.vision-form-table textarea:focus {
  border-color: var(--color-primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
  background: var(--color-bg-primary);
}

/* Ensure all input types are covered */
input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="number"],
input[type="search"],
input[type="tel"],
input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"],
input[type="week"],
select,
textarea {
  background: var(--color-bg-primary) !important;
  color: var(--color-text-primary) !important;
  border: 1px solid var(--color-border) !important;
  font-family: var(--font-family);
  font-size: 14px;
  transition: all var(--transition-fast);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="date"]:focus,
input[type="time"]:focus,
input[type="datetime-local"]:focus,
input[type="month"]:focus,
input[type="week"]:focus,
select:focus,
textarea:focus {
  border-color: var(--color-primary) !important;
  outline: none;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

/* Status indicators */
.vision-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.vision-status-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.vision-status-label {
  font-size: 12px;
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.vision-status-value {
  font-weight: 600;
  color: var(--color-text-primary);
}

.vision-status-active {
  color: var(--color-success);
}

/* ========================================
 * Utility Components
 * ======================================== */

/* Tooltips */
.vision-tooltip {
    position: absolute;
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10000;
    display: none;
    pointer-events: none;
}

.vision-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border: 5px solid transparent;
    border-top-color: #333;
}

/* Copy to clipboard button */
.copy-to-clipboard {
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.copy-to-clipboard:hover {
    opacity: 1;
}

/* Collapsible sections */
.vision-collapsible-header {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-bottom: 0;
    transition: background-color 0.2s;
}

.vision-collapsible-header:hover {
    background: #e9ecef;
}

.vision-collapsible-header::after {
    content: '▼';
    font-size: 12px;
    transition: transform 0.2s;
}

.vision-collapsible-header.collapsed::after {
    transform: rotate(-90deg);
}

.vision-collapsible-content {
    border: 1px solid #dee2e6;
    border-top: none;
    border-radius: 0 0 4px 4px;
    padding: 16px;
    background: white;
}

/* Modal improvements */
.vision-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.vision-modal-content {
    background: white;
    border-radius: 8px;
    padding: 24px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.vision-modal-close {
    position: absolute;
    top: 12px;
    right: 12px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 4px;
    line-height: 1;
}

.vision-modal-close:hover {
    color: #333;
}

/* ========================================
 * Responsive Design
 * ======================================== */

/* Responsive design */
@media (max-width: 768px) {
  .vision-header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }
  
  .vision-nav-tabs {
    flex-wrap: wrap;
  }
  
  .vision-content {
    padding: var(--spacing-md);
  }
  
  .vision-card {
    padding: var(--spacing-md);
  }
  
  .vision-form-table th {
    width: auto;
  }
  
  .vision-status-grid {
    grid-template-columns: 1fr;
  }
}

/* Loading and animations */
.vision-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-border);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Progress bar */
.vision-progress-bar {
  width: 100%;
  height: 8px;
  background: var(--color-bg-tertiary);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.vision-progress-fill {
  height: 100%;
  background: var(--color-primary);
  transition: width var(--transition-normal);
}

/* Logs */
.vision-log-entry {
  display: grid;
  grid-template-columns: auto auto 1fr;
  gap: var(--spacing-md);
  padding: var(--spacing-sm);
  border-bottom: 1px solid var(--color-border);
  font-family: monospace;
  font-size: 13px;
}

.vision-log-time {
  color: var(--color-text-secondary);
}

.vision-log-level {
  font-weight: 600;
  text-transform: uppercase;
}

.vision-log-info .vision-log-level {
  color: var(--color-primary);
}

.vision-log-warning .vision-log-level {
  color: var(--color-warning);
}

.vision-log-error .vision-log-level {
  color: var(--color-error);
}

/* ========================================
 * Blog Writer Specific Styles
 * ======================================== */

/* Blog Writer color scheme enhancements */
:root {
  /* Blog Writer brand colors */
  --color-blog-writer: #2563eb;
  --color-blog-writer-hover: #1d4ed8;
  --color-blog-writer-light: #dbeafe;
  --color-ai-green: #10b981;
  --color-ai-orange: #f59e0b;
  --color-ai-purple: #8b5cf6;
}

/* Dashboard specific styles */
.vision-dashboard {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.vision-welcome-section {
  background: var(--color-welcome-bg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--color-blog-writer);
  color: var(--color-welcome-text);
}

.vision-welcome-section h2,
.vision-welcome-section h3 {
  color: var(--color-welcome-text);
}

.vision-welcome-section p {
  color: var(--color-welcome-text);
  opacity: 0.9;
}

.vision-dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.vision-full-width {
  grid-column: 1 / -1;
}

/* Setup workflow styles */
.vision-setup-card {
  /* TEMPORARILY HIDDEN - Code preserved for future use */
  display: none !important;
  border-left: 4px solid var(--color-warning);
  background: var(--color-warning-bg);
  box-shadow: var(--shadow-lg);
  color: var(--color-text-primary);
}

.vision-setup-card h2,
.vision-setup-card h3 {
  color: var(--color-text-primary);
}

.vision-setup-card p {
  color: var(--color-text-primary);
  opacity: 0.9;
}

.vision-workflow-step {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  transition: all var(--transition-normal);
}

.vision-workflow-step:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.vision-workflow-step.completed {
  background: var(--color-success-bg, var(--color-bg-secondary));
  border-color: var(--color-success);
  color: var(--color-text-primary);
}

.vision-step-number {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--color-blog-writer);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  flex-shrink: 0;
  box-shadow: var(--shadow-sm);
}

.vision-workflow-step.completed .vision-step-number {
  background: var(--color-success);
}

/* User management styles */
.vision-user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
}

.vision-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: 3px solid var(--color-blog-writer);
  box-shadow: var(--shadow-sm);
}

.vision-user-details strong {
  display: block;
  color: var(--color-text-primary);
  font-size: 16px;
  margin-bottom: var(--spacing-xs);
}

.vision-user-meta {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
}

.vision-user-stats {
  font-size: 13px;
  color: var(--color-blog-writer);
  font-weight: 600;
}

/* Stats grid styling */
.vision-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.vision-stat-item {
  text-align: center;
  padding: var(--spacing-md);
  background: linear-gradient(135deg, var(--color-bg-secondary) 0%, var(--color-bg-primary) 100%);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
  transition: all var(--transition-normal);
}

.vision-stat-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.vision-stat-number {
  display: block;
  font-size: 28px;
  font-weight: 700;
  color: var(--color-blog-writer);
  margin-bottom: var(--spacing-xs);
}

.vision-stat-label {
  display: block;
  font-size: 11px;
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.8px;
  font-weight: 600;
}

/* Quick actions styling */
.vision-action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: var(--spacing-sm);
}

.vision-action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--color-bg-secondary) 0%, var(--color-bg-primary) 100%);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
  color: var(--color-text-primary);
}

.vision-action-button:hover {
  background: var(--color-action-hover-bg);
  border-color: var(--color-blog-writer);
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  color: var(--color-blog-writer);
}

.vision-action-icon {
  font-size: 32px;
  margin-bottom: var(--spacing-xs);
}

.vision-action-text {
  font-size: 11px;
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: 0.8px;
  text-align: center;
  line-height: 1.2;
}

/* System status styling */
.vision-status-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.vision-status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
}

.vision-status-icon {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--color-text-secondary);
  position: relative;
}

.vision-status-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.vision-status-item.status-good {
  border-color: var(--color-success);
  background: var(--color-success-bg);
}

.vision-status-item.status-good .vision-status-icon {
  background: var(--color-success);
  color: var(--color-success);
}

.vision-status-item.status-warning {
  border-color: var(--color-warning);
  background: var(--color-warning-bg);
}

.vision-status-item.status-warning .vision-status-icon {
  background: var(--color-warning);
  color: var(--color-warning);
}

.vision-status-item.status-error {
  border-color: var(--color-error);
  background: var(--color-error-bg);
}

.vision-status-item.status-error .vision-status-icon {
  background: var(--color-error);
  color: var(--color-error);
}

.vision-status-text {
  font-weight: 500;
  color: var(--color-text-primary);
}

/* Page Keywords tab styles */
.vision-page-keywords {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.vision-extraction-controls {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.vision-pages-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.vision-pages-table th {
  background: var(--color-blog-writer);
  color: white;
  padding: var(--spacing-md);
  text-align: left;
  font-weight: 600;
  font-size: 14px;
}

.vision-pages-table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
  vertical-align: top;
}

.vision-pages-table tr:hover {
  background: var(--color-bg-secondary);
}

.vision-keyword-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  max-width: 300px;
}

.vision-keyword-tag {
  background: var(--color-blog-writer-light);
  color: var(--color-blog-writer);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
  font-size: 11px;
  font-weight: 500;
}

/* Article Builder styles */
.vision-article-builder {
  animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.vision-generation-workflow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.vision-workflow-card {
  background: var(--color-bg-primary);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  position: relative;
  transition: all var(--transition-normal);
}

.vision-workflow-card.active {
  border-color: var(--color-blog-writer);
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
}

.vision-workflow-card .step-badge {
  position: absolute;
  top: -12px;
  left: var(--spacing-lg);
  background: var(--color-blog-writer);
  color: white;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-lg);
  font-size: 12px;
  font-weight: 700;
}

/* Model Competition styles */
.vision-model-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.vision-model-card {
  background: var(--color-bg-secondary);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.vision-model-card:hover {
  border-color: var(--color-blog-writer);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.vision-model-card.selected {
  border-color: var(--color-blog-writer);
  background: var(--color-blog-writer-light);
  color: var(--color-blog-writer);
}

.vision-model-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
}

.vision-model-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.vision-model-description {
  font-size: 13px;
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-md);
}

.vision-model-cost {
  font-size: 12px;
  font-weight: 600;
  color: var(--color-ai-green);
}

/* Generated Articles styles */
.vision-articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.vision-article-card {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.vision-article-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-blog-writer) 0%, var(--color-ai-green) 100%);
}

.vision-article-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-blog-writer);
}

.vision-article-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
}

.vision-article-header h4 {
  margin: 0;
  color: var(--color-text-primary);
  flex: 1;
  margin-right: var(--spacing-sm);
  font-size: 16px;
  line-height: 1.4;
}

.vision-status-badge {
  padding: 4px var(--spacing-sm);
  border-radius: var(--radius-lg);
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.vision-status-badge.status-draft {
  background: var(--color-warning);
  color: white;
}

.vision-status-badge.status-published {
  background: var(--color-success);
  color: white;
}

.vision-article-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.meta-item {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.meta-item strong {
  color: var(--color-text-primary);
  font-weight: 600;
}

.vision-article-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

/* Form enhancements */
.vision-form-group {
  margin-bottom: var(--spacing-lg);
}

.vision-form-group label {
  display: block;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.vision-input,
.vision-select,
.vision-textarea {
  width: 100%;
  border: 2px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-family: var(--font-family);
  background: var(--color-bg-primary);
  color: var(--color-text-primary);
  transition: all var(--transition-fast);
  font-size: 14px;
  box-sizing: border-box;
}

.vision-input:focus,
.vision-select:focus,
.vision-textarea:focus {
  border-color: var(--color-blog-writer);
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  background: var(--color-bg-primary);
}

/* Vision form group inputs */
.vision-form-group .vision-input,
.vision-form-group .vision-select {
  width: 100%;
  padding: var(--spacing-sm);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-md);
  background: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-size: 14px;
  transition: all var(--transition-fast);
  box-sizing: border-box;
}

.vision-form-group .vision-input:focus,
.vision-form-group .vision-select:focus {
  border-color: var(--color-blog-writer);
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  background: var(--color-bg-primary);
}

.vision-form-help {
  font-size: 13px;
  color: var(--color-text-secondary);
  margin-top: var(--spacing-xs);
  line-height: 1.4;
}

.vision-form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

/* Button enhancements */
.vision-button {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.vision-button-primary {
  background: linear-gradient(135deg, var(--color-blog-writer) 0%, var(--color-blog-writer-hover) 100%);
  color: white;
  border-color: var(--color-blog-writer);
}

.vision-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.vision-button-secondary {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
  border-color: var(--color-border);
}

.vision-button-secondary:hover {
  background: var(--color-border);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.vision-button-small {
  padding: 6px var(--spacing-sm);
  font-size: 12px;
}

.vision-button-large {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 16px;
}

/* Loading states and animations */
.vision-loading {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: currentColor;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Slide in animations for cards */
.vision-card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Success state animations */
.vision-workflow-step.completed {
  animation: successPulse 0.5s ease-out;
}

@keyframes successPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

/* Button loading state */
.vision-button[disabled] {
  opacity: 0.7;
  cursor: not-allowed;
  pointer-events: none;
}

/* Smooth transitions for all interactive elements */
.vision-button,
.vision-nav-tab,
.vision-workflow-step,
.vision-card {
  transition: all var(--transition-normal);
}

.vision-button-primary {
  background: linear-gradient(135deg, var(--color-blog-writer) 0%, var(--color-blog-writer-hover) 100%);
  color: white;
  border-color: var(--color-blog-writer);
}

.vision-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.vision-button-secondary {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
  border-color: var(--color-border);
}

.vision-button-secondary:hover {
  background: var(--color-border);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.vision-button-small {
  padding: 6px var(--spacing-sm);
  font-size: 12px;
}

.vision-button-large {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 16px;
}

/* Card enhancements */
.vision-card {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.vision-card:hover {
  box-shadow: var(--shadow-md);
}

.vision-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

.vision-card-header h2,
.vision-card-header h3 {
  margin: 0;
  color: var(--color-text-primary);
}

.vision-controls {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

/* Empty state styling */
.vision-empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--color-text-secondary);
}

.vision-empty-state p {
  font-size: 16px;
  margin-bottom: var(--spacing-lg);
}

/* Activity and log styling */
.vision-activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.vision-activity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--color-border);
}

.vision-activity-item.info {
  border-left-color: var(--color-primary);
}

.vision-activity-item.warning {
  border-left-color: var(--color-warning);
}

.vision-activity-item.error {
  border-left-color: var(--color-error);
}

.vision-activity-content {
  flex: 1;
}

.vision-activity-message {
  font-weight: 500;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.vision-activity-time {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.vision-activity-level {
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
  background: var(--color-bg-tertiary);
  color: var(--color-text-secondary);
}

/* Responsive enhancements */
@media (max-width: 768px) {
  .vision-dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .vision-articles-grid {
    grid-template-columns: 1fr;
  }
  
  .vision-generation-workflow {
    grid-template-columns: 1fr;
  }
  
  .vision-model-grid {
    grid-template-columns: 1fr;
  }
  
  .vision-action-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .vision-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .vision-article-meta {
    grid-template-columns: 1fr;
  }
  
  .vision-form-row {
    grid-template-columns: 1fr;
  }
  
  .vision-extraction-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .vision-card-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  
  .vision-controls {
    justify-content: stretch;
  }
}

/* ========================================
 * COMPREHENSIVE COMPONENT OVERHAUL
 * All components with proper spacing and theming
 * ======================================== */

/* Welcome Section - Tight and Clean */
.vision-welcome-section {
  background: var(--color-welcome-bg);
  border-radius: var(--radius-md);
  padding: var(--spacing-md) var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  border: 1px solid var(--color-blog-writer);
  color: var(--color-welcome-text);
}

.vision-welcome-section h1 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 22px;
  font-weight: 700;
  color: var(--color-welcome-text);
  line-height: 1.2;
}

.vision-welcome-section .vision-subtitle {
  margin: 0;
  font-size: 14px;
  color: var(--color-welcome-text);
  opacity: 0.9;
  line-height: 1.4;
}

/* Modern Setup Card - Clean and Professional */
.vision-setup-card {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%) !important;
  border: 1px solid var(--color-border) !important;
  border-left: 3px solid var(--color-blog-writer) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--spacing-lg) !important;
  margin-bottom: var(--spacing-lg) !important;
  color: var(--color-text-primary) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  position: relative;
  overflow: hidden;
}

/* Dark mode override */
[data-theme="dark"] .vision-setup-card,
body.admin-color-midnight .vision-setup-card,
body.admin-color-ectoplasm .vision-setup-card {
  background: linear-gradient(135deg, #1e293b 0%, #1d2327 100%) !important;
  border-color: var(--color-border) !important;
  border-left-color: var(--color-blog-writer) !important;
}

.vision-setup-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  border-radius: 0 0 0 100px;
}

.vision-setup-card .vision-card-header {
  margin-bottom: var(--spacing-lg) !important;
  padding-bottom: var(--spacing-md) !important;
  border-bottom: 1px solid var(--color-border) !important;
  position: relative;
}

.vision-setup-card .vision-card-header h2 {
  margin: 0 0 var(--spacing-sm) 0 !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  color: var(--color-text-primary) !important;
  line-height: 1.3 !important;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.vision-setup-card .vision-card-header h2::before {
  content: '🚀';
  font-size: 18px;
}

.vision-setup-card .vision-card-header p {
  margin: 0 !important;
  font-size: 14px !important;
  color: var(--color-text-secondary) !important;
  line-height: 1.5 !important;
}

/* Modern Setup Workflow Steps */
.vision-setup-workflow {
  display: grid;
  gap: var(--spacing-md);
}

.vision-workflow-step {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.vision-workflow-step::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--color-border);
  transition: all var(--transition-normal);
}

.vision-workflow-step:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.vision-workflow-step:hover::before {
  background: var(--color-blog-writer);
  width: 6px;
}

.vision-workflow-step.completed {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, transparent 100%);
  border-color: rgba(34, 197, 94, 0.2);
}

.vision-workflow-step.completed::before {
  background: var(--color-success);
  width: 4px;
}

.vision-step-number {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--color-blog-writer) 0%, #1d4ed8 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 14px;
  flex-shrink: 0;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
  transition: all var(--transition-normal);
}

.vision-workflow-step.completed .vision-step-number {
  background: linear-gradient(135deg, var(--color-success) 0%, #16a34a 100%);
  box-shadow: 0 2px 6px rgba(34, 197, 94, 0.3);
}

.vision-workflow-step:hover .vision-step-number {
  transform: scale(1.1);
}

.vision-step-content {
  flex: 1;
  min-width: 0;
}

.vision-step-content h3 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  line-height: 1.3;
}

.vision-step-content p {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 14px;
  color: var(--color-text-secondary);
  line-height: 1.4;
}

/* Modern Button Styling */
.vision-workflow-step .vision-button {
  margin-top: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: 13px;
  font-weight: 600;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--color-blog-writer) 0%, #1d4ed8 100%);
  border: none;
  color: white;
  text-decoration: none;
  transition: all var(--transition-normal);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.vision-workflow-step .vision-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  background: linear-gradient(135deg, #1d4ed8 0%, var(--color-blog-writer) 100%);
}

.vision-workflow-step .vision-button:active {
  transform: translateY(0);
}

/* Completion State */
.vision-workflow-step.completed .vision-button {
  background: linear-gradient(135deg, var(--color-success) 0%, #16a34a 100%);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.2);
}

.vision-workflow-step.completed .vision-button:hover {
  box-shadow: 0 4px 8px rgba(34, 197, 94, 0.3);
}

/* Card Headers - Consistent Styling */
.vision-card-header {
  margin-bottom: var(--spacing-md) !important;
  padding-bottom: var(--spacing-sm) !important;
  border-bottom: 1px solid var(--color-border) !important;
}

.vision-card-header h3 {
  margin: 0 !important;
  font-size: 16px !important;
  font-weight: 700 !important;
  color: var(--color-text-primary) !important;
  line-height: 1.3 !important;
}

/* Card Content Areas */
.vision-card-content {
  padding: 0 !important;
}

/* Button Groups - Tight Spacing */
.vision-button-group {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
  flex-wrap: wrap;
}

/* Stats Grid - Compact */
.vision-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-sm);
}

.vision-stat-item {
  text-align: center;
  padding: var(--spacing-sm);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-border);
  transition: all var(--transition-fast);
}

.vision-stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.vision-stat-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: var(--color-blog-writer);
  margin-bottom: var(--spacing-xs);
  line-height: 1;
}

.vision-stat-label {
  display: block;
  font-size: 10px;
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
  line-height: 1.2;
}

/* Action Grid - Compact */
.vision-action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-xs);
}

.vision-action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  color: var(--color-text-primary);
}

.vision-action-button:hover {
  background: var(--color-action-hover-bg);
  border-color: var(--color-blog-writer);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
  color: var(--color-blog-writer);
}

.vision-action-icon {
  font-size: 24px;
  margin-bottom: var(--spacing-xs);
}

.vision-action-text {
  font-size: 10px;
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-align: center;
  line-height: 1.2;
}

/* User Info - Compact */
.vision-user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-border);
  margin-bottom: var(--spacing-sm);
}

.vision-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: 2px solid var(--color-blog-writer);
  box-shadow: var(--shadow-sm);
}

.vision-user-details strong {
  display: block;
  color: var(--color-text-primary);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  line-height: 1.3;
}

.vision-user-meta {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
  line-height: 1.3;
}

.vision-user-stats {
  font-size: 11px;
  color: var(--color-blog-writer);
  font-weight: 600;
  line-height: 1.3;
}

/* Status List - Compact */
.vision-status-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.vision-status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-border);
  font-size: 12px;
}

.vision-status-item.status-good {
  border-color: var(--color-success);
  background: var(--color-success-bg);
}

.vision-status-item.status-warning {
  border-color: var(--color-warning);
  background: var(--color-warning-bg);
}

.vision-status-item.status-error {
  border-color: var(--color-error);
  background: var(--color-error-bg);
}

.vision-status-icon {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-text-secondary);
}

.vision-status-item.status-good .vision-status-icon {
  background: var(--color-success);
}

.vision-status-item.status-warning .vision-status-icon {
  background: var(--color-warning);
}

.vision-status-item.status-error .vision-status-icon {
  background: var(--color-error);
}

.vision-status-text {
  font-weight: 500;
  color: var(--color-text-primary);
  line-height: 1.3;
}

/* Activity List - Compact */
.vision-activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.vision-activity-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--color-border);
  font-size: 12px;
}

.vision-activity-item.info {
  border-left-color: var(--color-primary);
}

.vision-activity-item.warning {
  border-left-color: var(--color-warning);
}

.vision-activity-item.error {
  border-left-color: var(--color-error);
}

.vision-activity-content {
  flex: 1;
}

.vision-activity-message {
  font-weight: 500;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1.3;
}

.vision-activity-time {
  font-size: 11px;
  color: var(--color-text-secondary);
  line-height: 1.3;
}

.vision-activity-level {
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
  background: var(--color-bg-tertiary);
  color: var(--color-text-secondary);
}

/* Empty State */
.vision-empty-state {
  text-align: center;
  padding: var(--spacing-lg);
  color: var(--color-text-secondary);
}

.vision-empty-state p {
  font-size: 14px;
  margin-bottom: var(--spacing-md);
  line-height: 1.4;
}
.vision-warning-card {
  background: linear-gradient(135deg, var(--color-warning-bg) 0%, var(--color-warning-bg-secondary) 100%);
  border: 1px solid var(--color-warning);
  border-left: 4px solid var(--color-warning);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  color: var(--color-text-primary);
}

.vision-warning-card h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--color-text-primary);
  font-weight: 600;
}

.vision-warning-card p {
  color: var(--color-text-primary);
  opacity: 0.9;
}

/* Print styles */
@media print {
  .vision-framework-wrapper {
    background: white;
    color: black;
  }
  
  .vision-header,
  .vision-navigation {
    display: none;
  }
  
  .vision-card {
    box-shadow: none;
    border: 1px solid var(--color-border);
  }
  
  .vision-button {
    display: none;
  }
}

/* User Management Modal Styles */
.vision-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  z-index: 999999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

/* API Provider Interface Styles */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* API Status Indicators */
.api-status-message {
  animation: slideInUp 0.3s ease;
  transition: all 0.3s ease;
}

.api-status-message:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Provider status indicators with better visibility */
.status-indicator {
  transition: all 0.3s ease;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
}

.status-indicator.connected {
  background: #28a745;
  box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3), 0 0 8px rgba(40, 167, 69, 0.6);
}

.status-indicator.error {
  background: #dc3545;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3), 0 0 8px rgba(220, 53, 69, 0.6);
}

.status-indicator.testing {
  background: #007cba;
  animation: pulse 1.5s infinite;
  box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.3), 0 0 8px rgba(0, 124, 186, 0.6);
}

/* Enhanced API provider cards */
.api-provider-section {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.api-provider-section:hover {
  transform: translateY(-2px);
}

/* Enhanced buttons with better loading states */
.test-api-btn, .test-all-apis {
  position: relative;
  overflow: hidden;
}

.test-api-btn:disabled, .test-all-apis:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.btn-loading {
  animation: spin 1s linear infinite;
}

/* API key input enhancements */
.api-key-input {
  transition: all 0.3s ease;
}

.api-key-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;
}

/* Provider badge styling */
.provider-badge {
  animation: fadeIn 0.5s ease;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .api-providers-grid {
    gap: var(--spacing-md);
  }
  
  .api-provider-section {
    padding: var(--spacing-sm);
  }
  
  .provider-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
  
  .vision-input-group {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .vision-input-group .vision-input {
    order: 1;
  }
  
  .vision-input-group .test-api-btn {
    order: 2;
    width: 100%;
  }
  
  .vision-input-group .toggle-visibility {
    order: 3;
    width: 100%;
  }
}

/* Dark mode specific enhancements */
[data-theme="dark"] .api-status-message.success {
  background: rgba(40, 167, 69, 0.2);
  color: #4ade80;
  border-left-color: #4ade80;
}

[data-theme="dark"] .api-status-message.error {
  background: rgba(220, 53, 69, 0.2);
  color: #f87171;
  border-left-color: #f87171;
}

[data-theme="dark"] .api-status-message.testing {
  background: rgba(0, 124, 186, 0.2);
  color: #60a5fa;
  border-left-color: #60a5fa;
}

/* Enhanced focus states for accessibility */
.test-api-btn:focus,
.test-all-apis:focus,
.toggle-visibility:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Success flash animation */
@keyframes successFlash {
  0% { background-color: rgba(40, 167, 69, 0.1); }
  50% { background-color: rgba(40, 167, 69, 0.3); }
  100% { background-color: rgba(40, 167, 69, 0.1); }
}

.api-provider-section.success-flash {
  animation: successFlash 1s ease;
}

.vision-modal-content {
  background: var(--color-bg-primary);
  border-radius: var(--radius-xl);
  max-width: 700px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.3s ease;
}

.vision-modal-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.vision-modal-header h3 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: 18px;
  font-weight: 700;
}

.vision-modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--color-text-secondary);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.vision-modal-close:hover {
  background: var(--color-border);
  color: var(--color-text-primary);
}

.vision-modal-body {
  padding: var(--spacing-lg);
}

.vision-user-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.vision-option-card {
  background: var(--color-bg-secondary);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
}

.vision-option-card:hover {
  border-color: var(--color-blog-writer);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.vision-option-card h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--color-text-primary);
  font-size: 16px;
  font-weight: 600;
}

.vision-option-card p {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--color-text-secondary);
  font-size: 14px;
  line-height: 1.4;
}

.warning-text {
  color: var(--color-warning) !important;
  font-weight: 500 !important;
  background: var(--color-warning-bg);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--color-warning);
}

.vision-divider {
  text-align: center;
  color: var(--color-text-secondary);
  font-weight: 600;
  position: relative;
  margin: var(--spacing-md) 0;
}

.vision-divider::before,
.vision-divider::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 40%;
  height: 1px;
  background: var(--color-border);
}

.vision-divider::before {
  left: 0;
}

.vision-divider::after {
  right: 0;
}

.vision-form-group {
  margin-bottom: var(--spacing-md);
}

.vision-form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  color: var(--color-text-primary);
  font-weight: 500;
  font-size: 14px;
}

.vision-form-group .vision-input,
.vision-form-group .vision-select {
  width: 100%;
  padding: var(--spacing-sm);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-md);
  background: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-size: 14px;
  transition: all var(--transition-fast);
}

.vision-form-group .vision-input:focus,
.vision-form-group .vision-select:focus {
  border-color: var(--color-blog-writer);
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.vision-option-card .vision-button {
  margin-top: var(--spacing-sm);
  width: 100%;
  justify-content: center;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive modal */
@media (max-width: 768px) {
  .vision-modal-content {
    width: 95%;
    max-height: 90vh;
  }
  
  .vision-modal-header,
  .vision-modal-body {
    padding: var(--spacing-md);
  }
  
  .vision-user-options {
    gap: var(--spacing-md);
  }
}

/* Dark mode specific border fixes */
body.admin-color-midnight .vision-framework-wrapper .vision-welcome-section,
body.admin-color-ectoplasm .vision-framework-wrapper .vision-welcome-section {
  border-color: var(--color-border-dark);
}

body.admin-color-midnight .vision-framework-wrapper .vision-status-item,
body.admin-color-ectoplasm .vision-framework-wrapper .vision-status-item {
  border-color: var(--color-border-dark);
}

body.admin-color-midnight .vision-framework-wrapper .vision-status-item.status-good,
body.admin-color-ectoplasm .vision-framework-wrapper .vision-status-item.status-good {
  border-color: var(--color-border-dark);
}

body.admin-color-midnight .vision-framework-wrapper .vision-status-item.status-warning,
body.admin-color-ectoplasm .vision-framework-wrapper .vision-status-item.status-warning {
  border-color: var(--color-border-dark);
}

body.admin-color-midnight .vision-framework-wrapper .vision-status-item.status-error,
body.admin-color-ectoplasm .vision-framework-wrapper .vision-status-item.status-error {
  border-color: var(--color-border-dark);
}

@media (prefers-color-scheme: dark) {
  .vision-framework-wrapper .vision-welcome-section {
    border-color: var(--color-border-dark);
  }
  
  .vision-framework-wrapper .vision-status-item {
    border-color: var(--color-border-dark);
  }
  
  .vision-framework-wrapper .vision-status-item.status-good,
  .vision-framework-wrapper .vision-status-item.status-warning,
  .vision-framework-wrapper .vision-status-item.status-error {
    border-color: var(--color-border-dark);
  }
}

[data-theme="dark"] .vision-framework-wrapper .vision-welcome-section {
  border-color: var(--color-border-dark);
}

[data-theme="dark"] .vision-framework-wrapper .vision-status-item {
  border-color: var(--color-border-dark);
}

[data-theme="dark"] .vision-framework-wrapper .vision-status-item.status-good,
[data-theme="dark"] .vision-framework-wrapper .vision-status-item.status-warning,
[data-theme="dark"] .vision-framework-wrapper .vision-status-item.status-error {
  border-color: var(--color-border-dark);
}

/* Dark mode input field fixes - proper conditional application */
body.admin-color-midnight .vision-framework-wrapper input,
body.admin-color-midnight .vision-framework-wrapper select,
body.admin-color-midnight .vision-framework-wrapper textarea,
body.admin-color-midnight .vision-framework-wrapper .vision-input,
body.admin-color-midnight .vision-framework-wrapper .vision-select,
body.admin-color-midnight .vision-framework-wrapper .vision-textarea,
body.admin-color-midnight .vision-framework-wrapper .api-key-input,
body.admin-color-midnight .vision-framework-wrapper .vision-form-table input,
body.admin-color-midnight .vision-framework-wrapper .vision-form-table select,
body.admin-color-midnight .vision-framework-wrapper .vision-form-table textarea,
body.admin-color-ectoplasm .vision-framework-wrapper input,
body.admin-color-ectoplasm .vision-framework-wrapper select,
body.admin-color-ectoplasm .vision-framework-wrapper textarea,
body.admin-color-ectoplasm .vision-framework-wrapper .vision-input,
body.admin-color-ectoplasm .vision-framework-wrapper .vision-select,
body.admin-color-ectoplasm .vision-framework-wrapper .vision-textarea,
body.admin-color-ectoplasm .vision-framework-wrapper .api-key-input,
body.admin-color-ectoplasm .vision-framework-wrapper .vision-form-table input,
body.admin-color-ectoplasm .vision-framework-wrapper .vision-form-table select,
body.admin-color-ectoplasm .vision-framework-wrapper .vision-form-table textarea {
  background: var(--color-bg-secondary) !important;
  color: var(--color-text-primary) !important;
  border-color: var(--color-border) !important;
}

@media (prefers-color-scheme: dark) {
  .vision-framework-wrapper input,
  .vision-framework-wrapper select,
  .vision-framework-wrapper textarea,
  .vision-framework-wrapper .vision-input,
  .vision-framework-wrapper .vision-select,
  .vision-framework-wrapper .vision-textarea,
  .vision-framework-wrapper .api-key-input,
  .vision-framework-wrapper .vision-form-table input,
  .vision-framework-wrapper .vision-form-table select,
  .vision-framework-wrapper .vision-form-table textarea {
    background: var(--color-bg-secondary) !important;
    color: var(--color-text-primary) !important;
    border-color: var(--color-border) !important;
  }
}

[data-theme="dark"] .vision-framework-wrapper input,
[data-theme="dark"] .vision-framework-wrapper select,
[data-theme="dark"] .vision-framework-wrapper textarea,
[data-theme="dark"] .vision-framework-wrapper .vision-input,
[data-theme="dark"] .vision-framework-wrapper .vision-select,
[data-theme="dark"] .vision-framework-wrapper .vision-textarea,
[data-theme="dark"] .vision-framework-wrapper .api-key-input,
[data-theme="dark"] .vision-framework-wrapper .vision-form-table input,
[data-theme="dark"] .vision-framework-wrapper .vision-form-table select,
[data-theme="dark"] .vision-framework-wrapper .vision-form-table textarea {
  background: var(--color-bg-secondary) !important;
  color: var(--color-text-primary) !important;
  border-color: var(--color-border) !important;
}

/* Hide Vision Framework branding mentions while preserving code */
.vision-about-section p:first-of-type {
  display: none;
}

.vision-about-section h4:first-of-type,
.vision-about-section h4:first-of-type + ul {
  display: none;
}

/* ========================================
 * COMPREHENSIVE INPUT FIELD AUDIT FIX
 * Ensuring all input types work correctly in light and dark modes
 * ======================================== */

/* Base input styling - applies to ALL inputs within the plugin wrapper */
.vision-framework-wrapper input[type="text"],
.vision-framework-wrapper input[type="email"],
.vision-framework-wrapper input[type="url"],
.vision-framework-wrapper input[type="password"],
.vision-framework-wrapper input[type="number"],
.vision-framework-wrapper input[type="search"],
.vision-framework-wrapper input[type="tel"],
.vision-framework-wrapper input[type="date"],
.vision-framework-wrapper input[type="time"],
.vision-framework-wrapper input[type="datetime-local"],
.vision-framework-wrapper input[type="month"],
.vision-framework-wrapper input[type="week"],
.vision-framework-wrapper input[type="color"],
.vision-framework-wrapper input[type="file"],
.vision-framework-wrapper select,
.vision-framework-wrapper textarea,
.vision-framework-wrapper .wp-editor-area {
  background: var(--color-bg-primary) !important;
  color: var(--color-text-primary) !important;
  border-color: var(--color-border) !important;
  font-family: var(--font-family) !important;
}

/* Focus states for all inputs */
.vision-framework-wrapper input[type="text"]:focus,
.vision-framework-wrapper input[type="email"]:focus,
.vision-framework-wrapper input[type="url"]:focus,
.vision-framework-wrapper input[type="password"]:focus,
.vision-framework-wrapper input[type="number"]:focus,
.vision-framework-wrapper input[type="search"]:focus,
.vision-framework-wrapper input[type="tel"]:focus,
.vision-framework-wrapper input[type="date"]:focus,
.vision-framework-wrapper input[type="time"]:focus,
.vision-framework-wrapper input[type="datetime-local"]:focus,
.vision-framework-wrapper input[type="month"]:focus,
.vision-framework-wrapper input[type="week"]:focus,
.vision-framework-wrapper input[type="color"]:focus,
.vision-framework-wrapper input[type="file"]:focus,
.vision-framework-wrapper select:focus,
.vision-framework-wrapper textarea:focus,
.vision-framework-wrapper .wp-editor-area:focus {
  background: var(--color-bg-primary) !important;
  color: var(--color-text-primary) !important;
  border-color: var(--color-primary) !important;
}

/* WordPress editor specific fixes */
.vision-framework-wrapper .wp-editor-wrap,
.vision-framework-wrapper .wp-editor-container,
.vision-framework-wrapper .mce-container {
  background: var(--color-bg-primary) !important;
  color: var(--color-text-primary) !important;
  border-color: var(--color-border) !important;
}

/* API key inputs and specialized form elements */
.vision-framework-wrapper .api-key-input,
.vision-framework-wrapper .vision-api-input,
.vision-framework-wrapper .form-control,
.vision-framework-wrapper .regular-text {
  background: var(--color-bg-primary) !important;
  color: var(--color-text-primary) !important;
  border-color: var(--color-border) !important;
}

/* Placeholder text styling */
.vision-framework-wrapper input::placeholder,
.vision-framework-wrapper textarea::placeholder {
  color: var(--color-text-tertiary) !important;
  opacity: 0.7;
}

/* Disabled input styling */
.vision-framework-wrapper input:disabled,
.vision-framework-wrapper select:disabled,
.vision-framework-wrapper textarea:disabled {
  background: var(--color-bg-tertiary) !important;
  color: var(--color-text-tertiary) !important;
  opacity: 0.6;
}

/* Button inputs */
.vision-framework-wrapper input[type="button"],
.vision-framework-wrapper input[type="submit"],
.vision-framework-wrapper input[type="reset"] {
  background: var(--color-primary) !important;
  color: white !important;
  border-color: var(--color-primary) !important;
}

.vision-framework-wrapper input[type="button"]:hover,
.vision-framework-wrapper input[type="submit"]:hover,
.vision-framework-wrapper input[type="reset"]:hover {
  background: var(--color-primary-hover) !important;
}