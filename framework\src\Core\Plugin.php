<?php
/**
 * Core Plugin Framework Base Class
 * 
 * Base class for all plugins using the Vision Framework
 */

namespace VisionFramework\Core;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Plugin Base Class
 */
abstract class Plugin
{
    /**
     * Plugin instance
     * 
     * @var static
     */
    protected static $instance;
    
    /**
     * Plugin configuration
     * 
     * @var array
     */
    protected $config = [];
    
    /**
     * Admin UI instance
     * 
     * @var AdminUI
     */
    protected $adminUI;
    
    /**
     * API handler instance
     * 
     * @var ApiHandler
     */
    protected $apiHandler;
    
    /**
     * Constructor
     */
    protected function __construct()
    {
        $this->initConfig();
        $this->initHooks();
        $this->initComponents();
    }
    
    /**
     * Get plugin instance (singleton)
     * 
     * @return static
     */
    public static function instance()
    {
        if (!isset(static::$instance)) {
            static::$instance = new static();
        }
        
        return static::$instance;
    }
    
    /**
     * Initialize plugin configuration
     * Must be implemented by child classes
     */
    abstract protected function initConfig();
    
    /**
     * Initialize WordPress hooks
     */
    protected function initHooks()
    {
        add_action('init', [$this, 'init']);
        add_action('admin_init', [$this, 'adminInit']);
        add_action('admin_menu', [$this, 'adminMenu']);
        add_action('admin_enqueue_scripts', [$this, 'adminEnqueueScripts']);
        add_action('wp_ajax_' . $this->getConfig('text_domain') . '_ajax', [$this, 'handleAjax']);
    }
    
    /**
     * Initialize plugin components
     */
    protected function initComponents()
    {
        $this->adminUI = new AdminUI($this->config);
        $this->apiHandler = new ApiHandler($this->config);
    }
    
    /**
     * Plugin initialization
     */
    public function init()
    {
        // Hook for child classes to override
        do_action($this->getConfig('text_domain') . '_init', $this);
    }
    
    /**
     * Admin initialization
     */
    public function adminInit()
    {
        // Initialize admin components
        $this->adminUI->init();
        
        // Hook for child classes to override
        do_action($this->getConfig('text_domain') . '_admin_init', $this);
    }
    
    /**
     * Add admin menu
     */
    public function adminMenu()
    {
        $this->adminUI->addMenu();
    }
    
    /**
     * Enqueue admin scripts and styles
     * 
     * @param string $hook The current admin page hook
     */
    public function adminEnqueueScripts($hook)
    {
        // Only load on our plugin pages
        if (!$this->adminUI->isPluginPage($hook)) {
            return;
        }
        
        $this->adminUI->enqueueAssets();
    }
    
    /**
     * Handle AJAX requests
     */
    public function handleAjax()
    {
        $this->apiHandler->handleRequest();
    }
    
    /**
     * Get configuration value
     * 
     * @param string $key Configuration key
     * @param mixed $default Default value if key doesn't exist
     * @return mixed
     */
    public function getConfig($key, $default = null)
    {
        return isset($this->config[$key]) ? $this->config[$key] : $default;
    }
    
    /**
     * Set configuration value
     * 
     * @param string $key Configuration key
     * @param mixed $value Configuration value
     */
    public function setConfig($key, $value)
    {
        $this->config[$key] = $value;
    }
    
    /**
     * Get admin UI instance
     * 
     * @return AdminUI
     */
    public function getAdminUI()
    {
        return $this->adminUI;
    }
    
    /**
     * Get API handler instance
     * 
     * @return ApiHandler
     */
    public function getApiHandler()
    {
        return $this->apiHandler;
    }
}