/*!
 * Vision Framework Admin JavaScript
 * Handles AJAX navigation, interactions, and UI enhancements
 */

(function($) {
    'use strict';

    // Framework object
    window.VisionFramework = window.VisionFramework || {};

    const Framework = {
        /**
         * Initialize the framework
         */
        init: function() {
            console.log('🚀 Framework.init() called');

            // Set up global error handler
            this.setupGlobalErrorHandler();

            try {
                console.log('📎 Binding events...');
                this.bindEvents();
                console.log('🗂️ Initializing tab navigation...');
                this.initTabNavigation();
                console.log('🎨 Initializing theme toggle...');
                this.initThemeToggle();
                console.log('💡 Initializing tooltips...');
                this.initTooltips();
                console.log('📊 Initializing progress bars...');
                this.initProgressBars();

                console.log('✅ Framework initialization complete');
                // Trigger ready event
                $(document).trigger('vision:ready');
            } catch (error) {
                console.error('❌ Error during Framework initialization:', error);
                this.showNotice('Initialization error. Please refresh the page.', 'error');
            }
        },

        /**
         * Setup global error handler
         */
        setupGlobalErrorHandler: function() {
            const self = this;

            // Handle unhandled promise rejections
            window.addEventListener('unhandledrejection', function(event) {
                console.error('❌ Unhandled promise rejection:', event.reason);
                if (event.reason && event.reason.message) {
                    self.showNotice('An error occurred: ' + event.reason.message, 'error');
                }
            });

            // Handle general JavaScript errors
            window.addEventListener('error', function(event) {
                console.error('❌ JavaScript error:', event.error);
                // Only show user-facing errors for our plugin code
                if (event.filename && event.filename.includes('blogwriter')) {
                    self.showNotice('A plugin error occurred. Please check the console.', 'error');
                }
            });
        },

        /**
         * Bind global events
         */
        bindEvents: function() {
            const self = this;
            console.log('📎 Starting event binding...');

            try {
                // Essential events only - test basic functionality first
                console.log('📎 Binding essential events...');

                // AJAX form submissions
                $(document).on('submit', '.vision-ajax-form', function(e) {
                    self.handleAjaxForm.call(this, e);
                });

                // Button actions
                $(document).on('click', '[data-action]', function(e) {
                    self.handleButtonAction.call(this, e);
                });

                // Settings form
                $(document).on('submit', '.vision-settings-form', function(e) {
                    self.handleSettingsForm.call(this, e);
                });

                // Log filters
                $(document).on('change', '.vision-log-level-filter', function(e) {
                    self.handleLogFilter.call(this, e);
                });

                // Theme mode changes
                $(document).on('change', '#theme_mode', function(e) {
                    self.handleThemeChange.call(this, e);
                });

                console.log('✅ Essential events bound successfully');

                // Test basic functionality
                console.log('🧪 Testing basic functionality...');
                console.log('- handleAjaxForm exists:', typeof self.handleAjaxForm === 'function');
                console.log('- handleButtonAction exists:', typeof self.handleButtonAction === 'function');
                console.log('- handleThemeChange exists:', typeof self.handleThemeChange === 'function');

                // Stage 1: Draft comparison events
                console.log('📎 Binding draft comparison events...');
                $(document).on('click', '.draft-card', function(e) {
                    self.handleDraftSelection.call(this, e);
                });
                $(document).on('click', '.draft-select-btn', function(e) {
                    self.handleDraftConfirmation.call(this, e);
                });
                $(document).on('click', '.rating-star', function(e) {
                    self.handleStarRating.call(this, e);
                });

                // Stage 2: Comparison controls
                console.log('📎 Binding comparison controls...');
                $(document).on('click', '[data-action="show-all-drafts"]', function(e) {
                    self.showAllDrafts.call(this, e);
                });
                $(document).on('click', '[data-action="hide-weak-drafts"]', function(e) {
                    self.hideWeakDrafts.call(this, e);
                });
                $(document).on('click', '[data-action="show-only-favorites"]', function(e) {
                    self.showOnlyFavorites.call(this, e);
                });

                // Stage 3: Metric toggles
                console.log('📎 Binding metric toggles...');
                $(document).on('change', '.metric-toggle input', function(e) {
                    self.handleMetricToggle.call(this, e);
                });

                // Stage 4: Image placeholder events
                console.log('📎 Binding image placeholder events...');
                $(document).on('click', '.generate-image-btn', function(e) {
                    self.handleImageGenerationClick.call(this, e);
                });
                $(document).on('click', '.select-image-btn', function(e) {
                    self.handleImageSelectionClick.call(this, e);
                });

                // Stage 5: Builder workflow navigation
                console.log('📎 Binding builder workflow navigation...');
                $(document).on('click', '[data-action="back-to-topics"]', function(e) {
                    self.goToTopicsStep.call(this, e);
                });
                $(document).on('click', '[data-action="back-to-generation"]', function(e) {
                    self.goToGenerationStep.call(this, e);
                });
                $(document).on('click', '[data-action="proceed-to-preview"]', function(e) {
                    self.goToPreviewStep.call(this, e);
                });

                // Stage 6: Additional utility events
                console.log('📎 Binding additional utility events...');

                // Modal events
                $(document).on('click', '.vision-modal-close', function(e) {
                    e.preventDefault();
                    $(this).closest('.vision-modal').fadeOut();
                });

                // Tooltip events
                $(document).on('mouseenter', '[data-tooltip]', function() {
                    self.showTooltip($(this));
                });
                $(document).on('mouseleave', '[data-tooltip]', function() {
                    self.hideTooltip();
                });

                // Copy to clipboard events
                $(document).on('click', '.copy-to-clipboard', function(e) {
                    e.preventDefault();
                    self.copyToClipboard($(this));
                });

                // Collapsible sections
                $(document).on('click', '.vision-collapsible-header', function(e) {
                    e.preventDefault();
                    $(this).next('.vision-collapsible-content').slideToggle();
                    $(this).toggleClass('collapsed');
                });

                console.log('✅ All event binding complete');

                // Debug: Log all bound events
                console.log('🧪 Event binding summary:');
                console.log('- Essential events: ✅');
                console.log('- Draft comparison: ✅');
                console.log('- Comparison controls: ✅');
                console.log('- Metric toggles: ✅');
                console.log('- Image placeholders: ✅');
                console.log('- Workflow navigation: ✅');
                console.log('- Utility events: ✅');

                // Test that critical methods exist
                const criticalMethods = [
                    'handleAjaxForm', 'handleButtonAction', 'handleThemeChange',
                    'handleDraftSelection', 'showAllDrafts', 'handleMetricToggle'
                ];

                const missingMethods = criticalMethods.filter(method => typeof self[method] !== 'function');
                if (missingMethods.length > 0) {
                    console.error('❌ Missing critical methods:', missingMethods);
                } else {
                    console.log('✅ All critical methods available');
                }
            } catch (error) {
                console.error('❌ Error during event binding:', error);
            }
        },

        /**
         * Initialize tab navigation with AJAX
         */
        initTabNavigation: function() {
            const self = this;
            
            $(document).on('click', '.vision-nav-tab', function(e) {
                e.preventDefault();
                
                const $tab = $(this);
                const tabId = $tab.data('tab');
                
                if ($tab.hasClass('active')) {
                    return;
                }
                
                self.switchTab(tabId);
            });
            
            // Handle browser back/forward
            window.addEventListener('popstate', function(e) {
                if (e.state && e.state.tab) {
                    self.switchTab(e.state.tab, false);
                }
            });
            
            // Set initial state
            const currentTab = $('.vision-nav-tab.active').data('tab') || 'dashboard';
            history.replaceState({tab: currentTab}, '', window.location.href);
        },

        /**
         * Switch between tabs
         */
        switchTab: function(tabId, updateHistory = true) {
            const $tabs = $('.vision-nav-tab');
            const $contents = $('.vision-tab-content');
            
            // Update active states
            $tabs.removeClass('active');
            $contents.removeClass('active');
            
            const $activeTab = $tabs.filter(`[data-tab="${tabId}"]`);
            const $activeContent = $(`#vision-tab-${tabId}`);
            
            if ($activeTab.length && $activeContent.length) {
                $activeTab.addClass('active');
                $activeContent.addClass('active');
                
                // Update URL
                if (updateHistory) {
                    const url = new URL(window.location);
                    url.searchParams.set('tab', tabId);
                    history.pushState({tab: tabId}, '', url);
                }
                
                // Load tab data via AJAX
                this.loadTabData(tabId);
                
                // Trigger tab change event
                $(document).trigger('vision:tab-changed', [tabId]);
            }
        },

        /**
         * Load tab data via AJAX
         */
        loadTabData: function(tabId) {
            this.ajaxRequest('switch_tab', {tab: tabId})
                .done(function(response) {
                    if (response.success && response.data) {
                        $(document).trigger('vision:tab-data-loaded', [tabId, response.data]);
                    }
                });
        },

        /**
         * Initialize theme toggle functionality
         */
        initThemeToggle: function() {
            const self = this;
            
            // Handle theme toggle button click
            $(document).on('click', '#vision-theme-toggle', function(e) {
                e.preventDefault();
                self.toggleTheme();
            });
            
            // Initialize theme from localStorage or system preference
            const savedTheme = localStorage.getItem('vision-theme');
            if (savedTheme) {
                this.setTheme(savedTheme);
            } else {
                // Auto-detect system theme preference
                const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                this.setTheme(prefersDark ? 'dark' : 'light');
            }
            
            // Listen for system theme changes
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', (e) => {
                // Only auto-switch if no manual preference is saved
                if (!localStorage.getItem('vision-theme')) {
                    this.setTheme(e.matches ? 'dark' : 'light');
                }
            });
        },

        /**
         * Toggle between light and dark themes
         */
        toggleTheme: function() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            this.setTheme(newTheme);
        },

        /**
         * Set theme
         */
        setTheme: function(theme) {
            // Update document attribute for CSS targeting
            document.documentElement.setAttribute('data-theme', theme);
            
            // Save preference
            localStorage.setItem('vision-theme', theme);
            
            // Update theme toggle button visibility
            this.updateThemeToggleVisual(theme);
            
            // Trigger theme change event
            $(document).trigger('vision:theme-changed', [theme]);
        },

        /**
         * Update theme toggle button visual state
         */
        updateThemeToggleVisual: function(theme) {
            const $toggle = $('#vision-theme-toggle');
            if ($toggle.length) {
                $toggle.attr('data-theme', theme);
                $toggle.attr('title', theme === 'light' ? 'Switch to Dark Mode' : 'Switch to Light Mode');
            }
        },

        /**
         * Initialize tooltips
         */
        initTooltips: function() {
            // Simple tooltip implementation
            $(document).on('mouseenter', '[data-tooltip]', function() {
                const $this = $(this);
                const text = $this.data('tooltip');
                
                if (text && !$this.data('tooltip-shown')) {
                    const $tooltip = $('<div class="vision-tooltip">' + text + '</div>');
                    $('body').append($tooltip);
                    
                    const offset = $this.offset();
                    $tooltip.css({
                        position: 'absolute',
                        top: offset.top - $tooltip.outerHeight() - 5,
                        left: offset.left + ($this.outerWidth() / 2) - ($tooltip.outerWidth() / 2),
                        zIndex: 9999
                    });
                    
                    $this.data('tooltip-shown', true);
                }
            });
            
            $(document).on('mouseleave', '[data-tooltip]', function() {
                $('.vision-tooltip').remove();
                $(this).data('tooltip-shown', false);
            });
        },

        /**
         * Initialize progress bars
         */
        initProgressBars: function() {
            $('.vision-progress-bar').each(function() {
                const $bar = $(this);
                const $fill = $bar.find('.vision-progress-fill');
                const target = $bar.data('progress') || 0;
                
                setTimeout(() => {
                    $fill.css('width', target + '%');
                }, 100);
            });
        },

        /**
         * Handle AJAX form submissions
         */
        handleAjaxForm: function(e) {
            e.preventDefault();
            
            const $form = $(this);
            const $submit = $form.find('[type="submit"]');
            const originalText = $submit.text();
            
            // Disable form
            $form.addClass('vision-loading');
            $submit.prop('disabled', true).text(visionFramework.strings.loading);
            
            // Prepare form data
            const formData = new FormData(this);
            formData.append('action', visionFramework.action);
            formData.append('nonce', visionFramework.nonce);
            
            // Submit via AJAX
            $.ajax({
                url: visionFramework.ajaxUrl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        Framework.showNotice(response.data.message || visionFramework.strings.success, 'success');
                        $(document).trigger('vision:form-success', [$form, response.data]);
                    } else {
                        Framework.showNotice(response.data || visionFramework.strings.error, 'error');
                    }
                },
                error: function() {
                    Framework.showNotice(visionFramework.strings.error, 'error');
                },
                complete: function() {
                    $form.removeClass('vision-loading');
                    $submit.prop('disabled', false).text(originalText);
                }
            });
        },

        /**
         * Handle button actions
         */
        handleButtonAction: function(e) {
            e.preventDefault();
            
            const $button = $(this);
            const action = $button.data('action');
            
            // Only collect non-conflicting data attributes
            const data = {};
            $.each($button.data(), function(key, value) {
                // Skip action and endpoint to avoid conflicts
                if (key !== 'action' && key !== 'endpoint') {
                    data[key] = value;
                }
            });
            
            if (!action) return;
            
            // Confirm if needed
            if ($button.data('confirm')) {
                if (!confirm($button.data('confirm'))) {
                    return;
                }
            }
            
            // Show loading state
            const originalHtml = $button.html();
            $button.prop('disabled', true).html('<span class="vision-loading"></span> ' + visionFramework.strings.loading);
            
            Framework.ajaxRequest(action, data)
                .done(function(response) {
                    if (response.success) {
                        Framework.showNotice(response.data.message || visionFramework.strings.success, 'success');
                        $(document).trigger('vision:action-success', [action, response.data]);
                        
                        // Handle specific actions
                        Framework.handleActionResponse(action, response.data, $button);
                    } else {
                        Framework.showNotice(response.data || visionFramework.strings.error, 'error');
                    }
                })
                .fail(function() {
                    Framework.showNotice(visionFramework.strings.error, 'error');
                })
                .always(function() {
                    $button.prop('disabled', false).html(originalHtml);
                });
        },

        /**
         * Handle settings form submission
         */
        handleSettingsForm: function(e) {
            e.preventDefault();
            
            const $form = $(this);
            const formData = $form.serializeArray();
            const data = {};
            
            // Convert form data to object
            $.each(formData, function() {
                if (this.name.endsWith('[]')) {
                    const key = this.name.slice(0, -2);
                    if (!data[key]) data[key] = [];
                    data[key].push(this.value);
                } else {
                    data[this.name] = this.value;
                }
            });
            
            // Add checkboxes that aren't checked
            $form.find('input[type="checkbox"]').each(function() {
                if (!this.checked && !data.hasOwnProperty(this.name)) {
                    data[this.name] = '0';
                }
            });
            
            const $submit = $form.find('[type="submit"]');
            const originalText = $submit.text();
            
            $submit.prop('disabled', true).text(visionFramework.strings.loading);
            
            Framework.ajaxRequest('save_settings', data)
                .done(function(response) {
                    if (response.success) {
                        Framework.showNotice(response.data.message, 'success');
                        $(document).trigger('vision:settings-saved', [response.data.settings]);
                    } else {
                        Framework.showNotice(response.data || visionFramework.strings.error, 'error');
                    }
                })
                .fail(function() {
                    Framework.showNotice(visionFramework.strings.error, 'error');
                })
                .always(function() {
                    $submit.prop('disabled', false).text(originalText);
                });
        },

        /**
         * Handle log level filtering
         */
        handleLogFilter: function() {
            const level = $(this).val();
            const $logs = $('.vision-log-entry');
            
            if (level === '') {
                $logs.show();
            } else {
                $logs.hide();
                $(`.vision-log-${level}`).show();
            }
        },

        /**
         * Handle theme mode changes
         */
        handleThemeChange: function() {
            const mode = $(this).val();
            
            switch (mode) {
                case 'light':
                    Framework.setTheme('light');
                    break;
                case 'dark':
                    Framework.setTheme('dark');
                    break;
                case 'auto':
                default:
                    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                    Framework.setTheme(prefersDark ? 'dark' : 'light');
                    break;
            }
        },

        /**
         * Handle specific action responses
         */
        handleActionResponse: function(action, data, $button) {
            switch (action) {
                case 'start_scan':
                    this.handleScanResponse(data, $button);
                    break;
                case 'refresh_data':
                    this.handleRefreshResponse(data);
                    break;
                case 'create_user':
                case 'repair_database':
                    // Reload the page after user creation or database repair
                    if (data.reload_page) {
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    }
                    break;
                case 'change_assigned_user':
                    // Handle nested response structure
                    const modalData = data.data || data;
                    if (modalData.show_modal) {
                        this.showUserManagementModal(modalData);
                    }
                    break;
                case 'clear_cache':
                case 'clear_logs':
                    // Reload current tab to show changes
                    const currentTab = $('.vision-nav-tab.active').data('tab');
                    if (currentTab) {
                        this.loadTabData(currentTab);
                    }
                    break;
                case 'refresh_page_balance':
                    this.handlePageBalanceRefresh(data);
                    break;
                case 'generate_for_page':
                    this.handleGenerateForPage(data, $button);
                    break;
                case 'start-generation':
                    this.handleStartGeneration(data, $button);
                    break;
                case 'generate_placeholder_image':
                    this.handleImageGeneration(data, $button);
                    break;
                case 'update_placeholder_selection':
                    this.handlePlaceholderSelection(data, $button);
                    break;
                case 'refresh_models':
                    this.handleModelsRefresh(data);
                    break;
            }
        },

        /**
         * Handle scan response with progress
         */
        handleScanResponse: function(data, $button) {
            if (data.results) {
                // Show scan results
                const $results = $('.vision-scan-results');
                if ($results.length) {
                    $results.html(this.formatScanResults(data.results));
                }
            }
        },

        /**
         * Handle refresh response
         */
        handleRefreshResponse: function(data) {
            if (data.stats) {
                // Update dashboard stats
                $('.vision-status-value').each(function() {
                    const $this = $(this);
                    const key = $this.data('stat');
                    if (key && data.stats[key]) {
                        $this.text(data.stats[key]);
                    }
                });
            }
        },

        /**
         * Format scan results for display
         */
        formatScanResults: function(results) {
            return `
                <h3>Scan Complete</h3>
                <div class="vision-scan-summary">
                    <div class="vision-scan-item ${results.issues_found > 0 ? 'vision-scan-warning' : 'vision-scan-success'}">
                        <span class="dashicons dashicons-${results.issues_found > 0 ? 'warning' : 'yes-alt'}"></span>
                        ${results.issues_found > 0 ? results.issues_found + ' issues found' : 'No issues found'}
                    </div>
                    <div class="vision-scan-item vision-scan-info">
                        <span class="dashicons dashicons-clock"></span>
                        Completed: ${results.completed_at}
                    </div>
                    <div class="vision-scan-item vision-scan-info">
                        <span class="dashicons dashicons-search"></span>
                        Files scanned: ${results.total_checked}
                    </div>
                </div>
            `;
        },

        /**
         * Handle page balance refresh response
         */
        handlePageBalanceRefresh: function(data) {
            // Update balance summary metrics
            if (data.balance_summary) {
                const summary = data.balance_summary;
                $('.balance-metric .metric-value').each(function() {
                    const $metric = $(this);
                    const $parent = $metric.closest('.balance-metric');
                    const label = $parent.find('.metric-label').text().toLowerCase();

                    if (label.includes('content balance')) {
                        $metric.text(summary.content_balance + '%')
                               .toggleClass('warning', summary.content_balance < 50);
                    } else if (label.includes('pages covered')) {
                        $metric.text(summary.pages_covered);
                    } else if (label.includes('needs attention')) {
                        $metric.text(summary.needs_attention)
                               .toggleClass('warning', summary.needs_attention > 0);
                    }
                });
            }

            // Update category breakdown
            if (data.balance_summary && data.balance_summary.categories) {
                this.updateCategoryBreakdown(data.balance_summary.categories);
            }

            // Update insights
            if (data.balance_insights) {
                this.updateBalanceInsights(data.balance_insights);
            }

            // Update pages needing attention
            if (data.pages_needing_attention) {
                this.updatePagesNeedingAttention(data.pages_needing_attention);
            }
        },

        /**
         * Handle generate for page response
         */
        handleGenerateForPage: function(data, $button) {
            if (data.article_id) {
                // Update the page card to show it now has content
                const $pageCard = $button.closest('.page-attention-card');
                if ($pageCard.length) {
                    $pageCard.addClass('has-content');

                    // Update article count display
                    const $articleCount = $pageCard.find('.page-articles');
                    if ($articleCount.length) {
                        const currentText = $articleCount.text();
                        const match = currentText.match(/(\d+)\/(\d+)/);
                        if (match) {
                            const newCount = parseInt(match[1]) + 1;
                            const target = match[2];
                            $articleCount.text(`${newCount}/${target} articles`);
                        }
                    }
                }

                // Show success with article details
                this.showNotice(`Article "${data.title}" generated successfully for ${data.page_title}!`, 'success');

                // Optionally refresh the dashboard after a delay
                setTimeout(() => {
                    this.loadTabData('dashboard');
                }, 2000);
            }
        },

        /**
         * Handle article generation start
         */
        handleStartGeneration: function(data, $button) {
            // Show the comparison loading interface
            const $comparisonContainer = $('#drafts-comparison');
            const $loadingDiv = $comparisonContainer.find('.comparison-loading');
            const $gridDiv = $comparisonContainer.find('.comparison-grid');

            $loadingDiv.show();
            $gridDiv.hide();

            // Start the multi-model generation process
            this.startMultiModelGeneration();
        },

        /**
         * Handle image generation response
         */
        handleImageGeneration: function(data, $button) {
            if (data.image && data.placeholder_id) {
                // Find the placeholder container
                const $placeholder = $(`.image-placeholder[data-placeholder-id="${data.placeholder_id}"]`);
                if ($placeholder.length) {
                    // Update the placeholder with the generated image
                    const $imageContainer = $placeholder.find('.generated-images');
                    const imageHtml = `
                        <div class="generated-image" data-image-url="${data.image.url}">
                            <img src="${data.image.url}" alt="Generated image" />
                            <div class="image-actions">
                                <button type="button" class="vision-button vision-button-small"
                                        data-action="select_image"
                                        data-placeholder-id="${data.placeholder_id}"
                                        data-image-url="${data.image.url}">
                                    Select
                                </button>
                                <button type="button" class="vision-button vision-button-small vision-button-secondary"
                                        data-action="regenerate_image"
                                        data-placeholder-id="${data.placeholder_id}">
                                    Regenerate
                                </button>
                            </div>
                        </div>
                    `;
                    $imageContainer.append(imageHtml);
                }
            }
        },

        /**
         * Handle placeholder selection response
         */
        handlePlaceholderSelection: function(data, $button) {
            if (data.placeholder_id && data.selected_image_url) {
                // Update the placeholder to show the selected image
                const $placeholder = $(`.image-placeholder[data-placeholder-id="${data.placeholder_id}"]`);
                if ($placeholder.length) {
                    $placeholder.addClass('has-selection');

                    // Update the selected image display
                    const $selectedImage = $placeholder.find('.selected-image');
                    if ($selectedImage.length) {
                        $selectedImage.find('img').attr('src', data.selected_image_url);
                        $selectedImage.show();
                    }

                    // Hide the generation interface
                    $placeholder.find('.image-generation-interface').hide();
                }
            }
        },

        /**
         * Start multi-model article generation
         */
        startMultiModelGeneration: function() {
            // Get selected models and parameters
            const selectedModels = [];
            $('.ai-model-checkbox:checked').each(function() {
                selectedModels.push($(this).val());
            });

            if (selectedModels.length === 0) {
                this.showNotice('Please select at least one AI model.', 'error');
                return;
            }

            const topic = $('#article-topic').val();
            const wordCount = $('#word-count').val() || 1000;
            const tone = $('#tone-select').val() || 'professional';
            const audience = $('#audience-select').val() || 'general';
            const customPrompt = $('#custom-prompt').val();

            const data = {
                topic: topic,
                models: selectedModels,
                word_count: wordCount,
                tone: tone,
                audience: audience,
                custom_prompt: customPrompt
            };

            // Show progress
            this.updateGenerationProgress(0, selectedModels.length);

            // Make the AJAX request
            this.ajaxRequest('generate_multiple_drafts', data)
                .done((response) => {
                    if (response.success) {
                        this.displayDraftComparison(response.data.drafts);
                        this.updateGenerationProgress(selectedModels.length, selectedModels.length);

                        if (response.data.errors && response.data.errors.length > 0) {
                            this.showGenerationErrors(response.data.errors);
                        }
                    } else {
                        this.showNotice(response.data.error || 'Failed to generate drafts.', 'error');
                        this.hideGenerationProgress();
                    }
                })
                .fail(() => {
                    this.showNotice('Failed to generate drafts. Please try again.', 'error');
                    this.hideGenerationProgress();
                });
        },

        /**
         * Update generation progress
         */
        updateGenerationProgress: function(completed, total) {
            const percentage = Math.round((completed / total) * 100);
            const $progressBar = $('.progress-fill');
            const $progressText = $('.progress-text');

            $progressBar.css('width', percentage + '%');
            $progressText.text(`${percentage}% complete (${completed}/${total} models)`);
        },

        /**
         * Hide generation progress
         */
        hideGenerationProgress: function() {
            $('.comparison-loading').hide();
        },

        /**
         * Display draft comparison
         */
        displayDraftComparison: function(drafts) {
            const $comparisonContainer = $('#drafts-comparison');
            const $loadingDiv = $comparisonContainer.find('.comparison-loading');
            const $gridDiv = $comparisonContainer.find('.comparison-grid');

            // Hide loading, show grid
            $loadingDiv.hide();
            $gridDiv.show().empty();

            // Create draft cards
            drafts.forEach((draft, index) => {
                const draftCard = this.createDraftCard(draft, index);
                $gridDiv.append(draftCard);
            });

            // Enable the continue button
            $('[data-action="proceed-to-preview"]').prop('disabled', false);

            // Auto-select the first draft
            if (drafts.length > 0) {
                $gridDiv.find('.draft-card').first().addClass('selected');
            }
        },

        /**
         * Create draft card HTML
         */
        createDraftCard: function(draft, index) {
            const modelBadge = this.getModelBadge(draft.model);
            const truncatedContent = this.truncateContent(draft.content, 300);

            return $(`
                <div class="draft-card" data-draft-index="${index}">
                    <div class="draft-header">
                        <div class="draft-model-info">
                            <span class="model-name">${draft.model_info.name || draft.model}</span>
                            <span class="model-badge ${modelBadge.class}">${modelBadge.text}</span>
                        </div>
                        <div class="draft-actions">
                            <button type="button" class="vision-button vision-button-small"
                                    data-action="hide-draft" data-draft-index="${index}">
                                Hide
                            </button>
                        </div>
                    </div>

                    <div class="draft-metrics">
                        <span>Words: ${draft.word_count}</span>
                        <span>Readability: ${draft.readability_score}%</span>
                        <span>SEO: ${draft.seo_score}%</span>
                        <span>Links: ${draft.internal_links_count}</span>
                    </div>

                    <div class="draft-content">
                        <div class="draft-title">${draft.title}</div>
                        <div class="draft-excerpt">${draft.excerpt}</div>
                        <div class="draft-preview">${truncatedContent}</div>
                    </div>

                    <div class="draft-footer">
                        <div class="draft-rating">
                            ${this.createStarRating(5)}
                        </div>
                        <button type="button" class="draft-select-btn"
                                data-action="select-draft" data-draft-index="${index}">
                            Select This Draft
                        </button>
                    </div>
                </div>
            `);
        },

        /**
         * Get model badge information
         */
        getModelBadge: function(model) {
            const badges = {
                'gpt-4': { class: 'quality', text: 'Quality' },
                'gpt-3.5-turbo': { class: 'fast', text: 'Fast' },
                'claude-3': { class: 'recommended', text: 'Recommended' },
                'gemini': { class: 'fast', text: 'Fast' },
                'grok': { class: 'quality', text: 'Creative' }
            };

            return badges[model] || { class: 'fast', text: 'AI' };
        },

        /**
         * Truncate content for preview
         */
        truncateContent: function(content, maxLength) {
            const textContent = $('<div>').html(content).text();
            if (textContent.length <= maxLength) {
                return content;
            }

            const truncated = textContent.substring(0, maxLength) + '...';
            return '<p>' + truncated + '</p>';
        },

        /**
         * Create star rating HTML
         */
        createStarRating: function(maxStars) {
            let html = '';
            for (let i = 1; i <= maxStars; i++) {
                html += `<span class="rating-star" data-rating="${i}">★</span>`;
            }
            return html;
        },

        /**
         * Update category breakdown display
         */
        updateCategoryBreakdown: function(categories) {
            const $breakdown = $('.category-breakdown');
            if ($breakdown.length && categories.length > 0) {
                $breakdown.empty();

                categories.forEach(category => {
                    const progressWidth = Math.min(100, (category.total_articles / Math.max(1, category.page_count) * 20));
                    const categoryHtml = `
                        <div class="category-bar">
                            <div class="category-info">
                                <span class="category-name">${category.service_category.charAt(0).toUpperCase() + category.service_category.slice(1)}</span>
                                <span class="category-stats">${category.total_articles} articles</span>
                            </div>
                            <div class="category-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${progressWidth}%"></div>
                                </div>
                                <span class="progress-text">${category.page_count} pages</span>
                            </div>
                        </div>
                    `;
                    $breakdown.append(categoryHtml);
                });
            }
        },

        /**
         * Update balance insights display
         */
        updateBalanceInsights: function(insights) {
            const $insightsList = $('.balance-insights ul');
            if ($insightsList.length && insights.length > 0) {
                $insightsList.empty();

                insights.forEach(insight => {
                    const actionButton = insight.action ?
                        `<button type="button" class="insight-action-btn"
                                data-action="${insight.action}"
                                data-category="${insight.category || ''}">
                            Take Action
                        </button>` : '';

                    const insightHtml = `
                        <li class="${insight.type}">
                            ${insight.message}
                            ${actionButton}
                        </li>
                    `;
                    $insightsList.append(insightHtml);
                });
            }
        },

        /**
         * Update pages needing attention display
         */
        updatePagesNeedingAttention: function(pages) {
            const $grid = $('.pages-attention-grid');
            if ($grid.length && pages.length > 0) {
                $grid.empty();

                pages.forEach(page => {
                    const pageHtml = `
                        <div class="page-attention-card">
                            <div class="page-info">
                                <h4 class="page-title">${page.page_title}</h4>
                                <div class="page-meta">
                                    <span class="page-category">${page.service_category.charAt(0).toUpperCase() + page.service_category.slice(1)}</span>
                                    <span class="page-articles">${page.article_count}/${page.target_article_count} articles</span>
                                </div>
                                <div class="page-priority">
                                    <span class="priority-label">Priority:</span>
                                    <div class="priority-bar">
                                        <div class="priority-fill" style="width: ${page.priority_score}%"></div>
                                    </div>
                                    <span class="priority-score">${Math.round(page.priority_score)}%</span>
                                </div>
                            </div>
                            <div class="page-actions">
                                <button type="button" class="vision-button vision-button-small vision-button-primary"
                                        data-action="generate_for_page"
                                        data-page-id="${page.page_id}"
                                        data-page-title="${page.page_title}">
                                    Generate Article
                                </button>
                                <a href="/wp-admin/post.php?post=${page.page_id}&action=edit"
                                   class="vision-button vision-button-small vision-button-secondary">
                                    Edit Page
                                </a>
                            </div>
                        </div>
                    `;
                    $grid.append(pageHtml);
                });
            }
        },

        /**
         * Show generation errors
         */
        showGenerationErrors: function(errors) {
            if (errors.length > 0) {
                let errorMessage = 'Some models failed to generate content:\n';
                errors.forEach(error => {
                    errorMessage += `• ${error.model}: ${error.error}\n`;
                });
                this.showNotice(errorMessage, 'warning');
            }
        },

        /**
         * Make AJAX request with comprehensive debugging
         */
        ajaxRequest: function(endpoint, data = {}) {
            console.log('🔥 VisionFramework.ajaxRequest called:', {
                endpoint: endpoint,
                data: data,
                visionFramework: typeof visionFramework !== 'undefined' ? visionFramework : 'UNDEFINED'
            });
            
            // Check if visionFramework is available
            if (typeof visionFramework === 'undefined') {
                console.error('❌ CRITICAL ERROR: visionFramework is undefined!');
                console.log('🔍 Available global variables:', Object.keys(window));
                return $.Deferred().reject('visionFramework not available').promise();
            }
            
            // Validate required properties
            const required = ['ajaxUrl', 'action', 'nonce'];
            const missing = required.filter(prop => !visionFramework[prop]);
            if (missing.length > 0) {
                console.error('❌ Missing visionFramework properties:', missing);
                console.log('📋 visionFramework object:', visionFramework);
                return $.Deferred().reject('Missing required properties: ' + missing.join(', ')).promise();
            }
            
            // Clear any existing endpoint in data to prevent conflicts
            if (data.endpoint) {
                console.log('⚠️ Removing conflicting endpoint from data:', data.endpoint);
                delete data.endpoint;
            }
            
            data.endpoint = endpoint;
            data.action = visionFramework.action;
            data.nonce = visionFramework.nonce;
            
            console.log('📦 Final AJAX payload:', {
                url: visionFramework.ajaxUrl,
                data: data
            });
            
            const ajaxCall = $.ajax({
                url: visionFramework.ajaxUrl,
                type: 'POST',
                data: data,
                dataType: 'json'
            });
            
            // Add debug handlers
            ajaxCall.done(function(response) {
                console.log('✅ AJAX Success Response:', response);
            });
            
            ajaxCall.fail(function(xhr, status, error) {
                console.error('❌ AJAX Failed:', {
                    xhr: xhr,
                    status: status,
                    error: error,
                    responseText: xhr.responseText
                });
            });
            
            return ajaxCall;
        },
        
        /**
         * Fallback AJAX request when visionFramework is not available
         */
        fallbackAjaxRequest: function(endpoint, data = {}) {
            console.log('🆘 Using fallback AJAX request for:', endpoint);
            
            // Try to get WordPress AJAX URL manually
            let ajaxUrl = '/wp-admin/admin-ajax.php';
            if (window.ajaxurl) {
                ajaxUrl = window.ajaxurl;
            } else if (document.querySelector('script[src*="admin-ajax.php"]')) {
                // Try to extract from existing script tags
                const scripts = document.querySelectorAll('script[src*="wp-admin"]');
                if (scripts.length > 0) {
                    const src = scripts[0].src;
                    ajaxUrl = src.substring(0, src.indexOf('/wp-')) + '/wp-admin/admin-ajax.php';
                }
            }
            
            const requestData = {
                action: 'blog-writer_ajax',
                endpoint: endpoint,
                _wpnonce: $('meta[name="csrf-token"]').attr('content') || '', // Try to get nonce
                ...data
            };
            
            console.log('📦 Fallback AJAX payload:', {
                url: ajaxUrl,
                data: requestData
            });
            
            const ajaxCall = $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: requestData,
                dataType: 'json'
            });
            
            ajaxCall.fail(function(xhr, status, error) {
                console.error('❌ Fallback AJAX also failed:', {
                    xhr: xhr,
                    status: status,
                    error: error,
                    responseText: xhr.responseText
                });
            });
            
            return ajaxCall;
        },

        /**
         * Show admin notice
         */
        showNotice: function(message, type = 'info') {
            const $notice = $(`
                <div class="notice notice-${type} is-dismissible vision-notice" style="margin: 0 0 20px 0; position: relative; z-index: 1000;">
                    <p>${message}</p>
                    <button type="button" class="notice-dismiss">
                        <span class="screen-reader-text">Dismiss this notice.</span>
                    </button>
                </div>
            `);
            
            // Remove existing notices
            $('.vision-notice').remove();
            
            // Add new notice after the header, not above the banner
            const $header = $('.vision-header');
            const $main = $('.vision-main');
            
            if ($header.length && $main.length) {
                $main.prepend($notice);
            } else {
                // Fallback: add after any existing banners/welcome sections
                const $welcome = $('.vision-welcome');
                if ($welcome.length) {
                    $welcome.after($notice);
                } else {
                    $('.vision-framework-wrapper .vision-container').first().prepend($notice);
                }
            }
            
            // Handle dismiss button
            $notice.find('.notice-dismiss').on('click', function() {
                $notice.fadeOut(200, function() {
                    $(this).remove();
                });
            });
            
            // Auto-dismiss after 5 seconds for success messages
            if (type === 'success') {
                setTimeout(() => {
                    $notice.fadeOut(200, function() {
                        $(this).remove();
                    });
                }, 5000);
            }
        },

        /**
         * Utility: Debounce function
         */
        debounce: function(func, wait, immediate) {
            let timeout;
            return function executedFunction() {
                const context = this;
                const args = arguments;
                const later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        },
        
        /**
         * Show user management modal
         */
        showUserManagementModal: function(data) {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const isDark = currentTheme === 'dark';
            
            const modalHtml = `
                <div class="vision-modal" id="user-management-modal" data-theme="${currentTheme}">
                    <div class="vision-modal-content">
                        <div class="vision-modal-header">
                            <h3>Manage Blog Writer User</h3>
                            <button type="button" class="vision-modal-close">&times;</button>
                        </div>
                        <div class="vision-modal-body">
                            <div class="vision-user-options">
                                <div class="vision-option-card">
                                    <h4>👥 Select Existing User</h4>
                                    <p class="warning-text" style="color: ${isDark ? '#ffa500' : '#8B4513'}; background: ${isDark ? 'rgba(255, 165, 0, 0.1)' : 'rgba(255, 165, 0, 0.1)'}; padding: 8px; border-radius: 4px; border-left: 3px solid ${isDark ? '#ffa500' : '#8B4513'};">⚠️ The selected user will be visible as the author on all generated blog posts.</p>
                                    <select id="existing-user-select" class="vision-select">
                                        <option value="">Choose a user...</option>
                                        ${data.available_users.map(user => 
                                            `<option value="${user.id}" ${user.id == data.current_user_id ? 'selected' : ''}>
                                                ${user.display_name} (${user.username})
                                            </option>`
                                        ).join('')}
                                    </select>
                                    <button type="button" class="vision-button vision-button-primary" id="assign-existing-user">
                                        Assign Selected User
                                    </button>
                                </div>
                                
                                <div class="vision-divider">OR</div>
                                
                                <div class="vision-option-card">
                                    <h4>➕ Create New User</h4>
                                    <p>Create a dedicated user account for blog authorship. A secure password will be automatically generated.</p>
                                    <div class="vision-form-group">
                                        <label>Username:</label>
                                        <input type="text" id="new-username" class="vision-input" placeholder="blogwriter_${window.location.hostname.replace(/\./g, '_')}">
                                    </div>
                                    <div class="vision-form-group">
                                        <label>Email:</label>
                                        <input type="email" id="new-email" class="vision-input" placeholder="blogwriter@${window.location.hostname}">
                                    </div>
                                    <div class="vision-form-group">
                                        <label>Display Name:</label>
                                        <input type="text" id="new-display-name" class="vision-input" placeholder="Blog Writer">
                                    </div>
                                    <button type="button" class="vision-button vision-button-secondary" id="create-new-user">
                                        Create & Assign User
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Remove existing modal
            $('#user-management-modal').remove();
            
            // Add modal to page
            $('body').append(modalHtml);
            
            // Apply theme-aware styling
            this.applyModalThemeStyles(currentTheme);
            
            // Bind events
            this.bindUserModalEvents();
        },
        
        /**
         * Apply comprehensive theme-aware styling to modal
         */
        applyModalThemeStyles: function(theme) {
            const $modal = $('#user-management-modal');
            const isDark = theme === 'dark';
            
            // Apply theme attribute for CSS targeting
            $modal.attr('data-theme', theme);
            
            // Base modal backdrop
            $modal.css({
                'background': isDark ? 'rgba(0, 0, 0, 0.85)' : 'rgba(0, 0, 0, 0.6)'
            });
            
            // Modal content container
            const $content = $modal.find('.vision-modal-content');
            $content.css({
                'background': isDark ? '#2c3338' : '#ffffff',
                'color': isDark ? '#f0f0f1' : '#1d2327',
                'border': isDark ? '1px solid #50575e' : '1px solid #ddd',
                'box-shadow': isDark ? '0 20px 60px rgba(0, 0, 0, 0.7)' : '0 20px 60px rgba(0, 0, 0, 0.3)'
            });
            
            // Modal header
            const $header = $modal.find('.vision-modal-header');
            $header.css({
                'background': isDark ? '#32373c' : '#f6f7f7',
                'border-bottom': isDark ? '1px solid #50575e' : '1px solid #ddd',
                'color': isDark ? '#f0f0f1' : '#1d2327'
            });
            
            // Modal header title
            $header.find('h3').css({
                'color': isDark ? '#f0f0f1' : '#1d2327'
            });
            
            // Close button
            const $closeBtn = $modal.find('.vision-modal-close');
            $closeBtn.css({
                'color': isDark ? '#c3c4c7' : '#646970'
            }).hover(
                function() {
                    $(this).css({
                        'background': isDark ? '#50575e' : '#ddd',
                        'color': isDark ? '#f0f0f1' : '#1d2327'
                    });
                },
                function() {
                    $(this).css({
                        'background': 'transparent',
                        'color': isDark ? '#c3c4c7' : '#646970'
                    });
                }
            );
            
            // Option cards
            const $optionCards = $modal.find('.vision-option-card');
            $optionCards.css({
                'background': isDark ? '#1e2328' : '#f9f9f9',
                'border': isDark ? '2px solid #3c434a' : '2px solid #e0e0e0',
                'color': isDark ? '#f0f0f1' : '#1d2327'
            });
            
            // Option card headers
            $optionCards.find('h4').css({
                'color': isDark ? '#f0f0f1' : '#1d2327'
            });
            
            // Option card paragraphs
            $optionCards.find('p').css({
                'color': isDark ? '#c3c4c7' : '#646970'
            });
            
            // Form inputs and selects
            const $inputs = $modal.find('.vision-input, .vision-select');
            $inputs.css({
                'background': isDark ? '#3c434a' : '#ffffff',
                'color': isDark ? '#f0f0f1' : '#1d2327',
                'border': isDark ? '2px solid #50575e' : '2px solid #ddd'
            }).focus(function() {
                $(this).css({
                    'border-color': '#3b82f6',
                    'box-shadow': isDark ? 
                        '0 0 0 3px rgba(59, 130, 246, 0.2)' : 
                        '0 0 0 3px rgba(59, 130, 246, 0.1)'
                });
            }).blur(function() {
                $(this).css({
                    'border-color': isDark ? '#50575e' : '#ddd',
                    'box-shadow': 'none'
                });
            });
            
            // Form labels
            $modal.find('label').css({
                'color': isDark ? '#f0f0f1' : '#1d2327'
            });
            
            // Warning text with enhanced contrast
            const $warningText = $modal.find('.warning-text');
            $warningText.css({
                'color': isDark ? '#fbbf24' : '#92400e',
                'background': isDark ? 'rgba(251, 191, 36, 0.15)' : 'rgba(251, 191, 36, 0.1)',
                'border-left-color': isDark ? '#fbbf24' : '#92400e'
            });
            
            // Divider
            const $divider = $modal.find('.vision-divider');
            $divider.css({
                'color': isDark ? '#8c8f94' : '#646970'
            });
            
            // Buttons - preserve their existing styling but ensure visibility
            const $buttons = $modal.find('.vision-button');
            $buttons.each(function() {
                const $btn = $(this);
                if ($btn.hasClass('vision-button-primary')) {
                    // Primary buttons should maintain their blue theme
                    $btn.css({
                        'background': 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
                        'color': '#ffffff',
                        'border-color': '#3b82f6'
                    });
                } else {
                    // Secondary buttons adapt to theme
                    $btn.css({
                        'background': isDark ? '#3c434a' : '#f6f7f7',
                        'color': isDark ? '#f0f0f1' : '#1d2327',
                        'border-color': isDark ? '#50575e' : '#ddd'
                    });
                }
            });
            
            // Add enhanced hover effects
            $optionCards.hover(
                function() {
                    $(this).css({
                        'border-color': '#3b82f6',
                        'transform': 'translateY(-2px)',
                        'box-shadow': isDark ? 
                            '0 8px 25px rgba(0, 0, 0, 0.4)' : 
                            '0 8px 25px rgba(0, 0, 0, 0.15)'
                    });
                },
                function() {
                    $(this).css({
                        'border-color': isDark ? '#3c434a' : '#e0e0e0',
                        'transform': 'translateY(0)',
                        'box-shadow': 'none'
                    });
                }
            );
        },
        
        /**
         * Bind user management modal events
         */
        bindUserModalEvents: function() {
            const self = this;
            
            // Close modal with ESC key
            $(document).on('keydown.userModal', function(e) {
                if (e.key === 'Escape') {
                    self.closeUserModal();
                }
            });
            
            // Close modal on background click or close button
            $(document).on('click', '.vision-modal-close, .vision-modal', function(e) {
                if (e.target === this) {
                    self.closeUserModal();
                }
            });
            
            // Prevent modal close on content click
            $(document).on('click', '.vision-modal-content', function(e) {
                e.stopPropagation();
            });
            
            // Assign existing user
            $(document).on('click', '#assign-existing-user', function() {
                const userId = $('#existing-user-select').val();
                if (!userId) {
                    self.showNotice('Please select a user first', 'warning');
                    return;
                }
                
                self.assignUser(userId);
            });
            
            // Create new user
            $(document).on('click', '#create-new-user', function() {
                const username = $('#new-username').val().trim();
                const email = $('#new-email').val().trim();
                const displayName = $('#new-display-name').val().trim();
                
                if (!username || !email) {
                    self.showNotice('Username and email are required', 'warning');
                    return;
                }
                
                self.createAndAssignUser({
                    username: username,
                    email: email,
                    display_name: displayName || username
                });
            });
        },
        
        /**
         * Close user modal and cleanup
         */
        closeUserModal: function() {
            $('#user-management-modal').fadeOut(300, function() {
                $(this).remove();
            });
            $(document).off('keydown.userModal');
        },
        
        /**
         * Assign existing user
         */
        assignUser: function(userId) {
            const self = this;
            
            this.ajaxRequest('assign_user', {user_id: userId})
                .done(function(response) {
                    if (response.success) {
                        self.showNotice(response.data.message, 'success');
                        self.closeUserModal();
                        
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        self.showNotice(response.data.error || 'Assignment failed', 'error');
                    }
                })
                .fail(function() {
                    self.showNotice('Network error during user assignment', 'error');
                });
        },
        
        /**
         * Create and assign new user
         */
        createAndAssignUser: function(userData) {
            const self = this;
            
            this.ajaxRequest('create_user', userData)
                .done(function(response) {
                    if (response.success) {
                        // Show password information if available
                        let message = response.data.message;
                        if (response.data.password) {
                            message += `<br><br><strong>Generated Password:</strong> <code style="background: #f0f0f0; padding: 2px 4px; font-family: monospace;">${response.data.password}</code><br><small>Please save this password for future reference.</small>`;
                        }
                        
                        self.showNotice(message, 'success');
                        self.closeUserModal();
                        
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000); // Give user time to see the password
                    } else {
                        self.showNotice(response.data.error || 'User creation failed', 'error');
                    }
                })
                .fail(function() {
                    self.showNotice('Network error during user creation', 'error');
                });
        }
    };

/* ========================================
 * Blog Writer Specific JavaScript
 * ======================================== */

    // Blog Writer specific functionality
    const BlogWriter = {
        /**
         * Initialize Blog Writer specific features
         */
        init: function() {
            console.log('🚀 BlogWriter.init() called');
            
            // Critical visionFramework check
            if (typeof visionFramework === 'undefined') {
                console.error('❌ BlogWriter CRITICAL ERROR: visionFramework is not available!');
                console.log('🔍 Available globals:', Object.keys(window).filter(k => k.toLowerCase().includes('vision')));
                
                // Try to find alternative names
                const alternatives = ['blogWriterFramework', 'vision_framework', 'VisionFramework'];
                alternatives.forEach(alt => {
                    if (window[alt]) {
                        console.log('🔍 Found alternative:', alt, window[alt]);
                    }
                });
                
                // Show user-visible error
                $('body').prepend('<div style="background: #dc3545; color: white; padding: 15px; margin: 10px; border-radius: 5px; font-weight: bold;">❌ AJAX ERROR: WordPress admin scripts not loaded properly. Please refresh the page.</div>');
                return;
            }
            
            console.log('✅ visionFramework available:', visionFramework);
            
            this.bindBlogWriterEvents();
            this.initKeywordExtraction();
            this.initArticleGeneration();
            this.initModelCompetition();
            this.initUserManagement();
            this.initWorkflowSteps();
            
            // Auto-load dashboard stats on init
            if ($('.vision-dashboard').length) {
                this.loadDashboardStats();
            }
            
            // Initialize API key original values for change detection
            $('.api-key-input').each(function() {
                const $field = $(this);
                const currentValue = $field.val();
                $field.data('original-value', currentValue);
                
                const inputId = $field.attr('id');
                const provider = inputId.replace('-api-key', '');
                console.log(`🆔 Set original value for ${provider}:`, currentValue ? currentValue.substring(0, 8) + '...' : 'EMPTY');
            });
        },

        /**
         * Bind Blog Writer specific events
         */
        bindBlogWriterEvents: function() {
            // Keyword extraction events
            $(document).on('click', '.extract-keywords-btn', this.handleKeywordExtraction.bind(this));
            $(document).on('click', '.extract-all-keywords-btn', this.handleBulkKeywordExtraction.bind(this));
            
            // Article generation events
            $(document).on('click', '.generate-article-btn', this.handleArticleGeneration.bind(this));
            $(document).on('click', '.preview-article-btn', this.handleArticlePreview.bind(this));
            $(document).on('click', '.publish-article-btn', this.handleArticlePublish.bind(this));
            
            // Model selection
            $(document).on('click', '.vision-model-card', this.handleModelSelection.bind(this));
            $(document).on('click', '.model-competition-btn', this.startModelCompetition.bind(this));
            
            // User management
            $(document).on('click', '.create-user-btn', this.handleUserCreation.bind(this));
            $(document).on('click', '.assign-user-btn', this.handleUserAssignment.bind(this));
            
            // Multi-Provider API Settings - with debugging
            console.log('🔗 Binding API test button events...');
            $(document).on('click', '.test-api-btn', function(e) {
                console.log('🔴 TEST API BUTTON CLICKED!', e.target);
                BlogWriter.handleProviderApiTest.call(BlogWriter, e);
            });
            
            $(document).on('click', '.test-all-apis', function(e) {
                console.log('🔴 TEST ALL APIS BUTTON CLICKED!', e.target);
                BlogWriter.handleTestAllApis.call(BlogWriter, e);
            });
            
            $(document).on('submit', '.vision-settings-form', this.handleSettingsSave.bind(this));
            
            // Auto-save API keys on blur with debugging
            console.log('🔗 Binding API key auto-save events...');
            $(document).on('blur', '.api-key-input', function(e) {
                console.log('🔵 API KEY FIELD BLURRED!', e.target.id);
                BlogWriter.handleProviderApiKeySave.call(BlogWriter, e);
            });
            
            // Clear API status when key is changed
            $(document).on('input', '.api-key-input', function() {
                const inputId = $(this).attr('id');
                const provider = inputId.replace('-api-key', '');
                console.log('🎨 API key changed for provider:', provider);
                $(`#${provider}-status-container`).empty();
            });
            
            // Workflow steps
            $(document).on('click', '.workflow-step-btn', this.handleWorkflowStep.bind(this));
            
            // Status filters
            $(document).on('change', '#status-filter', this.handleStatusFilter.bind(this));
            $(document).on('change', '#model-filter', this.handleModelFilter.bind(this));
            
            // Real-time updates
            this.initRealTimeUpdates();

            // Model management
            this.initModelManagement();
        },

        /**
         * Initialize keyword extraction functionality
         */
        initKeywordExtraction: function() {
            // Auto-save extracted keywords
            $(document).on('blur', '.keyword-input', this.saveKeywords.bind(this));
            
            // Keyword suggestion dropdown
            $(document).on('focus', '.keyword-input', this.showKeywordSuggestions.bind(this));
            
            // Bulk selection for pages
            $(document).on('change', '.page-checkbox', this.updateBulkActions.bind(this));
            $(document).on('click', '.select-all-pages', this.toggleAllPages.bind(this));
        },

        /**
         * Initialize article generation workflow
         */
        initArticleGeneration: function() {
            // Multi-step workflow
            this.currentStep = 1;
            this.totalSteps = 4;
            
            $(document).on('click', '.next-step-btn', this.nextWorkflowStep.bind(this));
            $(document).on('click', '.prev-step-btn', this.prevWorkflowStep.bind(this));
            
            // Real-time word count and cost estimation
            $(document).on('input', '#word-count-input', this.updateCostEstimation.bind(this));
            $(document).on('change', '.model-checkbox', this.updateCostEstimation.bind(this));
            
            // Internal linking preview
            $(document).on('click', '.preview-links-btn', this.previewInternalLinks.bind(this));
        },

        /**
         * Initialize model competition interface
         */
        initModelCompetition: function() {
            this.selectedModels = [];
            this.competitionResults = {};
            
            // Live competition results
            $(document).on('click', '.start-competition-btn', this.startModelCompetition.bind(this));
            $(document).on('click', '.vote-model-btn', this.voteForModel.bind(this));
        },

        /**
         * Initialize user management
         */
        initUserManagement: function() {
            // User creation modal
            $(document).on('click', '.create-user-modal-btn', this.showUserCreationModal.bind(this));
            
            // User assignment dropdown
            this.loadAvailableUsers();
        },

        /**
         * Initialize workflow steps tracking
         */
        initWorkflowSteps: function() {
            this.checkWorkflowCompletion();
            
            // Progress indicators
            this.updateProgressIndicators();
        },

        /**
         * Handle keyword extraction for single page
         */
        handleKeywordExtraction: function(e) {
            e.preventDefault();
            
            const $button = $(e.currentTarget);
            const pageId = $button.data('page-id');
            const model = $('#keyword-model-select').val() || 'gpt-3.5-turbo';
            
            this.showLoadingState($button, 'Extracting keywords...');
            
            VisionFramework.ajaxRequest('extract_keywords', {
                page_id: pageId,
                model: model
            })
            .done((response) => {
                if (response.success) {
                    this.displayExtractedKeywords(pageId, response.data.keywords);
                    this.updateKeywordStats();
                    VisionFramework.showNotice(response.data.message, 'success');
                } else {
                    VisionFramework.showNotice(response.data.error || 'Extraction failed', 'error');
                }
            })
            .fail(() => {
                VisionFramework.showNotice('Network error during keyword extraction', 'error');
            })
            .always(() => {
                this.hideLoadingState($button);
            });
        },

        /**
         * Handle bulk keyword extraction
         */
        handleBulkKeywordExtraction: function(e) {
            e.preventDefault();
            
            const selectedPages = $('.page-checkbox:checked').map(function() {
                return $(this).val();
            }).get();
            
            if (selectedPages.length === 0) {
                VisionFramework.showNotice('Please select pages to extract keywords from', 'warning');
                return;
            }
            
            const $button = $(e.currentTarget);
            this.showBulkProgress(selectedPages.length);
            
            this.processBulkExtraction(selectedPages, 0);
        },

        /**
         * Process bulk extraction recursively
         */
        processBulkExtraction: function(pageIds, index) {
            if (index >= pageIds.length) {
                this.hideBulkProgress();
                VisionFramework.showNotice(`Keyword extraction completed for ${pageIds.length} pages`, 'success');
                this.updateKeywordStats();
                return;
            }
            
            const pageId = pageIds[index];
            this.updateBulkProgress(index + 1, pageIds.length, `Processing page ${pageId}...`);
            
            VisionFramework.ajaxRequest('extract_keywords', {
                page_id: pageId,
                model: $('#keyword-model-select').val() || 'gpt-3.5-turbo'
            })
            .done((response) => {
                if (response.success) {
                    this.displayExtractedKeywords(pageId, response.data.keywords);
                }
            })
            .always(() => {
                // Process next page after a short delay
                setTimeout(() => {
                    this.processBulkExtraction(pageIds, index + 1);
                }, 500);
            });
        },

        /**
         * Handle article generation
         */
        handleArticleGeneration: function(e) {
            e.preventDefault();
            
            const formData = this.collectArticleData();
            
            if (!this.validateArticleData(formData)) {
                return;
            }
            
            const $button = $(e.currentTarget);
            this.showGenerationProgress();
            
            VisionFramework.ajaxRequest('generate_article', formData)
            .done((response) => {
                if (response.success) {
                    this.displayGeneratedArticle(response.data);
                    VisionFramework.showNotice('Article generated successfully!', 'success');
                } else {
                    VisionFramework.showNotice(response.data.error || 'Generation failed', 'error');
                }
            })
            .fail(() => {
                VisionFramework.showNotice('Network error during article generation', 'error');
            })
            .always(() => {
                this.hideGenerationProgress();
            });
        },

        /**
         * Handle model competition
         */
        startModelCompetition: function(e) {
            e.preventDefault();
            
            const selectedModels = $('.model-checkbox:checked').map(function() {
                return $(this).val();
            }).get();
            
            if (selectedModels.length < 2) {
                VisionFramework.showNotice('Please select at least 2 models for competition', 'warning');
                return;
            }
            
            const topic = $('#topic-input').val();
            if (!topic.trim()) {
                VisionFramework.showNotice('Please enter a topic for the competition', 'warning');
                return;
            }
            
            this.showCompetitionProgress(selectedModels.length);
            
            VisionFramework.ajaxRequest('start_model_competition', {
                models: selectedModels,
                topic: topic,
                word_count: $('#word-count-input').val() || 1000
            })
            .done((response) => {
                if (response.success) {
                    this.displayCompetitionResults(response.data.results);
                } else {
                    VisionFramework.showNotice(response.data.error || 'Competition failed', 'error');
                }
            })
            .always(() => {
                this.hideCompetitionProgress();
            });
        },

        /**
         * Handle multi-provider API testing with comprehensive debugging
         */
        handleProviderApiTest: function(e) {
            e.preventDefault();
            console.log('🔥 API Test Button Clicked!', e);
            
            const $button = $(e.currentTarget);
            const provider = $button.data('provider');
            const $apiKeyField = $(`#${provider}-api-key`);
            const apiKey = $apiKeyField.val();
            
            console.log('🔍 API Test Debug Info:', {
                provider: provider,
                buttonElement: $button[0],
                fieldExists: $apiKeyField.length > 0,
                fieldValue: apiKey ? apiKey.substring(0, 8) + '...' : 'EMPTY',
                fieldValueLength: apiKey ? apiKey.length : 0,
                buttonData: $button.data()
            });
            
            if (!provider) {
                console.error('❌ No provider specified!');
                return;
            }
            
            if (!apiKey || !apiKey.trim()) {
                console.warn('⚠️ No API key entered');
                this.showProviderApiStatus(provider, 'error', 'Please enter an API key to test');
                return;
            }
            
            console.log('🚀 Starting API test for provider:', provider);
            
            // Show loading state
            this.setProviderLoadingState($button, true);
            this.showProviderApiStatus(provider, 'testing', 'Testing API connection...');
            this.updateProviderStatus(provider, 'testing');
            
            // Auto-save the key before testing
            console.log('💾 Auto-saving API key...');
            
            // Choose AJAX method based on availability
            const ajaxMethod = typeof visionFramework !== 'undefined' ? VisionFramework.ajaxRequest : VisionFramework.fallbackAjaxRequest;
            
            ajaxMethod('save_api_key', {
                provider: provider,
                api_key: apiKey.trim()
            })
            .done((saveResponse) => {
                console.log('💾 API key save response:', saveResponse);
            })
            .fail((saveError) => {
                console.warn('⚠️ API key save failed:', saveError);
            })
            .always(() => {
                // Now test the API
                console.log('🧪 Testing API connection...');
                ajaxMethod('test_api_provider', {
                    provider: provider,
                    api_key: apiKey.trim()
                })
                .done((response) => {
                    console.log(`✅ ${provider} API Test Response:`, response);
                    if (response.success) {
                        const data = response.data;
                        
                        // Create a cleaner success message
                        let message = data.message || `${provider.charAt(0).toUpperCase() + provider.slice(1)} API connected successfully!`;
                        
                        // Only add model/timing info if available and valid
                        const modelCount = data.models_available;
                        const responseTime = data.response_time;
                        
                        console.log(`🔍 Response data for ${provider}:`, {
                            modelCount: modelCount,
                            responseTime: responseTime,
                            details: data.details
                        });
                        
                        if (modelCount !== undefined && modelCount !== null && !isNaN(modelCount) && 
                            responseTime !== undefined && responseTime !== null && !isNaN(responseTime)) {
                            message += ` (${modelCount} models, ${responseTime}s)`;
                        } else if (modelCount !== undefined && modelCount !== null && !isNaN(modelCount)) {
                            message += ` (${modelCount} models available)`;
                        } else if (responseTime !== undefined && responseTime !== null && !isNaN(responseTime)) {
                            message += ` (${responseTime}s response time)`;
                        } else if (data.details && data.details.trim()) {
                            message += ` - ${data.details}`;
                        }
                        
                        this.showProviderApiStatus(provider, 'success', message);
                        this.updateProviderStatus(provider, 'connected');
                        
                        // Add success flash animation
                        const $providerSection = $(`.api-provider-section[data-provider="${provider}"]`);
                        $providerSection.addClass('success-flash');
                        setTimeout(() => {
                            $providerSection.removeClass('success-flash');
                        }, 1000);
                        
                        // Show success notification
                        VisionFramework.showNotice(`${provider} API connected successfully!`, 'success');
                    } else {
                        console.error(`❌ ${provider} API Test Failed:`, response);
                        const error = response.data?.error || response.data || 'API test failed';
                        this.showProviderApiStatus(provider, 'error', error);
                        this.updateProviderStatus(provider, 'error');
                    }
                })
                .fail((xhr) => {
                    console.error(`💥 ${provider} API Test Network Error:`, xhr);
                    let errorMsg = 'Network error during API test';
                    if (xhr.responseJSON && xhr.responseJSON.data) {
                        errorMsg = xhr.responseJSON.data.error || xhr.responseJSON.data;
                    }
                    this.showProviderApiStatus(provider, 'error', errorMsg);
                    this.updateProviderStatus(provider, 'error');
                })
                .always(() => {
                    console.log('🏁 API test completed, hiding loading state');
                    this.setProviderLoadingState($button, false);
                });
            });
        },
        
        /**
         * Handle test all APIs
         */
        handleTestAllApis: function(e) {
            e.preventDefault();
            
            const $button = $(e.currentTarget);
            this.setProviderLoadingState($button, true);
            
            // Choose AJAX method based on availability
            const ajaxMethod = typeof visionFramework !== 'undefined' ? VisionFramework.ajaxRequest : VisionFramework.fallbackAjaxRequest;
            
            ajaxMethod('test_all_apis', {})
            .done((response) => {
                if (response.success) {
                    const data = response.data;
                    const results = data.results;
                    
                    // Update each provider status
                    Object.keys(results).forEach(provider => {
                        const result = results[provider];
                        if (result.success) {
                            this.showProviderApiStatus(provider, 'success', 
                                `Connected (${result.models} models)`);
                            this.updateProviderStatus(provider, 'connected');
                        } else {
                            this.showProviderApiStatus(provider, 'error', result.message);
                            this.updateProviderStatus(provider, 'error');
                        }
                    });
                    
                    VisionFramework.showNotice(data.message, 'success');
                } else {
                    VisionFramework.showNotice(response.data.error || 'Bulk test failed', 'error');
                }
            })
            .fail(() => {
                VisionFramework.showNotice('Network error during bulk API test', 'error');
            })
            .always(() => {
                this.setProviderLoadingState($button, false);
            });
        },
        
        /**
         * Show provider-specific API status with robust feedback
         */
        showProviderApiStatus: function(provider, type, message) {
            const $statusContainer = $(`#${provider}-status-container`);
            
            const icons = {
                success: '✅',
                error: '❌',
                testing: '🔄',
                saved: '💾'
            };
            
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                testing: '#007cba',
                saved: '#6c757d'
            };
            
            const timestamp = new Date().toLocaleString();
            
            $statusContainer.html(`
                <div class="api-status-message ${type}" style="
                    margin-top: 8px;
                    padding: 8px 12px;
                    border-radius: 4px;
                    font-size: 13px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    animation: fadeIn 0.3s ease;
                ">
                    <span style="font-size: 14px;">${icons[type]}</span>
                    <span style="flex: 1;">${message}</span>
                    <small style="opacity: 0.7;">${timestamp}</small>
                </div>
            `);
        },
        
        /**
         * Update provider status indicator
         */
        updateProviderStatus: function(provider, status) {
            const $indicator = $(`#${provider}-status .status-indicator`);
            $indicator.removeClass('connected error testing').addClass(status);
        },
        
        /**
         * Set provider button loading state
         */
        setProviderLoadingState: function($button, loading) {
            const $btnText = $button.find('.btn-text');
            const $btnLoading = $button.find('.btn-loading');
            
            if (loading) {
                $button.prop('disabled', true);
                $btnText.hide();
                $btnLoading.show();
            } else {
                $button.prop('disabled', false);
                $btnText.show();
                $btnLoading.hide();
            }
        },
        
        /**
         * Handle API key auto-save with enhanced debugging
         */
        handleProviderApiKeySave: function(e) {
            const $input = $(e.currentTarget);
            const inputId = $input.attr('id');
            const provider = inputId.replace('-api-key', '');
            const apiKey = $input.val().trim();
            
            console.log('💾 Auto-saving API key for provider:', provider, 'Length:', apiKey.length, 'Field ID:', inputId);
            
            // Don't save if field is empty
            if (!apiKey) {
                console.log('⚠️ Skipping save - empty API key');
                return;
            }
            
            // Check if value actually changed from stored value
            const originalValue = $input.data('original-value') || '';
            if (apiKey === originalValue) {
                console.log('⚠️ Skipping save - no change from original:', originalValue.substring(0, 8) + '...');
                return;
            }
            
            console.log('🚀 Saving API key for:', provider, 'Previous value:', originalValue ? originalValue.substring(0, 8) + '...' : 'NONE');
            
            // Choose AJAX method based on availability
            const ajaxMethod = typeof visionFramework !== 'undefined' ? VisionFramework.ajaxRequest : VisionFramework.fallbackAjaxRequest;
            
            // Auto-save the API key
            ajaxMethod('save_api_key', {
                provider: provider,
                api_key: apiKey
            })
            .done((response) => {
                console.log('✅ API key save successful for', provider, ':', response);
                if (response.success) {
                    this.showProviderApiStatus(provider, 'saved', `API key saved for ${provider}`);
                    $input.data('original-value', apiKey);
                    console.log('💾 Updated original-value for', provider, 'to:', apiKey.substring(0, 8) + '...');
                } else {
                    console.error('❌ API key save returned unsuccessful:', response);
                    this.showProviderApiStatus(provider, 'error', response.data?.error || 'Failed to save API key');
                }
            })
            .fail((error) => {
                console.error('❌ API key save network error for', provider, ':', error);
                let errorMsg = 'Network error saving API key';
                if (error.responseJSON && error.responseJSON.data) {
                    errorMsg = error.responseJSON.data.error || error.responseJSON.data;
                }
                this.showProviderApiStatus(provider, 'error', errorMsg);
            });
        },

        /**
         * Load dashboard statistics
         */
        loadDashboardStats: function() {
            VisionFramework.ajaxRequest('get_dashboard_stats')
            .done((response) => {
                if (response.success) {
                    this.updateDashboardStats(response.data);
                }
            });
        },

        /**
         * Update dashboard statistics
         */
        updateDashboardStats: function(stats) {
            Object.keys(stats).forEach(key => {
                $(`.stat-${key}`).text(stats[key]);
            });
            
            // Update progress bars
            $('.progress-bar').each(function() {
                const $bar = $(this);
                const statKey = $bar.data('stat');
                if (stats[statKey + '_percentage']) {
                    $bar.find('.progress-fill').css('width', stats[statKey + '_percentage'] + '%');
                }
            });
        },

        /**
         * Show loading state for buttons
         */
        showLoadingState: function($button, text = 'Loading...') {
            $button.data('original-text', $button.html())
                   .prop('disabled', true)
                   .html(`<span class="spinner"></span> ${text}`);
        },

        /**
         * Hide loading state for buttons
         */
        hideLoadingState: function($button) {
            $button.prop('disabled', false)
                   .html($button.data('original-text'));
        },

        /**
         * Show bulk progress modal
         */
        showBulkProgress: function(total) {
            const modal = `
                <div id="bulk-progress-modal" class="vision-modal">
                    <div class="vision-modal-content">
                        <h3>Bulk Processing</h3>
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <div class="progress-text">Preparing...</div>
                        </div>
                    </div>
                </div>
            `;
            $('body').append(modal);
        },

        /**
         * Update bulk progress
         */
        updateBulkProgress: function(current, total, text) {
            const percentage = (current / total) * 100;
            $('#bulk-progress-modal .progress-fill').css('width', percentage + '%');
            $('#bulk-progress-modal .progress-text').text(text || `${current} of ${total}`);
        },

        /**
         * Hide bulk progress modal
         */
        hideBulkProgress: function() {
            $('#bulk-progress-modal').remove();
        },

        /**
         * Initialize real-time updates
         */
        initRealTimeUpdates: function() {
            // Poll for updates every 30 seconds when on dashboard
            if ($('.vision-dashboard').length) {
                setInterval(() => {
                    this.loadDashboardStats();
                }, 30000);
            }
        },

        /**
         * Collect article generation data
         */
        collectArticleData: function() {
            return {
                topic: $('#topic-input').val(),
                word_count: $('#word-count-input').val() || 1000,
                models: $('.model-checkbox:checked').map(function() { return $(this).val(); }).get(),
                internal_linking: $('#internal-linking-checkbox').is(':checked'),
                generate_image: $('#generate-image-checkbox').is(':checked'),
                custom_prompt: $('#custom-prompt-textarea').val()
            };
        },

        /**
         * Validate article generation data
         */
        validateArticleData: function(data) {
            if (!data.topic.trim()) {
                VisionFramework.showNotice('Please enter a topic for the article', 'warning');
                return false;
            }
            
            if (data.models.length === 0) {
                VisionFramework.showNotice('Please select at least one AI model', 'warning');
                return false;
            }
            
            if (data.word_count < 100 || data.word_count > 5000) {
                VisionFramework.showNotice('Word count must be between 100 and 5000', 'warning');
                return false;
            }
            
            return true;
        },

        /**
         * Display extracted keywords
         */
        displayExtractedKeywords: function(pageId, keywords) {
            const $keywordCell = $(`.page-row[data-page-id="${pageId}"] .keywords-cell`);
            const keywordTags = keywords.map(keyword => 
                `<span class="keyword-tag">${keyword}</span>`
            ).join('');
            
            $keywordCell.html(keywordTags);
        },

        /**
         * Update keyword statistics
         */
        updateKeywordStats: function() {
            VisionFramework.ajaxRequest('get_keyword_stats')
            .done((response) => {
                if (response.success) {
                    Object.keys(response.data).forEach(key => {
                        $(`.stat-${key}`).text(response.data[key]);
                    });
                }
            });
        },

        /**
         * Show generation progress
         */
        showGenerationProgress: function() {
            $('.generation-progress').show();
            $('.article-output').hide();
        },

        /**
         * Hide generation progress
         */
        hideGenerationProgress: function() {
            $('.generation-progress').hide();
        },
        
        /**
         * Handle article preview
         */
        handleArticlePreview: function(e) {
            e.preventDefault();
            
            const $button = $(e.currentTarget);
            const articleId = $button.data('article-id');
            
            if (!articleId) {
                VisionFramework.showNotice('No article ID provided', 'warning');
                return;
            }
            
            // Open preview in new window/tab
            const previewUrl = `/wp-admin/post.php?post=${articleId}&action=edit&preview=true`;
            window.open(previewUrl, '_blank');
        },
        
        /**
         * Handle article publish
         */
        handleArticlePublish: function(e) {
            e.preventDefault();
            
            const $button = $(e.currentTarget);
            const articleId = $button.data('article-id');
            
            if (!articleId) {
                VisionFramework.showNotice('No article ID provided', 'warning');
                return;
            }
            
            this.showLoadingState($button, 'Publishing...');
            
            VisionFramework.ajaxRequest('publish_article', {
                article_id: articleId
            })
            .done((response) => {
                if (response.success) {
                    VisionFramework.showNotice(response.data.message, 'success');
                } else {
                    VisionFramework.showNotice(response.data.error || 'Publishing failed', 'error');
                }
            })
            .always(() => {
                this.hideLoadingState($button);
            });
        },
        
        /**
         * Handle model selection
         */
        handleModelSelection: function(e) {
            const $card = $(e.currentTarget);
            $card.toggleClass('selected');
            
            // Update competition button state
            const selectedCount = $('.vision-model-card.selected').length;
            $('.model-competition-btn').prop('disabled', selectedCount < 2);
        },
        
        /**
         * Handle user creation
         */
        handleUserCreation: function(e) {
            e.preventDefault();
            
            const $button = $(e.currentTarget);
            this.showLoadingState($button, 'Creating user...');
            
            VisionFramework.ajaxRequest('create_user', {})
            .done((response) => {
                if (response.success) {
                    VisionFramework.showNotice(response.data.message, 'success');
                    if (response.data.reload_page) {
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    }
                } else {
                    VisionFramework.showNotice(response.data.error || 'User creation failed', 'error');
                }
            })
            .always(() => {
                this.hideLoadingState($button);
            });
        },
        
        /**
         * Handle user assignment
         */
        handleUserAssignment: function(e) {
            e.preventDefault();
            
            const $select = $('#user-assignment-select');
            const userId = $select.val();
            
            if (!userId) {
                VisionFramework.showNotice('Please select a user', 'warning');
                return;
            }
            
            const $button = $(e.currentTarget);
            this.showLoadingState($button, 'Assigning user...');
            
            VisionFramework.ajaxRequest('assign_user', {
                user_id: userId
            })
            .done((response) => {
                if (response.success) {
                    VisionFramework.showNotice(response.data.message, 'success');
                    if (response.data.reload_page) {
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    }
                } else {
                    VisionFramework.showNotice(response.data.error || 'User assignment failed', 'error');
                }
            })
            .always(() => {
                this.hideLoadingState($button);
            });
        },
        
        /**
         * Handle workflow step
         */
        handleWorkflowStep: function(e) {
            e.preventDefault();
            
            const $button = $(e.currentTarget);
            const action = $button.data('action');
            
            if (action) {
                // Delegate to button action handler
                VisionFramework.handleButtonAction.call($button[0], e);
            }
        },
        
        /**
         * Handle status filter
         */
        handleStatusFilter: function(e) {
            const status = $(e.currentTarget).val();
            
            $('.article-item').each(function() {
                const $item = $(this);
                const itemStatus = $item.data('status');
                
                if (status === '' || itemStatus === status) {
                    $item.show();
                } else {
                    $item.hide();
                }
            });
        },
        
        /**
         * Handle model filter
         */
        handleModelFilter: function(e) {
            const model = $(e.currentTarget).val();
            
            $('.article-item').each(function() {
                const $item = $(this);
                const itemModel = $item.data('model');
                
                if (model === '' || itemModel === model) {
                    $item.show();
                } else {
                    $item.hide();
                }
            });
        },
        
        /**
         * Save keywords
         */
        saveKeywords: function(e) {
            const $input = $(e.currentTarget);
            const pageId = $input.data('page-id');
            const keywords = $input.val();
            
            if (!pageId) return;
            
            VisionFramework.ajaxRequest('save_keywords', {
                page_id: pageId,
                keywords: keywords
            });
        },
        
        /**
         * Show keyword suggestions
         */
        showKeywordSuggestions: function(e) {
            // Placeholder for keyword suggestions functionality
        },

        /**
         * Initialize model management interface
         */
        initModelManagement: function() {
            const self = this;

            // Provider selection change
            $(document).on('change', '#provider-selector', function() {
                const provider = $(this).val();
                if (provider) {
                    self.loadProviderModels(provider);
                    $('#refresh-provider-models').show().data('provider', provider);
                } else {
                    $('#models-list').html('<p class="no-provider">Select a provider to see available models</p>');
                    $('#refresh-provider-models').hide();
                }
            });

            // Model selection
            $(document).on('click', '.model-item', function() {
                const modelId = $(this).data('model-id');
                const modelName = $(this).data('model-name');
                const provider = $(this).data('provider');

                self.addModelToPool(provider, modelId, modelName);
            });

            // Remove model from pool
            $(document).on('click', '.selected-model-remove', function(e) {
                e.preventDefault();
                $(this).closest('.selected-model-card').remove();
                self.saveSelectedModels();
            });

            // Load initial selected models
            this.loadSelectedModels();
        },

        /**
         * Load models for selected provider
         */
        loadProviderModels: function(provider) {
            const $modelsList = $('#models-list');
            $modelsList.html('<div class="loading-spinner"></div><p>Loading models...</p>');

            // Try to get from cache first
            const cachedModels = this.getCachedModels(provider);
            if (cachedModels && cachedModels.length > 0) {
                this.renderModelsList(cachedModels);
                return;
            }

            // Fetch fresh models
            VisionFramework.ajaxRequest('refresh_models', {provider: provider})
                .done((response) => {
                    if (response.success && response.data.providers && response.data.providers[provider]) {
                        const models = response.data.providers[provider].models;
                        this.renderModelsList(models);
                    } else {
                        $modelsList.html('<p class="no-provider">No models found for this provider</p>');
                    }
                })
                .fail(() => {
                    $modelsList.html('<p class="no-provider">Failed to load models</p>');
                });
        },

        /**
         * Render models list
         */
        renderModelsList: function(models) {
            const $modelsList = $('#models-list');
            let html = '';

            models.forEach(model => {
                html += `
                    <div class="model-item" data-model-id="${model.id}" data-model-name="${model.name}" data-provider="${model.provider}">
                        <span class="model-name">${model.name}</span>
                        <span class="model-provider-badge">${model.provider}</span>
                    </div>
                `;
            });

            $modelsList.html(html || '<p class="no-provider">No models available</p>');
        },

        /**
         * Add model to selected pool
         */
        addModelToPool: function(provider, modelId, modelName) {
            const $selectedModels = $('#selected-models');

            // Check if already selected
            if ($selectedModels.find(`[data-model-id="${modelId}"]`).length > 0) {
                VisionFramework.showNotice('Model already selected', 'warning');
                return;
            }

            // Remove "no models" message
            $selectedModels.find('.no-models').remove();

            // Add model card
            const modelCard = `
                <div class="selected-model-card" data-model-id="${modelId}" data-provider="${provider}">
                    <div class="selected-model-info">
                        <span class="model-provider-badge">${provider}</span>
                        <span class="model-name">${modelName}</span>
                    </div>
                    <button type="button" class="selected-model-remove" title="Remove model">×</button>
                </div>
            `;

            $selectedModels.append(modelCard);
            this.saveSelectedModels();
            VisionFramework.showNotice(`${modelName} added to pool`, 'success');
        },

        /**
         * Save selected models to database
         */
        saveSelectedModels: function() {
            const selectedModels = [];

            $('#selected-models .selected-model-card').each(function() {
                selectedModels.push({
                    provider: $(this).data('provider'),
                    model_id: $(this).data('model-id'),
                    label: $(this).find('.model-name').text()
                });
            });

            // Save to database
            VisionFramework.ajaxRequest('save_selected_models', {
                selected_models: selectedModels
            });
        },

        /**
         * Load selected models from database
         */
        loadSelectedModels: function() {
            VisionFramework.ajaxRequest('get_selected_models')
                .done((response) => {
                    if (response.success && response.data.models) {
                        this.renderSelectedModels(response.data.models);
                    }
                });
        },

        /**
         * Render selected models
         */
        renderSelectedModels: function(models) {
            const $selectedModels = $('#selected-models');
            $selectedModels.empty();

            if (models.length === 0) {
                $selectedModels.html('<p class="no-models">No models selected yet</p>');
                return;
            }

            models.forEach(model => {
                const modelCard = `
                    <div class="selected-model-card" data-model-id="${model.model_id}" data-provider="${model.provider}">
                        <div class="selected-model-info">
                            <span class="model-provider-badge">${model.provider}</span>
                            <span class="model-name">${model.label}</span>
                        </div>
                        <button type="button" class="selected-model-remove" title="Remove model">×</button>
                    </div>
                `;
                $selectedModels.append(modelCard);
            });
        },

        /**
         * Get cached models for provider
         */
        getCachedModels: function(provider) {
            // This would normally get from localStorage or a global variable
            // For now, return empty to force fresh fetch
            return null;
        },

        /**
         * Handle models refresh response
         */
        handleModelsRefresh: function(data) {
            if (data.providers_updated && data.total_models) {
                // If a provider is currently selected, refresh its models
                const selectedProvider = $('#provider-selector').val();
                if (selectedProvider) {
                    this.loadProviderModels(selectedProvider);
                }
            }
        },
        
        /**
         * Update bulk actions
         */
        updateBulkActions: function(e) {
            const checkedCount = $('.page-checkbox:checked').length;
            $('.bulk-action-btn').prop('disabled', checkedCount === 0);
        },
        
        /**
         * Toggle all pages
         */
        toggleAllPages: function(e) {
            const checked = $(e.currentTarget).prop('checked');
            $('.page-checkbox').prop('checked', checked);
            this.updateBulkActions();
        },
        
        /**
         * Next workflow step
         */
        nextWorkflowStep: function(e) {
            e.preventDefault();
            
            if (this.currentStep < this.totalSteps) {
                this.currentStep++;
                this.updateWorkflowDisplay();
            }
        },
        
        /**
         * Previous workflow step
         */
        prevWorkflowStep: function(e) {
            e.preventDefault();
            
            if (this.currentStep > 1) {
                this.currentStep--;
                this.updateWorkflowDisplay();
            }
        },
        
        /**
         * Update cost estimation
         */
        updateCostEstimation: function(e) {
            const wordCount = parseInt($('#word-count-input').val()) || 1000;
            const selectedModels = $('.model-checkbox:checked').length;
            
            // Rough estimation: $0.001 per 1000 words
            const estimatedCost = (wordCount / 1000) * 0.001 * selectedModels;
            
            $('.cost-estimation').text(`~$${estimatedCost.toFixed(4)}`);
        },
        
        /**
         * Preview internal links
         */
        previewInternalLinks: function(e) {
            e.preventDefault();
            
            const content = $('#article-content').val();
            if (!content) {
                VisionFramework.showNotice('No content to preview', 'warning');
                return;
            }
            
            VisionFramework.ajaxRequest('preview_links', {
                content: content
            })
            .done((response) => {
                if (response.success) {
                    this.displayLinkPreview(response.data);
                }
            });
        },
        
        /**
         * Check workflow completion
         */
        checkWorkflowCompletion: function() {
            // Check if all required steps are completed
        },
        
        /**
         * Update progress indicators
         */
        updateProgressIndicators: function() {
            // Update workflow progress indicators
        },
        
        /**
         * Load available users
         */
        loadAvailableUsers: function() {
            // Load users for assignment dropdown
        },
        
        /**
         * Show user creation modal
         */
        showUserCreationModal: function(e) {
            // Handled by framework now
        },
        
        /**
         * Update workflow display
         */
        updateWorkflowDisplay: function() {
            $('.workflow-step').removeClass('active');
            $(`.workflow-step[data-step="${this.currentStep}"]`).addClass('active');
        },
        
        /**
         * Display link preview
         */
        displayLinkPreview: function(data) {
            const $preview = $('.link-preview-container');
            if ($preview.length) {
                $preview.html(data.preview_html || 'No links found');
            }
        },

        /**
         * Display generated article
         */
        displayGeneratedArticle: function(articleData) {
            const $output = $('.article-output');
            $output.html(`
                <div class="generated-article">
                    <h3>${articleData.title}</h3>
                    <div class="article-meta">
                        <span>Model: ${articleData.model}</span>
                        <span>Words: ${articleData.word_count}</span>
                        <span>Generation time: ${articleData.generation_time}s</span>
                    </div>
                    <div class="article-content">${articleData.content}</div>
                    <div class="article-actions">
                        <button class="vision-button vision-button-primary publish-article-btn" data-article-id="${articleData.id}">
                            Publish to WordPress
                        </button>
                        <button class="vision-button vision-button-secondary preview-article-btn" data-article-id="${articleData.id}">
                            Preview
                        </button>
                    </div>
                </div>
            `).show();
        },

        /**
         * Handle draft card selection
         */
        handleDraftSelection: function(e) {
            if ($(e.target).hasClass('draft-select-btn') || $(e.target).hasClass('rating-star')) {
                return; // Let specific handlers deal with these
            }

            const $card = $(this);
            $('.draft-card').removeClass('selected');
            $card.addClass('selected');
        },

        /**
         * Handle draft confirmation
         */
        handleDraftConfirmation: function(e) {
            e.preventDefault();
            e.stopPropagation();

            const $button = $(this);
            const draftIndex = $button.data('draft-index');

            // Get all draft data (this would be stored when drafts are displayed)
            const drafts = window.currentDrafts || [];
            const topic = $('#article-topic').val();

            if (drafts[draftIndex]) {
                Framework.ajaxRequest('select_draft', {
                    draft_index: draftIndex,
                    drafts: drafts,
                    topic: topic
                })
                .done(function(response) {
                    if (response.success) {
                        Framework.showNotice('Draft selected successfully!', 'success');
                        // Move to preview step
                        Framework.goToPreviewStep();
                    } else {
                        Framework.showNotice(response.data.error || 'Failed to select draft.', 'error');
                    }
                })
                .fail(function() {
                    Framework.showNotice('Failed to select draft. Please try again.', 'error');
                });
            }
        },

        /**
         * Handle star rating clicks
         */
        handleStarRating: function(e) {
            e.preventDefault();
            e.stopPropagation();

            const $star = $(this);
            const rating = $star.data('rating');
            const $container = $star.closest('.draft-rating');

            // Update visual rating
            $container.find('.rating-star').each(function(index) {
                $(this).toggleClass('active', index < rating);
            });
        },

        /**
         * Show all drafts
         */
        showAllDrafts: function(e) {
            e.preventDefault();
            $('.draft-card').removeClass('hidden').show();
        },

        /**
         * Hide weak drafts (low scores)
         */
        hideWeakDrafts: function(e) {
            e.preventDefault();
            $('.draft-card').each(function() {
                const $card = $(this);
                const seoScore = parseInt($card.find('.draft-metrics span:contains("SEO:")').text().match(/\d+/)?.[0] || 0);
                const readabilityScore = parseInt($card.find('.draft-metrics span:contains("Readability:")').text().match(/\d+/)?.[0] || 0);

                if (seoScore < 60 || readabilityScore < 60) {
                    $card.addClass('hidden').hide();
                }
            });
        },

        /**
         * Show only favorites (high-rated drafts)
         */
        showOnlyFavorites: function(e) {
            e.preventDefault();
            $('.draft-card').each(function() {
                const $card = $(this);
                const activeStars = $card.find('.rating-star.active').length;

                if (activeStars >= 4) {
                    $card.removeClass('hidden').show();
                } else {
                    $card.addClass('hidden').hide();
                }
            });
        },

        /**
         * Handle metric toggle changes
         */
        handleMetricToggle: function(e) {
            const $checkbox = $(this);
            const metricType = $checkbox.attr('id').replace('show-', '');
            const isChecked = $checkbox.is(':checked');

            // Toggle visibility of metric in all draft cards
            $('.draft-metrics span').each(function() {
                const $span = $(this);
                const text = $span.text().toLowerCase();

                if ((metricType === 'word-count' && text.includes('words')) ||
                    (metricType === 'readability' && text.includes('readability')) ||
                    (metricType === 'seo-score' && text.includes('seo')) ||
                    (metricType === 'internal-links' && text.includes('links'))) {
                    $span.toggle(isChecked);
                }
            });
        },

        /**
         * Handle image generation button click
         */
        handleImageGenerationClick: function(e) {
            e.preventDefault();
            const $button = $(this);
            const placeholderId = $button.data('placeholder-id');
            const service = $button.data('service') || 'dalle';
            const style = $button.data('style') || 'natural';
            const size = $button.data('size') || '1024x1024';

            if (!placeholderId) {
                Framework.showNotice('Placeholder ID is required.', 'error');
                return;
            }

            const data = {
                placeholder_id: placeholderId,
                service: service,
                style: style,
                size: size
            };

            Framework.ajaxRequest('generate_placeholder_image', data)
                .done(function(response) {
                    if (response.success) {
                        Framework.handleImageGeneration(response.data, $button);
                    } else {
                        Framework.showNotice(response.data.error || 'Failed to generate image.', 'error');
                    }
                })
                .fail(function() {
                    Framework.showNotice('Failed to generate image. Please try again.', 'error');
                });
        },

        /**
         * Handle image selection button click
         */
        handleImageSelectionClick: function(e) {
            e.preventDefault();
            const $button = $(this);
            const placeholderId = $button.data('placeholder-id');
            const imageUrl = $button.data('image-url');
            const provider = $button.data('provider');
            const attachmentId = $button.data('attachment-id');

            if (!placeholderId || !imageUrl) {
                Framework.showNotice('Placeholder ID and image URL are required.', 'error');
                return;
            }

            const data = {
                placeholder_id: placeholderId,
                image_url: imageUrl,
                provider: provider,
                attachment_id: attachmentId
            };

            Framework.ajaxRequest('update_placeholder_selection', data)
                .done(function(response) {
                    if (response.success) {
                        Framework.handlePlaceholderSelection(response.data, $button);
                    } else {
                        Framework.showNotice(response.data.error || 'Failed to select image.', 'error');
                    }
                })
                .fail(function() {
                    Framework.showNotice('Failed to select image. Please try again.', 'error');
                });
        },

        /**
         * Navigate to topics step
         */
        goToTopicsStep: function(e) {
            e.preventDefault();
            Framework.activateStep(2);
        },

        /**
         * Navigate to generation step
         */
        goToGenerationStep: function(e) {
            e.preventDefault();
            Framework.activateStep(3);
        },

        /**
         * Navigate to preview step
         */
        goToPreviewStep: function(e) {
            e.preventDefault();
            Framework.activateStep(5);
        },

        /**
         * Activate a specific step in the builder workflow
         */
        activateStep: function(stepNumber) {
            $('.vision-step-card').removeClass('active');
            $(`#step-${stepNumber}`).addClass('active');

            // Scroll to the active step
            $(`#step-${stepNumber}`)[0].scrollIntoView({ behavior: 'smooth' });
        },

        /**
         * Show tooltip
         */
        showTooltip: function($element) {
            const text = $element.data('tooltip');
            if (!text) return;

            const $tooltip = $('<div class="vision-tooltip">' + text + '</div>');
            $('body').append($tooltip);

            const offset = $element.offset();
            $tooltip.css({
                top: offset.top - $tooltip.outerHeight() - 10,
                left: offset.left + ($element.outerWidth() / 2) - ($tooltip.outerWidth() / 2)
            }).fadeIn(200);
        },

        /**
         * Hide tooltip
         */
        hideTooltip: function() {
            $('.vision-tooltip').fadeOut(200, function() {
                $(this).remove();
            });
        },

        /**
         * Copy text to clipboard
         */
        copyToClipboard: function($element) {
            const text = $element.data('copy-text') || $element.text();

            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    this.showNotice('Copied to clipboard!', 'success');
                }).catch(() => {
                    this.fallbackCopyToClipboard(text);
                });
            } else {
                this.fallbackCopyToClipboard(text);
            }
        },

        /**
         * Fallback copy to clipboard method
         */
        fallbackCopyToClipboard: function(text) {
            const $temp = $('<textarea>');
            $('body').append($temp);
            $temp.val(text).select();

            try {
                document.execCommand('copy');
                this.showNotice('Copied to clipboard!', 'success');
            } catch (err) {
                this.showNotice('Failed to copy to clipboard', 'error');
            }

            $temp.remove();
        }
    };

    // Export both objects first
    window.VisionFramework = Framework;
    window.BlogWriter = BlogWriter;

    // Initialize both Framework and Blog Writer functionality when document is ready
    $(document).ready(function() {
        console.log('🚀 Document Ready - Starting initialization...');
        console.log('🔍 Global objects check:', {
            jQuery: typeof jQuery,
            $: typeof $,
            visionFramework: typeof visionFramework,
            VisionFramework: typeof window.VisionFramework,
            BlogWriter: typeof window.BlogWriter
        });

        // Initialize VisionFramework
        try {
            if (window.VisionFramework && typeof window.VisionFramework.init === 'function') {
                console.log('✅ Initializing VisionFramework...');
                window.VisionFramework.init();
            } else {
                console.error('❌ VisionFramework not available or init method missing');
            }
        } catch (error) {
            console.error('❌ Error initializing VisionFramework:', error);
        }

        // Initialize BlogWriter
        try {
            if (window.BlogWriter && typeof window.BlogWriter.init === 'function') {
                console.log('✅ Initializing BlogWriter...');
                window.BlogWriter.init();
            } else {
                console.error('❌ BlogWriter not available or init method missing');
            }
        } catch (error) {
            console.error('❌ Error initializing BlogWriter:', error);
        }

        // Debug info for visionFramework (WordPress localized data)
        if (typeof visionFramework === 'undefined') {
            console.warn('⚠️ visionFramework (WordPress localized data) not found - AJAX may not work');
        } else {
            console.log('✅ visionFramework found:', visionFramework);
        }
    });

})(jQuery);