<?php
/**
 * API Integration Handler
 * 
 * Centralized handler for all AI service API integrations with enhanced error handling,
 * rate limiting, and monitoring capabilities
 */

namespace BlogWriter;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ApiIntegrationHandler Class
 */
class ApiIntegrationHandler
{
    /**
     * Database handler instance
     * 
     * @var BlogWriterDatabaseHandler
     */
    protected $databaseHandler;
    
    /**
     * Plugin configuration
     * 
     * @var array
     */
    protected $config;
    
    /**
     * Rate limiting cache
     * 
     * @var array
     */
    protected $rateLimitCache = [];
    
    /**
     * API providers configuration
     * 
     * @var array
     */
    protected $providers = [
        'openrouter' => [
            'name' => 'OpenRouter',
            'base_url' => 'https://openrouter.ai/api/v1',
            'rate_limit' => [
                'requests_per_minute' => 60,
                'requests_per_hour' => 3600
            ],
            'default_timeout' => 60,
            'retry_attempts' => 10,
            'retry_delay' => 2
        ],
        'openai' => [
            'name' => 'OpenAI',
            'base_url' => 'https://api.openai.com/v1',
            'rate_limit' => [
                'requests_per_minute' => 50,
                'requests_per_hour' => 3000
            ],
            'default_timeout' => 60,
            'retry_attempts' => 10,
            'retry_delay' => 1
        ],
        'anthropic' => [
            'name' => 'Anthropic',
            'base_url' => 'https://api.anthropic.com/v1',
            'rate_limit' => [
                'requests_per_minute' => 40,
                'requests_per_hour' => 2400
            ],
            'default_timeout' => 60,
            'retry_attempts' => 10,
            'retry_delay' => 2
        ],
        'grok' => [
            'name' => 'Grok (X.AI)',
            'base_url' => 'https://api.x.ai/v1',
            'rate_limit' => [
                'requests_per_minute' => 30,
                'requests_per_hour' => 1800
            ],
            'default_timeout' => 60,
            'retry_attempts' => 10,
            'retry_delay' => 2
        ],
        'google' => [
            'name' => 'Google Gemini',
            'base_url' => 'https://generativelanguage.googleapis.com/v1beta',
            'rate_limit' => [
                'requests_per_minute' => 60,
                'requests_per_hour' => 3600
            ],
            'default_timeout' => 60,
            'retry_attempts' => 10,
            'retry_delay' => 1
        ],
        'groq' => [
            'name' => 'Groq',
            'base_url' => 'https://api.groq.com/openai/v1',
            'rate_limit' => [
                'requests_per_minute' => 30,
                'requests_per_hour' => 1800
            ],
            'default_timeout' => 30,
            'retry_attempts' => 3,
            'retry_delay' => 1
        ]
    ];
    
    /**
     * Constructor
     * 
     * @param BlogWriterDatabaseHandler $databaseHandler
     * @param array $config
     */
    public function __construct($databaseHandler, $config)
    {
        $this->databaseHandler = $databaseHandler;
        $this->config = $config;
        
        // Initialize rate limiting
        $this->initRateLimiting();
    }
    
    /**
     * Make API request with enhanced error handling and rate limiting
     * 
     * @param string $provider Provider identifier
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @param array $options Request options
     * @return array|WP_Error
     */
    public function makeRequest($provider, $endpoint, $data = [], $options = [])
    {
        $start_time = microtime(true);
        
        // Validate provider
        if (!isset($this->providers[$provider])) {
            return new \WP_Error('invalid_provider', "Unknown API provider: {$provider}");
        }
        
        $provider_config = $this->providers[$provider];
        
        // Check rate limiting
        $rate_limit_check = $this->checkRateLimit($provider);
        if (is_wp_error($rate_limit_check)) {
            return $rate_limit_check;
        }
        
        // Get API key
        $api_key = $this->getApiKey($provider);
        if (is_wp_error($api_key)) {
            return $api_key;
        }
        
        // Build request URL
        $url = $provider_config['base_url'] . '/' . ltrim($endpoint, '/');
        
        // Special handling for Google Gemini - add API key as query parameter
        if ($provider === 'google' && $api_key) {
            $url = add_query_arg('key', $api_key, $url);
        }
        
        // Prepare headers
        $headers = $this->buildHeaders($provider, $api_key, $options);
        
        // Prepare request options
        $request_options = [
            'headers' => $headers,
            'timeout' => $options['timeout'] ?? $provider_config['default_timeout'],
            'method' => $options['method'] ?? 'POST'
        ];
        
        if (!empty($data)) {
            $request_options['body'] = wp_json_encode($data);
        }
        
        // Execute request with retry logic
        $response = $this->executeWithRetry($url, $request_options, $provider_config);
        
        $request_time = microtime(true) - $start_time;
        
        // Update rate limiting
        $this->updateRateLimit($provider);
        
        // Log request
        $this->logApiRequest($provider, $endpoint, $request_time, $response);
        
        return $this->processResponse($response, $provider, $endpoint);
    }
    
    /**
     * Test API connection and capabilities
     * 
     * @param string $provider Provider identifier
     * @param string $api_key Optional API key override
     * @return array
     */
    public function testConnection($provider, $api_key = null)
    {
        $start_time = microtime(true);
        
        if (!isset($this->providers[$provider])) {
            return [
                'success' => false,
                'error' => "Unknown provider: {$provider}"
            ];
        }
        
        // Use provided key or get from settings
        if ($api_key === null) {
            $api_key_result = $this->getApiKey($provider);
            if (is_wp_error($api_key_result)) {
                return [
                    'success' => false,
                    'error' => $api_key_result->get_error_message()
                ];
            }
            $api_key = $api_key_result;
        }
        
        try {
            // Test with a simple request based on provider
            switch ($provider) {
                case 'openrouter':
                    $result = $this->testOpenRouterConnection($api_key);
                    break;
                case 'openai':
                    $result = $this->testOpenAIConnection($api_key);
                    break;
                case 'anthropic':
                    $result = $this->testAnthropicConnection($api_key);
                    break;
                case 'grok':
                    $result = $this->testGrokConnection($api_key);
                    break;
                case 'google':
                    $result = $this->testGoogleConnection($api_key);
                    break;
                case 'groq':
                    $result = $this->testGroqConnection($api_key);
                    break;
                default:
                    return [
                        'success' => false,
                        'error' => "No test method available for provider: {$provider}"
                    ];
            }
            
            $test_time = microtime(true) - $start_time;
            
            if ($result['success']) {
                $this->databaseHandler->log('info', 'API connection test successful', [
                    'provider' => $provider,
                    'response_time' => round($test_time, 3)
                ]);
                
                return [
                    'success' => true,
                    'message' => "Connection to {$this->providers[$provider]['name']} successful",
                    'response_time' => round($test_time, 3),
                    'provider_info' => $result['provider_info'] ?? []
                ];
            } else {
                return $result;
            }
            
        } catch (Exception $e) {
            $this->databaseHandler->log('error', 'API connection test failed', [
                'provider' => $provider,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => "Connection test failed: " . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get API usage statistics
     * 
     * @param string $provider Optional provider filter
     * @param string $period Time period (day, week, month)
     * @return array
     */
    public function getUsageStats($provider = null, $period = 'day')
    {
        $period_mapping = [
            'day' => '1 DAY',
            'week' => '7 DAY',
            'month' => '30 DAY'
        ];
        
        if (!isset($period_mapping[$period])) {
            $period = 'day';
        }
        
        $interval = $period_mapping[$period];
        
        // Get logs from database
        $logs = $this->databaseHandler->getLogs([
            'level' => 'info',
            'message_like' => 'API request',
            'since' => date('Y-m-d H:i:s', strtotime("-{$interval}")),
            'limit' => 1000
        ]);
        
        $stats = [
            'period' => $period,
            'total_requests' => 0,
            'successful_requests' => 0,
            'failed_requests' => 0,
            'average_response_time' => 0,
            'providers' => [],
            'error_types' => []
        ];
        
        $response_times = [];
        
        foreach ($logs as $log) {
            $log_data = json_decode($log['metadata'], true) ?? [];
            
            if ($provider && isset($log_data['provider']) && $log_data['provider'] !== $provider) {
                continue;
            }
            
            $stats['total_requests']++;
            
            if (isset($log_data['provider'])) {
                $log_provider = $log_data['provider'];
                
                if (!isset($stats['providers'][$log_provider])) {
                    $stats['providers'][$log_provider] = [
                        'requests' => 0,
                        'successful' => 0,
                        'failed' => 0,
                        'avg_response_time' => 0
                    ];
                }
                
                $stats['providers'][$log_provider]['requests']++;
                
                if (isset($log_data['response_time'])) {
                    $response_times[] = (float) $log_data['response_time'];
                }
                
                if (isset($log_data['success']) && $log_data['success']) {
                    $stats['successful_requests']++;
                    $stats['providers'][$log_provider]['successful']++;
                } else {
                    $stats['failed_requests']++;
                    $stats['providers'][$log_provider]['failed']++;
                    
                    if (isset($log_data['error_type'])) {
                        $error_type = $log_data['error_type'];
                        $stats['error_types'][$error_type] = ($stats['error_types'][$error_type] ?? 0) + 1;
                    }
                }
            }
        }
        
        // Calculate averages
        if (!empty($response_times)) {
            $stats['average_response_time'] = round(array_sum($response_times) / count($response_times), 3);
        }
        
        foreach ($stats['providers'] as $provider_key => &$provider_stats) {
            $provider_times = array_filter($response_times); // This would need more specific filtering in real implementation
            if (!empty($provider_times)) {
                $provider_stats['avg_response_time'] = round(array_sum($provider_times) / count($provider_times), 3);
            }
        }
        
        return $stats;
    }
    
    /**
     * Initialize rate limiting
     */
    protected function initRateLimiting()
    {
        // Load existing rate limit data from transients
        foreach ($this->providers as $provider_key => $provider_config) {
            $this->rateLimitCache[$provider_key] = [
                'minute_count' => get_transient("blogwriter_rate_limit_{$provider_key}_minute") ?: 0,
                'hour_count' => get_transient("blogwriter_rate_limit_{$provider_key}_hour") ?: 0,
                'last_request' => get_transient("blogwriter_last_request_{$provider_key}") ?: 0
            ];
        }
    }
    
    /**
     * Check rate limiting for provider
     * 
     * @param string $provider
     * @return bool|WP_Error
     */
    protected function checkRateLimit($provider)
    {
        $provider_config = $this->providers[$provider];
        $cache_key = $this->rateLimitCache[$provider] ?? [
            'minute_count' => 0,
            'hour_count' => 0,
            'last_request' => 0
        ];
        
        $now = time();
        $minute_ago = $now - 60;
        $hour_ago = $now - 3600;
        
        // Reset counters if time windows have passed
        if ($cache_key['last_request'] < $minute_ago) {
            $cache_key['minute_count'] = 0;
        }
        
        if ($cache_key['last_request'] < $hour_ago) {
            $cache_key['hour_count'] = 0;
        }
        
        // Check limits
        if ($cache_key['minute_count'] >= $provider_config['rate_limit']['requests_per_minute']) {
            return new \WP_Error('rate_limit_exceeded', 
                sprintf("Rate limit exceeded for %s: %d requests per minute", 
                    $provider_config['name'], 
                    $provider_config['rate_limit']['requests_per_minute']
                )
            );
        }
        
        if ($cache_key['hour_count'] >= $provider_config['rate_limit']['requests_per_hour']) {
            return new \WP_Error('rate_limit_exceeded', 
                sprintf("Rate limit exceeded for %s: %d requests per hour", 
                    $provider_config['name'], 
                    $provider_config['rate_limit']['requests_per_hour']
                )
            );
        }
        
        return true;
    }
    
    /**
     * Update rate limiting counters
     * 
     * @param string $provider
     */
    protected function updateRateLimit($provider)
    {
        $now = time();
        
        if (!isset($this->rateLimitCache[$provider])) {
            $this->rateLimitCache[$provider] = [
                'minute_count' => 0,
                'hour_count' => 0,
                'last_request' => 0
            ];
        }
        
        $this->rateLimitCache[$provider]['minute_count']++;
        $this->rateLimitCache[$provider]['hour_count']++;
        $this->rateLimitCache[$provider]['last_request'] = $now;
        
        // Store in transients
        set_transient("blogwriter_rate_limit_{$provider}_minute", $this->rateLimitCache[$provider]['minute_count'], 60);
        set_transient("blogwriter_rate_limit_{$provider}_hour", $this->rateLimitCache[$provider]['hour_count'], 3600);
        set_transient("blogwriter_last_request_{$provider}", $now, 3600);
    }
    
    /**
     * Get API key for provider
     * 
     * @param string $provider
     * @return string|WP_Error
     */
    protected function getApiKey($provider)
    {
        switch ($provider) {
            case 'openrouter':
                $api_key = $this->databaseHandler->getBlogSetting('openrouter_api_key', '');
                break;
            case 'openai':
                $api_key = $this->databaseHandler->getBlogSetting('openai_api_key', '');
                break;
            case 'anthropic':
                $api_key = $this->databaseHandler->getBlogSetting('anthropic_api_key', '');
                break;
            case 'grok':
                $api_key = $this->databaseHandler->getBlogSetting('grok_api_key', '');
                break;
            case 'google':
                $api_key = $this->databaseHandler->getBlogSetting('google_api_key', '');
                break;
            case 'groq':
                $api_key = $this->databaseHandler->getBlogSetting('groq_api_key', '');
                break;
            default:
                return new \WP_Error('unsupported_provider', "No API key configuration for provider: {$provider}");
        }
        
        if (empty($api_key)) {
            return new \WP_Error('missing_api_key', 
                sprintf("API key not configured for %s", $this->providers[$provider]['name'])
            );
        }
        
        return $api_key;
    }
    
    /**
     * Build request headers
     * 
     * @param string $provider
     * @param string $api_key
     * @param array $options
     * @return array
     */
    protected function buildHeaders($provider, $api_key, $options = [])
    {
        $headers = [
            'Content-Type' => 'application/json',
            'User-Agent' => 'BlogWriter/' . BLOG_WRITER_VERSION . ' (WordPress/' . get_bloginfo('version') . ')',
            'HTTP-Referer' => home_url(),
            'X-Title' => get_bloginfo('name') . ' - Blog Writer Plugin'
        ];
        
        // Provider-specific authorization
        switch ($provider) {
            case 'openrouter':
            case 'openai':
            case 'grok':
            case 'groq':
                $headers['Authorization'] = 'Bearer ' . $api_key;
                break;
            case 'anthropic':
                $headers['x-api-key'] = $api_key;
                $headers['anthropic-version'] = '2023-06-01';
                break;
            case 'google':
                // Google Gemini uses query parameter for API key
                // We'll add it to the URL in the makeRequest method
                break;
        }
        
        // Merge custom headers
        if (isset($options['headers']) && is_array($options['headers'])) {
            $headers = array_merge($headers, $options['headers']);
        }
        
        return $headers;
    }
    
    /**
     * Execute request with retry logic
     * 
     * @param string $url
     * @param array $options
     * @param array $provider_config
     * @return array|WP_Error
     */
    protected function executeWithRetry($url, $options, $provider_config)
    {
        $max_attempts = $provider_config['retry_attempts'];
        $retry_delay = $provider_config['retry_delay'];
        
        for ($attempt = 1; $attempt <= $max_attempts; $attempt++) {
            $response = wp_remote_request($url, $options);
            
            if (!is_wp_error($response)) {
                $response_code = wp_remote_retrieve_response_code($response);
                
                // Success or client error (don't retry client errors)
                if ($response_code < 500) {
                    return $response;
                }
            }
            
            // If this isn't the last attempt, wait before retrying
            if ($attempt < $max_attempts) {
                sleep($retry_delay);
                $retry_delay *= 2; // Exponential backoff
            }
        }
        
        return $response; // Return the last failed response
    }
    
    /**
     * Process API response
     * 
     * @param array|WP_Error $response
     * @param string $provider
     * @param string $endpoint
     * @return array|WP_Error
     */
    protected function processResponse($response, $provider, $endpoint)
    {
        if (is_wp_error($response)) {
            return new \WP_Error('request_failed', 
                sprintf("Request to %s failed: %s", $provider, $response->get_error_message())
            );
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        // Parse JSON response
        $data = json_decode($response_body, true);
        
        if ($response_code >= 400) {
            $error_message = 'Unknown error';
            
            if ($data && isset($data['error'])) {
                if (is_string($data['error'])) {
                    $error_message = $data['error'];
                } elseif (isset($data['error']['message'])) {
                    $error_message = $data['error']['message'];
                }
            }
            
            return new \WP_Error('api_error', 
                sprintf("API error (%d): %s", $response_code, $error_message),
                ['response_code' => $response_code, 'response_data' => $data]
            );
        }
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new \WP_Error('invalid_json', 
                sprintf("Invalid JSON response from %s: %s", $provider, json_last_error_msg())
            );
        }
        
        return [
            'success' => true,
            'data' => $data,
            'response_code' => $response_code
        ];
    }
    
    /**
     * Log API request
     * 
     * @param string $provider
     * @param string $endpoint
     * @param float $request_time
     * @param array|WP_Error $response
     */
    protected function logApiRequest($provider, $endpoint, $request_time, $response)
    {
        $success = !is_wp_error($response) && isset($response['success']) && $response['success'];
        
        $log_data = [
            'provider' => $provider,
            'endpoint' => $endpoint,
            'response_time' => round($request_time, 3),
            'success' => $success
        ];
        
        if (!$success) {
            if (is_wp_error($response)) {
                $log_data['error'] = $response->get_error_message();
                $log_data['error_type'] = $response->get_error_code();
            } elseif (isset($response['data']['error'])) {
                $log_data['error'] = $response['data']['error'];
            }
        }
        
        $this->databaseHandler->log(
            $success ? 'info' : 'warning',
            sprintf('API request to %s %s', $provider, $success ? 'successful' : 'failed'),
            $log_data
        );
    }
    
    /**
     * Test OpenRouter connection
     * 
     * @param string $api_key
     * @return array
     */
    protected function testOpenRouterConnection($api_key)
    {
        $url = 'https://openrouter.ai/api/v1/models';
        
        $response = wp_remote_get($url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $api_key,
                'HTTP-Referer' => home_url()
            ],
            'timeout' => 10
        ]);
        
        if (is_wp_error($response)) {
            return [
                'success' => false,
                'error' => $response->get_error_message()
            ];
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code === 200) {
            $body = json_decode(wp_remote_retrieve_body($response), true);
            $models_count = isset($body['data']) ? count($body['data']) : 0;
            
            return [
                'success' => true,
                'provider_info' => [
                    'models_available' => $models_count,
                    'service' => 'OpenRouter API'
                ]
            ];
        } else {
            return [
                'success' => false,
                'error' => "HTTP {$response_code}: Authentication failed"
            ];
        }
    }
    
    /**
     * Test OpenAI connection
     * 
     * @param string $api_key
     * @return array
     */
    protected function testOpenAIConnection($api_key)
    {
        $url = 'https://api.openai.com/v1/models';
        
        $response = wp_remote_get($url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $api_key
            ],
            'timeout' => 10
        ]);
        
        if (is_wp_error($response)) {
            return [
                'success' => false,
                'error' => $response->get_error_message()
            ];
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code === 200) {
            return [
                'success' => true,
                'provider_info' => [
                    'service' => 'OpenAI API'
                ]
            ];
        } else {
            return [
                'success' => false,
                'error' => "HTTP {$response_code}: Authentication failed"
            ];
        }
    }
    
    /**
     * Test Anthropic connection
     * 
     * @param string $api_key
     * @return array
     */
    protected function testAnthropicConnection($api_key)
    {
        // Anthropic doesn't have a simple test endpoint, so we'll make a minimal completion request
        $url = 'https://api.anthropic.com/v1/messages';
        
        $response = wp_remote_post($url, [
            'headers' => [
                'x-api-key' => $api_key,
                'anthropic-version' => '2023-06-01',
                'Content-Type' => 'application/json'
            ],
            'body' => wp_json_encode([
                'model' => 'claude-3-haiku-20240307',
                'max_tokens' => 10,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => 'Hello'
                    ]
                ]
            ]),
            'timeout' => 10
        ]);
        
        if (is_wp_error($response)) {
            return [
                'success' => false,
                'error' => $response->get_error_message()
            ];
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code === 200) {
            return [
                'success' => true,
                'provider_info' => [
                    'service' => 'Anthropic API'
                ]
            ];
        } else {
            return [
                'success' => false,
                'error' => "HTTP {$response_code}: Authentication failed"
            ];
        }
    }
    
    /**
     * Test Grok connection
     * 
     * @param string $api_key
     * @return array
     */
    protected function testGrokConnection($api_key)
    {
        $url = 'https://api.x.ai/v1/models';
        
        $response = wp_remote_get($url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $api_key
            ],
            'timeout' => 10
        ]);
        
        if (is_wp_error($response)) {
            return [
                'success' => false,
                'error' => $response->get_error_message()
            ];
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code === 200) {
            return [
                'success' => true,
                'provider_info' => [
                    'service' => 'Grok API'
                ]
            ];
        } else {
            return [
                'success' => false,
                'error' => "HTTP {$response_code}: Authentication failed"
            ];
        }
    }
    
    /**
     * Test Google Gemini connection
     * 
     * @param string $api_key
     * @return array
     */
    protected function testGoogleConnection($api_key)
    {
        $url = 'https://generativelanguage.googleapis.com/v1beta/models?key=' . $api_key;
        
        $response = wp_remote_get($url, [
            'timeout' => 10
        ]);
        
        if (is_wp_error($response)) {
            return [
                'success' => false,
                'error' => $response->get_error_message()
            ];
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code === 200) {
            return [
                'success' => true,
                'provider_info' => [
                    'service' => 'Google Gemini API'
                ]
            ];
        } else {
            return [
                'success' => false,
                'error' => "HTTP {$response_code}: Authentication failed"
            ];
        }
    }
    
    /**
     * Test Groq connection
     * 
     * @param string $api_key
     * @return array
     */
    protected function testGroqConnection($api_key)
    {
        $url = 'https://api.groq.com/openai/v1/models';
        
        $response = wp_remote_get($url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $api_key
            ],
            'timeout' => 10
        ]);
        
        if (is_wp_error($response)) {
            return [
                'success' => false,
                'error' => $response->get_error_message()
            ];
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code === 200) {
            return [
                'success' => true,
                'provider_info' => [
                    'service' => 'Groq API'
                ]
            ];
        } else {
            return [
                'success' => false,
                'error' => "HTTP {$response_code}: Authentication failed"
            ];
        }
    }
}