# Blog Writer - AI-Powered WordPress Content Generation Plugin

An advanced WordPress plugin that leverages multiple AI models to generate high-quality blog posts with intelligent internal linking, SEO optimization, and automated content creation workflows.

## Table of Contents

1. [Overview](#overview)
2. [Features](#features)
3. [Installation](#installation)
4. [Configuration](#configuration)
5. [Usage Guide](#usage-guide)
6. [AI Models](#ai-models)
7. [API Integration](#api-integration)
8. [Internal Linking](#internal-linking)
9. [SEO Optimization](#seo-optimization)
10. [Troubleshooting](#troubleshooting)
11. [Development](#development)
12. [Support](#support)

## Overview

Blog Writer is a comprehensive WordPress plugin that transforms your content creation process by:

- **AI-Powered Generation**: Utilizing GPT-4, Claude, Gemini, and other leading AI models
- **Intelligent Internal Linking**: Automatic keyword matching and relevance-based linking
- **SEO Optimization**: Built-in SEO analysis and optimization recommendations
- **Multi-Model Competition**: Compare outputs from different AI models to choose the best content
- **User Management**: Dedicated user creation for article ownership
- **Professional Interface**: Clean, responsive admin interface with Vision Framework

## Features

### 🤖 AI Content Generation
- ✅ Multiple AI model support (GPT-4, Claude 3, Gemini Pro)
- ✅ Topic suggestion and competition between models
- ✅ Customizable content prompts and templates
- ✅ Word count control (100-5000 words)
- ✅ Tone and audience targeting
- ✅ Cost estimation and usage tracking

### 🔗 Intelligent Internal Linking
- ✅ Automatic keyword extraction from existing pages
- ✅ Relevance scoring and smart link placement
- ✅ Link density control and optimization
- ✅ Preview functionality before publishing
- ✅ SEO plugin integration (Yoast, RankMath, AIOSEO)

### 🎨 AI Image Generation
- ✅ DALL-E 3 integration for custom images
- ✅ Automatic WordPress media library upload
- ✅ SEO-optimized alt text generation
- ✅ Custom image prompts and styles

### 📊 Advanced Management
- ✅ Comprehensive dashboard with statistics
- ✅ Generated articles management
- ✅ User assignment and ownership tracking
- ✅ API usage monitoring and rate limiting
- ✅ Detailed logging and error tracking

### 🎯 SEO Optimization
- ✅ Keyword extraction and analysis
- ✅ Content structure optimization
- ✅ Meta description generation
- ✅ Heading structure recommendations
- ✅ Search intent optimization

## Installation

### Requirements
- WordPress 5.0 or higher
- PHP 7.4 or higher
- OpenRouter API account (recommended)
- MySQL 5.7 or higher

### Installation Steps

1. **Download the Plugin**
   ```bash
   # Clone from repository
   git clone https://github.com/lvl3marketing/blog-writer.git
   
   # Or download ZIP and extract
   ```

2. **Upload to WordPress**
   - Upload the `blog-writer` folder to `/wp-content/plugins/`
   - Or install via WordPress admin > Plugins > Add New > Upload

3. **Activate the Plugin**
   - Go to Plugins in WordPress admin
   - Find "Blog Writer" and click Activate

4. **Initial Setup**
   - Navigate to Blog Writer in admin menu
   - Follow the setup wizard to configure API keys and user accounts

## Configuration

### API Setup

1. **OpenRouter API Key**
   - Sign up at [OpenRouter.ai](https://openrouter.ai)
   - Generate an API key in your account dashboard
   - Enter the key in Blog Writer > Settings > API Configuration

2. **Test API Connection**
   - Use the "Test" button to verify your API key
   - Check available models and rate limits

### User Configuration

1. **Create Dedicated User**
   - Use the Dashboard setup wizard
   - Or manually create a user for article ownership
   - Assign appropriate role (Author or Editor recommended)

2. **Set Default Preferences**
   - Default AI model for generation
   - Preferred word count range
   - Content tone and style preferences

## Usage Guide

### 1. Extract Keywords from Existing Pages

```
1. Go to Blog Writer > Page Keywords
2. Select pages to analyze
3. Choose AI model for extraction
4. Click "Extract Keywords" or "Extract All"
5. Review and edit extracted keywords
```

### 2. Generate New Articles

```
1. Navigate to Blog Writer > Article Builder
2. Enter your topic or theme
3. Select AI models for competition
4. Configure settings:
   - Word count (100-5000)
   - Include internal linking
   - Generate AI images
   - Custom prompts (optional)
5. Click "Generate Article"
6. Review generated content
7. Preview internal links
8. Publish to WordPress
```

### 3. Manage Generated Content

```
1. View all generated articles in Generated Articles tab
2. Filter by status (Draft, Published)
3. Edit or republish articles
4. View generation metadata and statistics
```

## AI Models

### Supported Models

| Model | Provider | Strengths | Cost/1K | Best For |
|-------|----------|-----------|---------|----------|
| GPT-4 | OpenAI | Accuracy, creativity | $0.03 | Complex topics, analysis |
| GPT-3.5 Turbo | OpenAI | Speed, reliability | $0.002 | General content, fast generation |
| Claude 3 Opus | Anthropic | Analysis, safety | $0.015 | Long-form, detailed content |
| Claude 3 Sonnet | Anthropic | Balance, efficiency | $0.003 | Versatile content creation |
| Gemini Pro | Google | Factual, cost-effective | $0.0005 | Research-heavy topics |

### Model Selection Tips

- **GPT-4**: Best for creative and complex content requiring high accuracy
- **Claude 3**: Excellent for analytical and detailed content with safety focus
- **Gemini Pro**: Cost-effective for factual and research-based content
- **GPT-3.5 Turbo**: Fast and reliable for general content needs

## API Integration

### OpenRouter Integration

Blog Writer uses OpenRouter as the primary API gateway, providing:

- Access to multiple AI models through a single API
- Competitive pricing and rate limits
- Unified billing and usage tracking
- Fallback options if specific models are unavailable

### Rate Limiting

The plugin implements intelligent rate limiting:

```php
// Default rate limits per provider
'openrouter' => [
    'requests_per_minute' => 60,
    'requests_per_hour' => 3600
],
'openai' => [
    'requests_per_minute' => 50,
    'requests_per_hour' => 3000
]
```

### Error Handling

Comprehensive error handling includes:

- API connection failures
- Rate limit exceeded
- Invalid API keys
- Model unavailability
- Network timeouts

## Internal Linking

### How It Works

1. **Keyword Extraction**: AI analyzes existing pages to extract relevant keywords
2. **Content Analysis**: Generated articles are scanned for keyword opportunities
3. **Relevance Scoring**: Algorithm calculates relevance between content and pages
4. **Smart Placement**: Links are placed naturally within content flow
5. **Density Control**: Ensures optimal link density (configurable maximum)

### Configuration Options

```php
// Internal linking settings
'max_internal_links' => 5,           // Maximum links per article
'min_relevance_score' => 0.7,        // Minimum relevance threshold
'link_strategy' => 'balanced',        // aggressive, conservative, balanced
'avoid_competing_keywords' => true,   // Avoid linking to competing content
```

### Best Practices

- Regularly update keyword extraction
- Review suggested links before publishing
- Monitor link density and user experience
- Use contextually relevant anchor text

## SEO Optimization

### Built-in SEO Features

1. **Content Structure**
   - Proper heading hierarchy (H1, H2, H3)
   - Optimized paragraph length
   - Strategic keyword placement

2. **Meta Optimization**
   - Auto-generated meta descriptions
   - Title tag optimization
   - SEO-friendly URLs

3. **Keyword Analysis**
   - Keyword density analysis
   - LSI keyword suggestions
   - Search intent matching

### SEO Plugin Integration

Compatible with popular SEO plugins:

- **Yoast SEO**: Automatic focus keyword setting
- **RankMath**: Schema markup and analysis
- **All in One SEO**: Meta optimization

## Troubleshooting

### Common Issues

**API Key Not Working**
```
1. Verify API key is correct
2. Check OpenRouter account has credits
3. Test connection in Settings
4. Review error logs in Dashboard
```

**Keywords Not Extracting**
```
1. Ensure pages are published
2. Check page has sufficient content
3. Verify AI model is available
4. Review API usage limits
```

**Generation Fails**
```
1. Check word count is within limits
2. Verify topic is appropriate
3. Try different AI model
4. Check network connectivity
```

**Database Issues**
```
1. Use "Repair Database" in Dashboard
2. Check WordPress database permissions
3. Verify plugin activation
4. Review error logs
```

### Debug Mode

Enable WordPress debug mode for detailed logging:

```php
// wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Development

### Architecture

Blog Writer is built on the Vision Framework with modular architecture:

```
blog-writer/
├── blogwriter.php              # Main plugin file
├── src/                        # Core plugin classes
│   ├── BlogWriter.php         # Main plugin class
│   ├── ApiIntegrationHandler.php # API management
│   ├── KeywordExtractor.php   # Keyword extraction
│   ├── ArticleGenerator.php   # Content generation
│   ├── InternalLinker.php     # Link management
│   ├── ImageGenerator.php     # Image creation
│   ├── UserManager.php        # User management
│   └── BlogWriterDatabaseHandler.php # Database operations
├── framework/                  # Vision Framework
└── assets/                     # CSS, JS, images
```

### Extending the Plugin

```php
// Add custom AI model
class CustomModel extends ArticleGenerator {
    protected function addCustomModel() {
        $this->availableModels['custom-model'] = [
            'name' => 'Custom Model',
            'provider' => 'custom',
            'cost_per_1k' => 0.001,
            'strengths' => ['custom', 'features']
        ];
    }
}

// Hook into generation process
add_action('blog_writer_before_generation', function($topic, $model) {
    // Custom pre-processing
});

add_filter('blog_writer_generated_content', function($content, $topic) {
    // Custom post-processing
    return $content;
}, 10, 2);
```

### Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Support

For support and questions:

- **Documentation**: [GitHub Wiki](https://github.com/lvl3marketing/blog-writer/wiki)
- **Issues**: [GitHub Issues](https://github.com/lvl3marketing/blog-writer/issues)
- **Email**: <EMAIL>
- **Website**: [LVL3 Marketing](https://lvl3marketing.com)

### Commercial Support

Professional support and customization services available:

- Plugin setup and configuration
- Custom AI model integration
- Advanced workflow automation
- Priority support and updates

## License

This plugin is licensed under the GPL v2 or later.

```
Blog Writer - AI-Powered WordPress Content Generation
Copyright (C) 2024 LVL3 Marketing

This program is free software; you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation; either version 2 of the License, or
(at your option) any later version.
```

## Changelog

### Version 1.0.0 (2024-01-XX)
- Initial release
- Multi-AI model support (GPT-4, Claude 3, Gemini Pro)
- Intelligent internal linking system
- SEO optimization features
- AI image generation
- Comprehensive admin interface
- User management system
- API rate limiting and monitoring

---

**Developed by [LVL3 Marketing](https://lvl3marketing.com) - Elevating your content strategy with AI-powered automation.**

## Table of Contents

1. [Overview](#overview)
2. [Features](#features)
3. [Quick Start](#quick-start)
4. [Framework Structure](#framework-structure)
5. [Creating a Plugin](#creating-a-plugin)
6. [UI Components](#ui-components)
7. [Backend Architecture](#backend-architecture)
8. [Customization](#customization)
9. [Best Practices](#best-practices)
10. [API Reference](#api-reference)

## Overview

The Vision Framework is designed to standardize WordPress plugin development by providing:

- **Unified Visual Identity**: Consistent design system with light/dark theme support
- **Responsive Layout**: Mobile-first approach with adaptive design
- **Security First**: Built-in security practices and validation
- **Modular Architecture**: Clean, extensible code structure
- **Developer Friendly**: Easy to use and extend

## Features

### Design & UI System
- ✅ Light and dark theme support with automatic OS detection
- ✅ System UI font stack for consistency
- ✅ Glassmorphism/neumorphism card styles
- ✅ Responsive layout (max-width ~1500px)
- ✅ Smooth transitions with AJAX navigation
- ✅ SVG icon integration
- ✅ Accessible animations and interactions

### Backend Architecture
- ✅ Modular OOP architecture
- ✅ Secure input/output handling
- ✅ Nonce validation for forms
- ✅ Role-based access control
- ✅ Database abstraction with `dbDelta` support
- ✅ Prepared statements for all queries
- ✅ Development/production awareness

### Standard Features
- ✅ Tab-based navigation system
- ✅ Settings management with validation
- ✅ Help documentation system
- ✅ About page with plugin information
- ✅ Logging system with levels
- ✅ Scan/health check functionality
- ✅ Dashboard with status overview

## Quick Start

### 1. Copy the Framework

```bash
# Copy the framework to your plugin directory
cp -r template_vision/ your-plugin-name/
```

### 2. Update Plugin Information

Edit `your-plugin.php` (rename from `sample-plugin.php`):

```php
<?php
/**
 * Plugin Name: Your Plugin Name
 * Description: Your plugin description
 * Version: 1.0.0
 * Author: Your Name
 * Text Domain: your-plugin
 */

// Update constants
define('YOUR_PLUGIN_VERSION', '1.0.0');
define('YOUR_PLUGIN_TEXT_DOMAIN', 'your-plugin');
// ... other constants
```

### 3. Create Your Plugin Class

```php
<?php
namespace YourPlugin;

use VisionFramework\Core\Plugin;

class YourPlugin extends Plugin
{
    protected function initConfig()
    {
        $this->config = [
            'plugin_name' => 'Your Plugin Name',
            'menu_title' => 'Your Plugin',
            'menu_slug' => 'your-plugin',
            'version' => YOUR_PLUGIN_VERSION,
            'text_domain' => YOUR_PLUGIN_TEXT_DOMAIN,
            // ... other config
        ];
    }
    
    // Add your custom functionality
}
```

### 4. Activate and Use

1. Upload to `/wp-content/plugins/`
2. Activate in WordPress admin
3. Find your plugin in the admin menu

## Framework Structure

```
your-plugin/
├── your-plugin.php              # Main plugin file
├── framework/                   # Vision Framework core
│   ├── autoloader.php          # Class autoloading
│   └── src/                    # Framework source
│       └── Core/
│           ├── Plugin.php      # Base plugin class
│           ├── AdminUI.php     # UI management
│           ├── ApiHandler.php  # AJAX handling
│           └── DatabaseHandler.php # Database operations
├── src/                        # Your plugin source
│   ├── YourPlugin.php         # Main plugin class
│   └── Installer.php         # Install/uninstall logic
├── assets/                     # Frontend assets
│   ├── css/
│   │   └── admin.css          # Admin styles
│   ├── js/
│   │   └── admin.js           # Admin JavaScript
│   └── images/                # Plugin images
├── languages/                  # Translation files
└── README.md                  # Documentation
```

## Creating a Plugin

### Step 1: Extend the Base Plugin Class

```php
<?php
namespace MyPlugin;

use VisionFramework\Core\Plugin;

class MyPlugin extends Plugin
{
    protected function initConfig()
    {
        $this->config = [
            'plugin_name' => 'My Awesome Plugin',
            'menu_title' => 'My Plugin',
            'menu_slug' => 'my-plugin',
            'menu_icon' => 'dashicons-star-filled',
            'version' => '1.0.0',
            'text_domain' => 'my-plugin',
            'plugin_url' => plugin_dir_url(__FILE__),
            'plugin_dir' => plugin_dir_path(__FILE__),
        ];
    }
    
    protected function initComponents()
    {
        parent::initComponents();
        
        // Add custom initialization here
        $this->initCustomFeatures();
    }
    
    private function initCustomFeatures()
    {
        // Your custom functionality
    }
}
```

### Step 2: Add Custom Tabs

```php
public function initComponents()
{
    parent::initComponents();
    
    // Add a custom tab
    $this->adminUI->addTab('my_tab', [
        'title' => __('My Custom Tab', $this->config['text_domain']),
        'icon' => 'admin-tools',
        'callback' => [$this, 'renderMyTab']
    ]);
}

public function renderMyTab()
{
    ?>
    <div class="vision-card">
        <h2><?php _e('My Custom Tab', $this->config['text_domain']); ?></h2>
        <p><?php _e('Custom content goes here.', $this->config['text_domain']); ?></p>
    </div>
    <?php
}
```

### Step 3: Add AJAX Endpoints

```php
public function initComponents()
{
    parent::initComponents();
    
    // Register custom AJAX endpoint
    $this->apiHandler->registerEndpoint('my_action', [$this, 'handleMyAction']);
}

public function handleMyAction($data)
{
    // Validate and sanitize input
    $value = sanitize_text_field($data['value'] ?? '');
    
    if (empty($value)) {
        return false; // Error
    }
    
    // Process the action
    // ...
    
    return [
        'message' => __('Action completed successfully!', $this->config['text_domain']),
        'data' => ['processed' => $value]
    ];
}
```

## UI Components

### Cards

```html
<div class="vision-card">
    <h2>Card Title</h2>
    <p>Card content goes here.</p>
</div>
```

### Buttons

```html
<button type="button" class="vision-button vision-button-primary">
    Primary Button
</button>

<button type="button" class="vision-button vision-button-secondary">
    Secondary Button
</button>

<button type="button" class="vision-button vision-button-large" data-action="my_action">
    Large Button with Action
</button>
```

### Form Elements

```html
<form class="vision-settings-form">
    <table class="vision-form-table">
        <tr>
            <th scope="row">
                <label for="my_setting">My Setting</label>
            </th>
            <td>
                <input type="text" id="my_setting" name="my_setting" class="regular-text">
                <p class="description">Setting description.</p>
            </td>
        </tr>
    </table>
    
    <p class="submit">
        <button type="submit" class="vision-button vision-button-primary">
            Save Settings
        </button>
    </p>
</form>
```

### Status Indicators

```html
<div class="vision-status-grid">
    <div class="vision-status-item">
        <span class="vision-status-label">Status</span>
        <span class="vision-status-value vision-status-active">Active</span>
    </div>
</div>
```

## Backend Architecture

### Database Operations

```php
// Get database handler
$db = $this->getDatabaseHandler();

// Log an entry
$db->log('info', 'Something happened', ['context' => 'data']);

// Save settings
$db->saveSetting('my_key', 'my_value', 'string');

// Get settings
$value = $db->getSetting('my_key', 'default_value');

// Save data
$db->saveData('my_type', 'my_key', ['complex' => 'data'], ['meta' => 'info']);

// Get data
$data = $db->getData('my_type', 'my_key');
```

### Security Best Practices

```php
// Always verify nonces in AJAX handlers
if (!wp_verify_nonce($_POST['nonce'], 'my_nonce')) {
    wp_die('Security check failed.');
}

// Check user capabilities
if (!current_user_can('manage_options')) {
    wp_die('Insufficient permissions.');
}

// Sanitize input
$input = sanitize_text_field($_POST['input']);

// Validate data
if (empty($input) || strlen($input) > 100) {
    return false;
}

// Escape output
echo esc_html($output);
```

## Customization

### Custom Themes

Add CSS variables to customize the appearance:

```css
:root {
    --color-primary: #your-color;
    --color-secondary: #your-secondary;
    --font-family: 'Your Font', sans-serif;
}

[data-theme="dark"] {
    --color-primary: #your-dark-color;
}
```

### Custom JavaScript

```javascript
// Listen for framework events
$(document).on('vision:ready', function() {
    // Framework is ready
});

$(document).on('vision:tab-changed', function(e, tabId) {
    // Tab was changed
});

$(document).on('vision:form-success', function(e, form, data) {
    // Form was submitted successfully
});

// Use framework utilities
VisionFramework.showNotice('Custom message', 'success');
VisionFramework.ajaxRequest('my_endpoint', {data: 'value'});
```

## Best Practices

### Plugin Development

1. **Follow WordPress Coding Standards**
2. **Use the framework's security features**
3. **Sanitize all input, escape all output**
4. **Use proper nonce validation**
5. **Check user capabilities**
6. **Use prepared statements for database queries**

### UI/UX Guidelines

1. **Use framework CSS classes for consistency**
2. **Follow the established color scheme**
3. **Ensure responsive design**
4. **Provide loading states for AJAX actions**
5. **Use appropriate feedback messages**

### Performance

1. **Use caching where appropriate**
2. **Optimize database queries**
3. **Minimize AJAX requests**
4. **Use WordPress transients for temporary data**
5. **Clean up unused data regularly**

## API Reference

### Plugin Class Methods

#### Configuration
- `initConfig()` - Set plugin configuration
- `getConfig($key, $default)` - Get configuration value
- `setConfig($key, $value)` - Set configuration value

#### Components
- `getAdminUI()` - Get AdminUI instance
- `getApiHandler()` - Get ApiHandler instance
- `getDatabaseHandler()` - Get DatabaseHandler instance

### AdminUI Class Methods

#### Tab Management
- `addTab($key, $tab)` - Add custom tab
- `removeTab($key)` - Remove tab
- `getCurrentTab()` - Get current tab

#### Rendering
- `renderMainPage()` - Main page renderer
- `enqueueAssets()` - Enqueue CSS/JS

### ApiHandler Class Methods

#### Endpoint Management
- `registerEndpoint($action, $callback)` - Register AJAX endpoint
- `handleRequest()` - Process AJAX request

### DatabaseHandler Class Methods

#### Logging
- `log($level, $message, $context)` - Log entry
- `getLogs($args)` - Get log entries
- `clearLogs($args)` - Clear logs

#### Settings
- `saveSetting($key, $value, $type, $autoload)` - Save setting
- `getSetting($key, $default)` - Get setting
- `deleteSetting($key)` - Delete setting

#### Data Storage
- `saveData($type, $key, $value, $meta, $status)` - Save data
- `getData($type, $key, $args)` - Get data
- `deleteData($type, $key)` - Delete data

## Example Plugin

See the included `SamplePlugin` class for a complete example implementation demonstrating all framework features.

## Support

For questions, issues, or contributions:

1. Check the documentation
2. Review the sample plugin implementation
3. Test with the included framework
4. Follow WordPress coding standards

## License

This framework is released under the GPL v2 or later license, compatible with WordPress.